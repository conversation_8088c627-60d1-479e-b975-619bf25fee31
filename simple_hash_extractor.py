#!/usr/bin/env python3
"""
Simple Registry Hash Extractor
A basic implementation to extract password hashes from Windows registry hives
without requiring the full Impacket installation.
"""

import struct
import sys
import os
from binascii import hexlify, unhexlify

def read_registry_hive(filepath):
    """Read registry hive file"""
    try:
        with open(filepath, 'rb') as f:
            return f.read()
    except Exception as e:
        print(f"[-] Error reading {filepath}: {e}")
        return None

def extract_sam_hashes(sam_data, system_data):
    """Basic SAM hash extraction"""
    print("[+] Attempting to extract SAM hashes...")
    print("[!] This is a simplified extractor. For full functionality, use secretsdump.py")
    
    # This is a very basic implementation
    # For production use, you should use proper tools like secretsdump.py
    
    if not sam_data or not system_data:
        print("[-] Missing SAM or SYSTEM data")
        return
    
    # Check if files are valid registry hives
    if sam_data[:4] != b'regf' or system_data[:4] != b'regf':
        print("[-] Invalid registry hive format")
        return
    
    print("[+] Valid registry hive files detected")
    print("[+] SAM hive size:", len(sam_data), "bytes")
    print("[+] SYSTEM hive size:", len(system_data), "bytes")
    
    print("\n[!] For proper hash extraction, please use one of these tools:")
    print("    1. secretsdump.py (from Impacket)")
    print("    2. samdump2 (Linux tool)")
    print("    3. pwdump (Windows tool)")
    print("    4. Online tools or other forensic suites")

def main():
    if len(sys.argv) < 3:
        print("Simple Registry Hash Extractor")
        print("Usage: python simple_hash_extractor.py <system_hive> <sam_hive> [security_hive]")
        print("\nExample:")
        print("  python simple_hash_extractor.py systemic.txt samantha.txt security.txt")
        print("\nNote: This is a basic tool. For full functionality, use secretsdump.py")
        return
    
    system_file = sys.argv[1]
    sam_file = sys.argv[2]
    security_file = sys.argv[3] if len(sys.argv) > 3 else None
    
    print("Simple Registry Hash Extractor")
    print("=" * 30)
    
    # Check if files exist
    for filepath in [system_file, sam_file]:
        if not os.path.exists(filepath):
            print(f"[-] File not found: {filepath}")
            return
    
    print(f"[+] SYSTEM hive: {system_file}")
    print(f"[+] SAM hive: {sam_file}")
    if security_file and os.path.exists(security_file):
        print(f"[+] SECURITY hive: {security_file}")
    
    # Read hive files
    system_data = read_registry_hive(system_file)
    sam_data = read_registry_hive(sam_file)
    
    if system_data and sam_data:
        extract_sam_hashes(sam_data, system_data)
    
    print("\n" + "=" * 50)
    print("RECOMMENDED TOOLS FOR HASH EXTRACTION:")
    print("=" * 50)
    print("\n1. SECRETSDUMP.PY (Impacket) - Most Popular:")
    print("   We have it available in this directory!")
    print("   Usage: python secretsdump.py -sam samantha.txt -system systemic.txt LOCAL")

    print("\n2. SAMDUMP2 (Linux):")
    print("   Install: apt-get install samdump2")
    print("   Usage: samdump2 systemic.txt samantha.txt")

    print("\n3. ONLINE TOOLS:")
    print("   - Upload hives to online hash extraction services")
    print("   - Use with caution on sensitive systems")

    print("\n4. FORENSIC SUITES:")
    print("   - Autopsy")
    print("   - FTK Imager")
    print("   - X-Ways Forensics")

    print("\n5. ALTERNATIVE TOOLS:")
    print("   - pwdump (Windows)")
    print("   - Ophcrack (GUI)")
    print("   - John the Ripper")
    print("   - Hashcat")

    print(f"\nYour registry files are ready at:")
    print(f"  SYSTEM: {os.path.abspath(system_file)}")
    print(f"  SAM: {os.path.abspath(sam_file)}")
    if security_file and os.path.exists(security_file):
        print(f"  SECURITY: {os.path.abspath(security_file)}")

    print(f"\n🎯 QUICK TEST:")
    print(f"Try running: python secretsdump.py -sam {sam_file} -system {system_file} LOCAL")

if __name__ == "__main__":
    main()
