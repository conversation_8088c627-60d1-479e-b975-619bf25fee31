#!/usr/bin/env python3
"""
Hash Extractor Wrapper
A wrapper script to extract password hashes from Windows registry hives
"""

import sys
import os
import subprocess

def run_secretsdump(sam_file, system_file, security_file=None):
    """Run secretsdump.py with proper error handling"""
    
    print("🔐 Hash Extractor Wrapper")
    print("=" * 30)
    
    # Check if files exist
    for filepath in [sam_file, system_file]:
        if not os.path.exists(filepath):
            print(f"❌ File not found: {filepath}")
            return False
    
    print(f"✅ SAM file: {sam_file}")
    print(f"✅ SYSTEM file: {system_file}")
    if security_file and os.path.exists(security_file):
        print(f"✅ SECURITY file: {security_file}")
    
    print("\n🚀 Attempting hash extraction...")
    
    # Try different methods
    methods = [
        # Method 1: Direct secretsdump.py
        {
            'name': 'secretsdump.py (local)',
            'cmd': ['python', 'secretsdump.py', '-sam', sam_file, '-system', system_file, 'LOCAL']
        },
        # Method 2: Try with security file
        {
            'name': 'secretsdump.py with security',
            'cmd': ['python', 'secretsdump.py', '-sam', sam_file, '-system', system_file, '-security', security_file, 'LOCAL'] if security_file else None
        },
        # Method 3: Try impacket module directly
        {
            'name': 'impacket module',
            'cmd': ['python', '-c', f"from impacket.examples import secretsdump; secretsdump.main()"]
        }
    ]
    
    for method in methods:
        if method['cmd'] is None:
            continue
            
        print(f"\n📋 Trying: {method['name']}")
        try:
            result = subprocess.run(method['cmd'], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and result.stdout:
                print("✅ Success! Hash extraction output:")
                print("-" * 40)
                print(result.stdout)
                print("-" * 40)
                return True
            elif result.stderr:
                print(f"❌ Error: {result.stderr[:200]}...")
            else:
                print("❌ No output or error")
                
        except subprocess.TimeoutExpired:
            print("❌ Timeout - process took too long")
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return False

def show_alternatives():
    """Show alternative methods for hash extraction"""
    print("\n" + "=" * 50)
    print("🛠️  ALTERNATIVE HASH EXTRACTION METHODS")
    print("=" * 50)
    
    print("\n1. 🌐 ONLINE TOOLS (Quick & Easy):")
    print("   • Search for 'SAM hash extractor online'")
    print("   • Upload your registry files")
    print("   • ⚠️  Use only on test systems!")
    
    print("\n2. 🐧 LINUX TOOLS:")
    print("   • samdump2: apt-get install samdump2")
    print("   • Usage: samdump2 system.hive sam.hive")
    
    print("\n3. 🪟 WINDOWS TOOLS:")
    print("   • pwdump7")
    print("   • Ophcrack (GUI)")
    print("   • Cain & Abel")
    
    print("\n4. 🔧 MANUAL REGISTRY PARSING:")
    print("   • Use registry editors to view raw data")
    print("   • Extract encrypted hashes manually")
    
    print("\n5. 💻 COMMAND LINE ALTERNATIVES:")
    print("   • reg query HKLM\\SAM\\SAM\\Domains\\Account\\Users")
    print("   • Use PowerShell registry cmdlets")

def main():
    if len(sys.argv) < 3:
        print("🔐 Hash Extractor Wrapper")
        print("Usage: python extract_hashes.py <system_hive> <sam_hive> [security_hive]")
        print("\nExample:")
        print("  python extract_hashes.py systemic.txt samantha.txt security.txt")
        print("\nThis script will try multiple methods to extract password hashes.")
        return
    
    system_file = sys.argv[1]
    sam_file = sys.argv[2]
    security_file = sys.argv[3] if len(sys.argv) > 3 else None
    
    # Try to extract hashes
    success = run_secretsdump(sam_file, system_file, security_file)
    
    if not success:
        print("\n❌ Hash extraction failed with all methods.")
        show_alternatives()
        
        print(f"\n📁 Your registry files are ready for manual processing:")
        print(f"   • SYSTEM: {os.path.abspath(system_file)}")
        print(f"   • SAM: {os.path.abspath(sam_file)}")
        if security_file and os.path.exists(security_file):
            print(f"   • SECURITY: {os.path.abspath(security_file)}")
    else:
        print("\n🎉 Hash extraction completed successfully!")

if __name__ == "__main__":
    main()
