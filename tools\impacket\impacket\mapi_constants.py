# Impacket - Collection of Python classes for working with network protocols.
#
# Copyright Fortra, LLC and its affiliated companies 
#
# All rights reserved.
#
# This software is provided under a slightly modified version
# of the Apache Software License. See the accompanying LICENSE file
# for more information.
#
# Authors: <AUTHORS>
#
# References:
#   Error codes taken from:
#   - [MS-OXCDATA] http://www.eventid.net/display-eventid-2115-source-MSExchangeDSAccess-eventno-4469-phase-1.htm
#   MAPI properties taken from:
#   - https://gist.github.com/mohemiv/76c265ac92ca026a10b7756899b5f8d5 (MIT)
#

ERROR_MESSAGES = {
        0x80004002: ("MAPI_E_INTERFACE_NO_SUPPORT"),
        0x80004005: ("MAPI_E_CALL_FAILED"),
        0x80040102: ("MAPI_E_NO_SUPPORT"),
        0x80040103: ("MAPI_E_BAD_CHARWIDTH"),
        0x80040105: ("MAPI_E_STRING_TOO_LONG"),
        0x80040106: ("MAPI_E_UNKNOWN_FLAGS"),
        0x80040107: ("MAPI_E_INVALID_ENTRYID"),
        0x80040108: ("MAPI_E_INVALID_OBJECT"),
        0x80040109: ("MAPI_E_OBJECT_CHANGED"),
        0x8004010A: ("MAPI_E_OBJECT_DELETED"),
        0x8004010B: ("MAPI_E_BUSY"),
        0x8004010D: ("MAPI_E_NOT_ENOUGH_DISK"),
        0x8004010E: ("MAPI_E_NOT_ENOUGH_RESOURCES"),
        0x8004010F: ("MAPI_E_NOT_FOUND"),
        0x80040110: ("MAPI_E_VERSION"),
        0x80040111: ("MAPI_E_LOGON_FAILED"),
        0x80040112: ("MAPI_E_SESSION_LIMIT"),
        0x80040113: ("MAPI_E_USER_CANCEL"),
        0x80040114: ("MAPI_E_UNABLE_TO_ABORT"),
        0x80040115: ("MAPI_E_NETWORK_ERROR"),
        0x80040116: ("MAPI_E_DISK_ERROR"),
        0x80040117: ("MAPI_E_TOO_COMPLEX"),
        0x80040118: ("MAPI_E_BAD_COLUMN"),
        0x80040119: ("MAPI_E_EXTENDED_ERROR"),
        0x8004011A: ("MAPI_E_COMPUTED"),
        0x8004011B: ("MAPI_E_CORRUPT_DATA"),
        0x8004011C: ("MAPI_E_UNCONFIGURED"),
        0x8004011D: ("MAPI_E_FAILONEPROVIDER"),
        0x8004011E: ("MAPI_E_UNKNOWN_CPID"),
        0x8004011F: ("MAPI_E_UNKNOWN_LCID"),
        0x80040120: ("MAPI_E_PASSWORD_CHANGE_REQUIRED"),
        0x80040121: ("MAPI_E_PASSWORD_EXPIRED"),
        0x80040122: ("MAPI_E_INVALID_WORKSTATION_ACCOUNT"),
        0x80040123: ("MAPI_E_INVALID_ACCESS_TIME"),
        0x80040124: ("MAPI_E_ACCOUNT_DISABLED"),
        0x80040200: ("MAPI_E_END_OF_SESSION"),
        0x80040201: ("MAPI_E_UNKNOWN_ENTRYID"),
        0x80040202: ("MAPI_E_MISSING_REQUIRED_COLUMN"),
        0x00040203: ("MAPI_W_NO_SERVICE"),
        0x80040301: ("MAPI_E_BAD_VALUE"),
        0x80040302: ("MAPI_E_INVALID_TYPE"),
        0x80040303: ("MAPI_E_TYPE_NO_SUPPORT"),
        0x80040304: ("MAPI_E_UNEXPECTED_TYPE"),
        0x80040305: ("MAPI_E_TOO_BIG"),
        0x80040306: ("MAPI_E_DECLINE_COPY"),
        0x80040307: ("MAPI_E_UNEXPECTED_ID"),
        0x00040380: ("MAPI_W_ERRORS_RETURNED"),
        0x80040400: ("MAPI_E_UNABLE_TO_COMPLETE"),
        0x80040401: ("MAPI_E_TIMEOUT"),
        0x80040402: ("MAPI_E_TABLE_EMPTY"),
        0x80040403: ("MAPI_E_TABLE_TOO_BIG"),
        0x80040405: ("MAPI_E_INVALID_BOOKMARK"),
        0x00040481: ("MAPI_W_POSITION_CHANGED"),
        0x00040482: ("MAPI_W_APPROX_COUNT"),
        0x80040500: ("MAPI_E_WAIT"),
        0x80040501: ("MAPI_E_CANCEL"),
        0x80040502: ("MAPI_E_NOT_ME"),
        0x00040580: ("MAPI_W_CANCEL_MESSAGE"),
        0x80040600: ("MAPI_E_CORRUPT_STORE"),
        0x80040601: ("MAPI_E_NOT_IN_QUEUE"),
        0x80040602: ("MAPI_E_NO_SUPPRESS"),
        0x80040604: ("MAPI_E_COLLISION"),
        0x80040605: ("MAPI_E_NOT_INITIALIZED"),
        0x80040606: ("MAPI_E_NON_STANDARD"),
        0x80040607: ("MAPI_E_NO_RECIPIENTS"),
        0x80040608: ("MAPI_E_SUBMITTED"),
        0x80040609: ("MAPI_E_HAS_FOLDERS"),
        0x8004060A: ("MAPI_E_HAS_MESAGES"),
        0x8004060B: ("MAPI_E_FOLDER_CYCLE"),
        0x8004060D: ("MAPI_E_LOCKID_LIMIT"),
        0x00040680: ("MAPI_W_PARTIAL_COMPLETION"),
        0x80040700: ("MAPI_E_AMBIGUOUS_RECIP"),
        0x80040800: ("SYNC_E_OBJECT_DELETED"),
        0x80040801: ("SYNC_E_IGNORE"),
        0x80040802: ("SYNC_E_CONFLICT"),
        0x80040803: ("SYNC_E_NO_PARENT"),
        0x80040804: ("SYNC_E_CYCLE_DETECTED"),
        0x80040805: ("SYNC_E_UNSYNCHRONIZED"),
        0x00040820: ("SYNC_W_PROGRESS"),
        0x00040821: ("SYNC_W_CLIENT_CHANGE_NEWER"),
        0x80040900: ("MAPI_E_NAMED_PROP_QUOTA_EXCEEDED"),
        0x80040FFF: ("MAPI_E_NOT_IMPLEMENTED"),
        0x80070005: ("MAPI_E_NO_ACCESS"),
        0x8007000E: ("MAPI_E_NOT_ENOUGH_MEMORY"),
        0x80070057: ("MAPI_E_INVALID_PARAMETER"),

        0x80040920: ("LDAP_NO_SUCH_OBJECT"),
        0x80040951: ("LDAP_SERVER_DOWN"),
        0x80040952: ("LDAP_LOCAL_ERROR"),
}

MAPI_E_INTERFACE_NO_SUPPORT               = 0x80004002
MAPI_E_CALL_FAILED                        = 0x80004005
MAPI_E_NO_SUPPORT                         = 0x80040102
MAPI_E_BAD_CHARWIDTH                      = 0x80040103
MAPI_E_STRING_TOO_LONG                    = 0x80040105
MAPI_E_UNKNOWN_FLAGS                      = 0x80040106
MAPI_E_INVALID_ENTRYID                    = 0x80040107
MAPI_E_INVALID_OBJECT                     = 0x80040108
MAPI_E_OBJECT_CHANGED                     = 0x80040109
MAPI_E_OBJECT_DELETED                     = 0x8004010A
MAPI_E_BUSY                               = 0x8004010B
MAPI_E_NOT_ENOUGH_DISK                    = 0x8004010D
MAPI_E_NOT_ENOUGH_RESOURCES               = 0x8004010E
MAPI_E_NOT_FOUND                          = 0x8004010F
MAPI_E_VERSION                            = 0x80040110
MAPI_E_LOGON_FAILED                       = 0x80040111
MAPI_E_SESSION_LIMIT                      = 0x80040112
MAPI_E_USER_CANCEL                        = 0x80040113
MAPI_E_UNABLE_TO_ABORT                    = 0x80040114
MAPI_E_NETWORK_ERROR                      = 0x80040115
MAPI_E_DISK_ERROR                         = 0x80040116
MAPI_E_TOO_COMPLEX                        = 0x80040117
MAPI_E_BAD_COLUMN                         = 0x80040118
MAPI_E_EXTENDED_ERROR                     = 0x80040119
MAPI_E_COMPUTED                           = 0x8004011A
MAPI_E_CORRUPT_DATA                       = 0x8004011B
MAPI_E_UNCONFIGURED                       = 0x8004011C
MAPI_E_FAILONEPROVIDER                    = 0x8004011D
MAPI_E_UNKNOWN_CPID                       = 0x8004011E
MAPI_E_UNKNOWN_LCID                       = 0x8004011F
MAPI_E_PASSWORD_CHANGE_REQUIRED           = 0x80040120
MAPI_E_PASSWORD_EXPIRED                   = 0x80040121
MAPI_E_INVALID_WORKSTATION_ACCOUNT        = 0x80040122
MAPI_E_INVALID_ACCESS_TIME                = 0x80040123
MAPI_E_ACCOUNT_DISABLED                   = 0x80040124
MAPI_E_END_OF_SESSION                     = 0x80040200
MAPI_E_UNKNOWN_ENTRYID                    = 0x80040201
MAPI_E_MISSING_REQUIRED_COLUMN            = 0x80040202
MAPI_W_NO_SERVICE                         = 0x00040203
MAPI_E_BAD_VALUE                          = 0x80040301
MAPI_E_INVALID_TYPE                       = 0x80040302
MAPI_E_TYPE_NO_SUPPORT                    = 0x80040303
MAPI_E_UNEXPECTED_TYPE                    = 0x80040304
MAPI_E_TOO_BIG                            = 0x80040305
MAPI_E_DECLINE_COPY                       = 0x80040306
MAPI_E_UNEXPECTED_ID                      = 0x80040307
MAPI_W_ERRORS_RETURNED                    = 0x00040380
MAPI_E_UNABLE_TO_COMPLETE                 = 0x80040400
MAPI_E_TIMEOUT                            = 0x80040401
MAPI_E_TABLE_EMPTY                        = 0x80040402
MAPI_E_TABLE_TOO_BIG                      = 0x80040403
MAPI_E_INVALID_BOOKMARK                   = 0x80040405
MAPI_W_POSITION_CHANGED                   = 0x00040481
MAPI_W_APPROX_COUNT                       = 0x00040482
MAPI_E_WAIT                               = 0x80040500
MAPI_E_CANCEL                             = 0x80040501
MAPI_E_NOT_ME                             = 0x80040502
MAPI_W_CANCEL_MESSAGE                     = 0x00040580
MAPI_E_CORRUPT_STORE                      = 0x80040600
MAPI_E_NOT_IN_QUEUE                       = 0x80040601
MAPI_E_NO_SUPPRESS                        = 0x80040602
MAPI_E_COLLISION                          = 0x80040604
MAPI_E_NOT_INITIALIZED                    = 0x80040605
MAPI_E_NON_STANDARD                       = 0x80040606
MAPI_E_NO_RECIPIENTS                      = 0x80040607
MAPI_E_SUBMITTED                          = 0x80040608
MAPI_E_HAS_FOLDERS                        = 0x80040609
MAPI_E_HAS_MESAGES                        = 0x8004060A
MAPI_E_FOLDER_CYCLE                       = 0x8004060B
MAPI_E_LOCKID_LIMIT                       = 0x8004060D
MAPI_W_PARTIAL_COMPLETION                 = 0x00040680
MAPI_E_AMBIGUOUS_RECIP                    = 0x80040700
SYNC_E_OBJECT_DELETED                     = 0x80040800
SYNC_E_IGNORE                             = 0x80040801
SYNC_E_CONFLICT                           = 0x80040802
SYNC_E_NO_PARENT                          = 0x80040803
SYNC_E_CYCLE_DETECTED                     = 0x80040804
SYNC_E_UNSYNCHRONIZED                     = 0x80040805
SYNC_W_PROGRESS                           = 0x00040820
SYNC_W_CLIENT_CHANGE_NEWER                = 0x00040821
MAPI_E_NAMED_PROP_QUOTA_EXCEEDED          = 0x80040900
MAPI_E_NOT_IMPLEMENTED                    = 0x80040FFF
MAPI_E_NO_ACCESS                          = 0x80070005
MAPI_E_NOT_ENOUGH_MEMORY                  = 0x8007000E
MAPI_E_INVALID_PARAMETER                  = 0x80070057

LDAP_NO_SUCH_OBJECT                       = 0x80040920
LDAP_SERVER_DOWN                          = 0x80040951
LDAP_LOCAL_ERROR                          = 0x80040952


# PR_DISPLAY_TYPE
# For address book contents tables
DT_MAILUSER         = 0x00000000
DT_DISTLIST         = 0x00000001
DT_FORUM            = 0x00000002
DT_AGENT            = 0x00000003
DT_ORGANIZATION     = 0x00000004
DT_PRIVATE_DISTLIST = 0x00000005
DT_REMOTE_MAILUSER  = 0x00000006
# For address book hierarchy tables
DT_MODIFIABLE       = 0x00010000
DT_GLOBAL           = 0x00020000
DT_LOCAL            = 0x00030000
DT_WAN              = 0x00040000
DT_NOT_SPECIFIC     = 0x00050000
# For folder hierarchy tables *
DT_FOLDER           = 0x01000000
DT_FOLDER_LINK      = 0x02000000
DT_FOLDER_SPECIAL   = 0x04000000

PR_DISPLAY_TYPE_VALUES = {
        0x00000000: "DT_MAILUSER",
        0x00000001: "DT_DISTLIST",
        0x00000002: "DT_FORUM",
        0x00000003: "DT_AGENT",
        0x00000004: "DT_ORGANIZATION",
        0x00000005: "DT_PRIVATE_DISTLIST",
        0x00000006: "DT_REMOTE_MAILUSER",
        0x00010000: "DT_MODIFIABLE",
        0x00020000: "DT_GLOBAL",
        0x00030000: "DT_LOCAL",
        0x00040000: "DT_WAN",
        0x00050000: "DT_NOT_SPECIFIC",
        0x01000000: "DT_FOLDER",
        0x02000000: "DT_FOLDER_LINK",
        0x04000000: "DT_FOLDER_SPECIAL"
}

# PR_OBJECT_TYPE
MAPI_STORE    = 0x1
MAPI_ADDRBOOK = 0x2
MAPI_FOLDER   = 0x3
MAPI_ABCONT   = 0x4
MAPI_MESSAGE  = 0x5
MAPI_MAILUSER = 0x6
MAPI_ATTACH   = 0x7
MAPI_DISTLIST = 0x8
MAPI_PROFSECT = 0x9
MAPI_STATUS   = 0xA
MAPI_SESSION  = 0xB
MAPI_FORMINFO = 0xC

PR_OBJECT_TYPE_VALUES = {
        0x1: "MAPI_STORE",
        0x2: "MAPI_ADDRBOOK",
        0x3: "MAPI_FOLDER",
        0x4: "MAPI_ABCONT",
        0x5: "MAPI_MESSAGE",
        0x6: "MAPI_MAILUSER",
        0x7: "MAPI_ATTACH",
        0x8: "MAPI_DISTLIST",
        0x9: "MAPI_PROFSECT",
        0xA: "MAPI_STATUS",
        0xB: "MAPI_SESSION",
        0xC: "MAPI_FORMINFO"
}

# PR_CONTAINER_FLAGS
AB_RECIPIENTS    = 0x00000001
AB_SUBCONTAINERS = 0x00000002
AB_MODIFIABLE    = 0x00000004
AB_UNMODIFIABLE  = 0x00000008
AB_FIND_ON_OPEN  = 0x00000010
AB_NOT_DEFAULT   = 0x00000020
AB_CONF_ROOMS    = 0x00000200

PR_CONTAINER_FLAGS_VALUES = {
        0x00000001: "AB_RECIPIENTS",
        0x00000002: "AB_SUBCONTAINERS",
        0x00000004: "AB_MODIFIABLE",
        0x00000008: "AB_UNMODIFIABLE",
        0x00000010: "AB_FIND_ON_OPEN",
        0x00000020: "AB_NOT_DEFAULT",
        0x00000200: "AB_CONF_ROOMS"
}

MAPI_PROPERTIES  = {
        # Field_1: (Field_2, Field_3, Field_4, Field_5, Field_6, Field_7, Field_8),
        # 
        # Field_1 is PropertyId
        # Field_2 is PropertyType (unicode when possible)
        # Field_3 is Active Directory LDAP-Display-Name
        # Field_4 is Active Directory CN
        # Field_5:
        #     1 when Is-Member-Of-Partial-Attribute-Set is TRUE
        #     2 when Is-Member-Of-Partial-Attribute-Set is FALSE
        #     3 when Is-Member-Of-Partial-Attribute-Set does not exist
        #     4 when it's not an Active Directory property
        #
        # Field_6 is MS-OXPROPS Canonical Name
        # Field_7 is MS-OXPROPS First Alternate Name (usually the shortest one which starts from PR_)
        # Field_8 is internal Exchange name
        0x0806: (0x101f, "msExchResourceSearchProperties", "ms-Exch-Resource-Search-Properties", 1, None, None, None),
        0x0807: (0x0003, "msExchResourceCapacity", "ms-Exch-Resource-Capacity", 1, "PidTagAddressBookRoomCapacity", "PR_EMS_AB_ROOM_CAPACITY", None),
        0x0808: (0x101f, "msExchResourceMetaData", "ms-Exch-Resource-Meta-Data", 1, None, None, None),
        0x0809: (0x001f, "msExchResourceDisplay", "ms-Exch-Resource-Display", 1, "PidTagAddressBookRoomDescription", "PR_EMS_AB_ROOM_DESCRIPTION", None),
        0x3004: (0x001f, "info", "Comment", 1, "PidTagComment", "PR_COMMENT", "Comment"),
        0x3007: (0x0040, "whenCreated", "When-Created", 1, "PidTagCreationTime", "PR_CREATION_TIME", "CreationTime"),
        0x3008: (0x0040, "whenChanged", "When-Changed", 1, "PidTagLastModificationTime", "PR_LAST_MODIFICATION_TIME", "LastModificationTime"),
        0x3905: (0x0003, "msExchRecipientDisplayType", "ms-Exch-Recipient-Display-Type", 1, "PidTagDisplayTypeEx", "PR_DISPLAY_TYPE_EX", "DisplayTypeEx"),
        0x39fe: (0x001f, "mail", "E-mail-Addresses", 1, "PidTagSmtpAddress", "PR_SMTP_ADDRESS", "SmtpAddress"),
        0x39ff: (0x001f, "displayNamePrintable", "Display-Name-Printable", 1, "PidTagAddressBookDisplayNamePrintable", "PR_EMS_AB_DISPLAY_NAME_PRINTABLE", "SimpleDisplayName"),
        0x3a00: (0x001f, "mailNickname", "ms-Exch-Mail-Nickname", 1, "PidTagAccount", "PR_ACCOUNT", "Account"),
        0x3a06: (0x001f, "givenName", "Given-Name", 1, "PidTagGivenName", "PR_GIVEN_NAME", "GivenName"),
        0x3a08: (0x001f, "telephoneNumber", "Telephone-Number", 1, "PidTagBusinessTelephoneNumber", "PR_BUSINESS_TELEPHONE_NUMBER", "BusinessTelephoneNumber"),
        0x3a09: (0x001f, "homePhone", "Phone-Home-Primary", 1, "PidTagHomeTelephoneNumber", "PR_HOME_TELEPHONE_NUMBER", "HomeTelephoneNumber"),
        0x3a0a: (0x001f, "initials", "Initials", 1, "PidTagInitials", "PR_INITIALS", "Initials"),
        0x3a0f: (0x001f, "cn", "Common-Name", 1, "PidTagMessageHandlingSystemCommonName", "PR_MHS_COMMON_NAME", "MhsCommonName"),
        0x3a11: (0x001f, "sn", "Surname", 1, "PidTagSurname", "PR_SURNAME", "Surname"),
        0x3a16: (0x001f, "company", "Company", 1, "PidTagCompanyName", "PR_COMPANY_NAME", "CompanyName"),
        0x3a17: (0x001f, "title", "Title", 1, "PidTagTitle", "PR_TITLE", "Title"),
        0x3a18: (0x001f, "department", "Department", 1, "PidTagDepartmentName", "PR_DEPARTMENT_NAME", "DepartmentName"),
        0x3a19: (0x001f, "physicalDeliveryOfficeName", "Physical-Delivery-Office-Name", 1, "PidTagOfficeLocation", "PR_OFFICE_LOCATION", "OfficeLocation"),
        0x3a1b: (0x101f, "otherTelephone", "Phone-Office-Other", 1, "PidTagBusiness2TelephoneNumbers", "PR_BUSINESS2_TELEPHONE_NUMBER_A_MV", "Business2TelephoneNumber"),
        0x3a1c: (0x001f, "mobile", "Phone-Mobile-Primary", 1, "PidTagMobileTelephoneNumber", "PR_MOBILE_TELEPHONE_NUMBER", "MobileTelephoneNumber"),
        0x3a21: (0x001f, "pager", "Phone-Pager-Primary", 1, "PidTagPagerTelephoneNumber", "PR_PAGER_TELEPHONE_NUMBER", "PagerTelephoneNumber"),
        0x3a22: (0x0102, "userCert", "User-Cert", 1, "PidTagUserCertificate", "PR_USER_CERTIFICATE", "UserCertificate"),
        0x3a23: (0x001f, "facsimileTelephoneNumber", "Facsimile-Telephone-Number", 1, "PidTagPrimaryFaxNumber", "PR_PRIMARY_FAX_NUMBER", "PrimaryFaxNumber"),
        0x3a26: (0x001f, "co", "Text-Country", 1, "PidTagCountry", "PR_COUNTRY", "Country"),
        0x3a27: (0x001f, "l", "Locality-Name", 1, "PidTagLocality", "PR_LOCALITY", "Locality"),
        0x3a28: (0x001f, "st", "State-Or-Province-Name", 1, "PidTagStateOrProvince", "PR_STATE_OR_PROVINCE", "StateOrProvince"),
        0x3a29: (0x001f, "streetAddress", "Address", 1, "PidTagStreetAddress", "PR_STREET_ADDRESS", "StreetAddress"),
        0x3a2a: (0x001f, "postalCode", "Postal-Code", 1, "PidTagPostalCode", "PR_POSTAL_CODE", "PostalCode"),
        0x3a2b: (0x101f, "postOfficeBox", "Post-Office-Box", 1, "PidTagPostOfficeBox", "PR_POST_OFFICE_BOX", "PostOfficeBox"),
        0x3a2c: (0x1102, "telexNumber", "Telex-Number", 3, "PidTagTelexNumber", "PR_TELEX_NUMBER", "TelexNumber"),
        0x3a2e: (0x001f, "telephoneAssistant", "ms-Exch-Telephone-Assistant", 1, "PidTagAssistantTelephoneNumber", "PR_ASSISTANT_TELEPHONE_NUMBER", "AssistantTelephoneNumber"),
        0x3a2f: (0x101f, "otherHomePhone", "Phone-Home-Other", 1, "PidTagHome2TelephoneNumbers", "PR_HOME2_TELEPHONE_NUMBER_A_MV", "Home2TelephoneNumber"),
        0x3a30: (0x001f, "msExchAssistantName", "ms-Exch-Assistant-Name", 1, "PidTagAssistant", "PR_ASSISTANT", "Assistant"),
        0x3a40: (0x000b, "mAPIRecipient", "ms-Exch-MAPI-Recipient", 1, "PidTagSendRichInfo", "PR_SEND_RICH_INFO", "SendRichInfo"),
        0x3a5d: (0x001f, "homePostalAddress", "Address-Home", 1, "PidTagHomeAddressStreet", "PR_HOME_ADDRESS_STREET", "HomeAddressStreet"),
        0x3a70: (0x1102, "userSMIMECertificate", "User-SMIME-Certificate", 1, "PidTagUserX509Certificate", "PR_USER_X509_CERTIFICATE", "UserSMimeCertificate"),
        0x3a71: (0x0003, "internetEncoding", "ms-Exch-Internet-Encoding", 1, "PidTagSendInternetEncoding", "PR_SEND_INTERNET_ENCODING", "SendInternetEncoding"),
        0x8003: (0x1102, "cACertificate", "CA-Certificate", 1, None, None, None),
        0x8004: (0x001f, "folderPathname", "ms-Exch-Folder-Pathname", 1, "PidTagAddressBookFolderPathname", "PR_EMS_AB_FOLDER_PATHNAME", None),
        0x8005: (0x000d, "manager", "Manager", 1, "PidTagAddressBookManagerDistinguishedName", "PR_EMS_AB_MANAGER_T", None),
        0x8006: (0x000d, "homeMDB", "ms-Exch-Home-MDB", 1, "PidTagAddressBookHomeMessageDatabase", "PR_EMS_AB_HOME_MDB", "HomeMdb"),
        0x8007: (0x001f, "homeMTA", "ms-Exch-Home-MTA", 1, "PidLidContactItemData", "dispidContactItemData", None),
        0x8008: (0x000d, "memberOf", "Is-Member-Of-DL", 3, "PidTagAddressBookIsMemberOfDistributionList", "PR_EMS_AB_IS_MEMBER_OF_DL", "MemberOf"),
        0x8009: (0x000d, "member", "Member", 1, "PidTagAddressBookMember", "PR_EMS_AB_MEMBER", "Members"),
        0x800a: (0x001f, "autoReplyMessage", "ms-Exch-AutoReply-Message", 2, None, None, None),
        0x800b: (0x000b, "autoReply", "ms-Exch-AutoReply", 1, None, None, None),
        0x800c: (0x000d, "managedBy", "Managed-By", 1, "PidTagAddressBookOwner", "PR_EMS_AB_OWNER_O", "ManagedBy"),
        0x800d: (0x001f, "kMServer", "ms-Exch-KM-Server", 2, None, None, None),
        0x800e: (0x000d, "directReports", "Reports", 3, "PidTagAddressBookReports", "PR_EMS_AB_REPORTS", None),
        0x800f: (0x101f, "proxyAddresses", "Proxy-Addresses", 1, "PidTagAddressBookProxyAddresses", "PR_EMS_AB_PROXY_ADDRESSES", "ProxyAddresses"),
        0x8010: (0x0102, "helpData32", "Help-Data32", 3, "PidLidDepartment", "dispidDepartment", "TemplateInfoHelpFileContents"),
        0x8011: (0x001f, "targetAddress", "ms-Exch-Target-Address", 1, "PidTagAddressBookTargetAddress", "PR_EMS_AB_TARGET_ADDRESS", None),
        0x8014: (0x000d, "homeMDBBL", "ms-Exch-Home-MDB-BL", 1, None, None, None),
        0x8015: (0x000d, "publicDelegates", "ms-Exch-Public-Delegates", 1, "PidTagAddressBookPublicDelegates", "PR_EMS_AB_PUBLIC_DELEGATES", "GrantSendOnBehalfTo"),
        0x8016: (0x0102, "certificateRevocationList", "Certificate-Revocation-List", 3, None, None, None),
        0x8017: (0x0102, "addressEntryDisplayTable", "Address-Entry-Display-Table", 3, None, None, "TemplateInfoTemplate"),
        0x8018: (0x0102, "addressSyntax", "Address-Syntax", 3, None, None, "TemplateInfoScript"),
        0x8023: (0x0102, "businessRoles", "ms-Exch-Business-Roles", 2, "PidLidContactCharacterSet", "dispidContactCharSet", None),
        0x8024: (0x000d, "managedObjects", "Managed-Objects", 3, "PidTagAddressBookOwnerBackLink", "PR_EMS_AB_OWNER_BL_O", None),
        0x8025: (0x1102, "crossCertificatePair", "Cross-Certificate-Pair", 3, "PidLidAutoLog", "dispidAutoLog", None),
        0x8026: (0x1102, "authorityRevocationList", "Authority-Revocation-List", 3, "PidLidFileUnderList", "dispidFileUnderList", None),
        0x8027: (0x0102, "objectSid", "Object-Sid", 1, None, None, None),
        0x8028: (0x0040, "expirationTime", "ms-Exch-Expiration-Time", 2, "PidLidAddressBookProviderEmailList", "dispidABPEmailList", None),
        0x8029: (0x0003, "uSNChanged", "USN-Changed", 1, "PidLidAddressBookProviderArrayType", "dispidABPArrayType", None),
        0x802d: (0x001f, "extensionAttribute1", "ms-Exch-Extension-Attribute-1", 1, "PidTagAddressBookExtensionAttribute1", "PR_EMS_AB_EXTENSION_ATTRIBUTE_1", None),
        0x802e: (0x001f, "extensionAttribute2", "ms-Exch-Extension-Attribute-2", 1, "PidTagAddressBookExtensionAttribute2", "PR_EMS_AB_EXTENSION_ATTRIBUTE_2", None),
        0x802f: (0x001f, "extensionAttribute3", "ms-Exch-Extension-Attribute-3", 1, "PidTagAddressBookExtensionAttribute3", "PR_EMS_AB_EXTENSION_ATTRIBUTE_3", None),
        0x8030: (0x001f, "extensionAttribute4", "ms-Exch-Extension-Attribute-4", 1, "PidTagAddressBookExtensionAttribute4", "PR_EMS_AB_EXTENSION_ATTRIBUTE_4", None),
        0x8031: (0x001f, "extensionAttribute5", "ms-Exch-Extension-Attribute-5", 1, "PidTagAddressBookExtensionAttribute5", "PR_EMS_AB_EXTENSION_ATTRIBUTE_5", None),
        0x8032: (0x001f, "extensionAttribute6", "ms-Exch-Extension-Attribute-6", 1, "PidTagAddressBookExtensionAttribute6", "PR_EMS_AB_EXTENSION_ATTRIBUTE_6", None),
        0x8033: (0x001f, "extensionAttribute7", "ms-Exch-Extension-Attribute-7", 1, "PidTagAddressBookExtensionAttribute7", "PR_EMS_AB_EXTENSION_ATTRIBUTE_7", None),
        0x8034: (0x001f, "extensionAttribute8", "ms-Exch-Extension-Attribute-8", 1, "PidTagAddressBookExtensionAttribute8", "PR_EMS_AB_EXTENSION_ATTRIBUTE_8", None),
        0x8035: (0x001f, "extensionAttribute9", "ms-Exch-Extension-Attribute-9", 1, "PidTagAddressBookExtensionAttribute9", "PR_EMS_AB_EXTENSION_ATTRIBUTE_9", None),
        0x8036: (0x001f, "extensionAttribute10", "ms-Exch-Extension-Attribute-10", 1, "PidTagAddressBookExtensionAttribute10", "PR_EMS_AB_EXTENSION_ATTRIBUTE_10", None),
        0x8037: (0x1102, "securityProtocol", "ms-Exch-Security-Protocol", 1, None, None, None),
        0x8038: (0x101f, "pFContacts", "ms-Exch-PF-Contacts", 1, None, None, "PfContacts"),
        0x803a: (0x0102, "helpData16", "Help-Data16", 3, None, None, None),
        0x803b: (0x001f, "helpFileName", "Help-File-Name", 3, None, None, "TemplateInfoHelpFileName"),
        0x803c: (0x001f, "distinguishedName", "Obj-Dist-Name", 1, "PidTagAddressBookObjectDistinguishedName", "PR_EMS_AB_OBJ_DIST_NAME", "ObjectDistinguishedName"),
        0x803d: (0x001f, "encryptAlgSelectedOther", "ms-Exch-Encrypt-Alg-Selected-Other", 2, None, None, None),
        0x8040: (0x101f, "encryptAlgListNA", "ms-Exch-Encrypt-Alg-List-NA", 2, "PidLidBusinessCardDisplayDefinition", "dispidBCDisplayDefinition", None),
        0x8041: (0x101f, "encryptAlgListOther", "ms-Exch-Encrypt-Alg-List-Other", 2, "PidLidBusinessCardCardPicture", "dispidBCCardPicture", None),
        0x8042: (0x001f, "importedFrom", "ms-Exch-Imported-From", 1, None, None, None),
        0x8043: (0x001f, "encryptAlgSelectedNA", "ms-Exch-Encrypt-Alg-Selected-NA", 2, None, None, None),
        0x8045: (0x0102, "activationSchedule", "ms-Exch-Activation-Schedule", 2, "PidLidWorkAddressStreet", "dispidWorkAddressStreet", None),
        0x8046: (0x0003, "activationStyle", "ms-Exch-Activation-Style", 2, "PidLidWorkAddressCity", "dispidWorkAddressCity", None),
        0x8047: (0x0102, "addressEntryDisplayTableMSDOS", "Address-Entry-Display-Table-MSDOS", 3, "PidLidWorkAddressState", "dispidWorkAddressState", None),
        0x8048: (0x001f, "addressType", "Address-Type", 3, "PidLidWorkAddressPostalCode", "dispidWorkAddressPostalCode", "TemplateInfoEmailType"),
        0x8049: (0x001f, "aDMD", "ms-Exch-ADMD", 2, "PidLidWorkAddressCountry", "dispidWorkAddressCountry", None),
        0x804a: (0x001f, "adminDescription", "Admin-Description", 3, "PidLidWorkAddressPostOfficeBox", "dispidWorkAddressPostOfficeBox", None),
        0x804b: (0x001f, "adminDisplayName", "Admin-Display-Name", 1, None, None, None),
        0x804c: (0x001f, "adminExtensionDLL", "ms-Exch-Admin-Extension-DLL", 2, "PidLidDistributionListChecksum", "dispidDLChecksum", None),
        0x804e: (0x000d, "altRecipient", "ms-Exch-Alt-Recipient", 1, "PidLidAnniversaryEventEntryId", "dispidAnniversaryEventEID", None),
        0x804f: (0x000d, "altRecipientBL", "ms-Exch-Alt-Recipient-BL", 2, "PidLidContactUserField1", "dispidContactUserField1", None),
        0x8051: (0x000d, "assocRemoteDXA", "ms-Exch-Assoc-Remote-DXA", 2, "PidLidContactUserField3", "dispidContactUserField3", None),
        0x8052: (0x0003, "associationLifetime", "ms-Exch-Association-Lifetime", 2, "PidLidContactUserField4", "dispidContactUserField4", None),
        0x8053: (0x000d, "authOrigBL", "ms-Exch-Auth-Orig-BL", 1, "PidLidDistributionListName", "dispidDLName", None),
        0x8054: (0x001f, "authorizedDomain", "ms-Exch-Authorized-Domain", 2, "PidLidDistributionListOneOffMembers", "dispidDLOneOffMembers", None),
        0x8055: (0x0102, "authorizedPassword", "ms-Exch-Authorized-Password", 2, "PidLidDistributionListMembers", "dispidDLMembers", None),
        0x8056: (0x001f, "authorizedUser", "ms-Exch-Authorized-User", 2, None, None, None),
        0x8057: (0x101f, "businessCategory", "Business-Category", 3, None, None, None),
        0x8060: (0x000b, "canPreserveDNs", "ms-Exch-Can-Preserve-DNs", 2, None, None, None),
        0x8061: (0x0003, "clockAlertOffset", "ms-Exch-Clock-Alert-Offset", 2, None, None, None),
        0x8062: (0x000b, "clockAlertRepair", "ms-Exch-Clock-Alert-Repair", 2, "PidLidInstantMessagingAddress", "dispidInstMsg", None),
        0x8063: (0x0003, "clockWarningOffset", "ms-Exch-Clock-Warning-Offset", 2, None, None, None),
        0x8064: (0x000b, "clockWarningRepair", "ms-Exch-Clock-Warning-Repair", 2, "PidLidDistributionListStream", "dispidDLStream", None),
        0x8065: (0x001f, "computerName", "ms-Exch-Computer-Name", 2, None, None, None),
        0x8066: (0x101f, "connectedDomains", "ms-Exch-Connected-Domains", 2, None, None, None),
        0x8067: (0x0003, "containerInfo", "ms-Exch-Container-Info", 2, None, None, None),
        0x8068: (0x0003, "cost", "Cost", 3, None, None, None),
        0x8069: (0x001f, "c", "Country-Name", 1, None, None, None),
        0x806a: (0x0003, "delivContLength", "ms-Exch-Deliv-Cont-Length", 1, "PidTagAddressBookDeliveryContentLength", "PR_EMS_AB_DELIV_CONT_LENGTH", None),
        0x806b: (0x1102, "delivEITs", "ms-Exch-Deliv-EITs", 2, None, None, None),
        0x806c: (0x1102, "delivExtContTypes", "ms-Exch-Deliv-Ext-Cont-Types", 1, None, None, None),
        0x806d: (0x000b, "deliverAndRedirect", "ms-Exch-Deliver-And-Redirect", 1, None, None, None),
        0x806e: (0x0003, "deliveryMechanism", "ms-Exch-Delivery-Mechanism", 1, None, None, None),
        0x806f: (0x101f, "description", "Description", 1, None, None, None),
        0x8070: (0x101f, "destinationIndicator", "Destination-Indicator", 3, None, None, None),
        0x8071: (0x001f, "diagnosticRegKey", "ms-Exch-Diagnostic-Reg-Key", 2, None, None, None),
        0x8072: (0x000d, "dLMemRejectPermsBL", "ms-Exch-DL-Mem-Reject-Perms-BL", 1, None, None, None),
        0x8073: (0x000d, "dLMemSubmitPermsBL", "ms-Exch-DL-Mem-Submit-Perms-BL", 1, "PidTagAddressBookDistributionListMemberSubmitAccepted", "PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL_O", None),
        0x8074: (0x1102, "dLMemberRule", "ms-Exch-DL-Member-Rule", 1, None, None, None),
        0x8075: (0x001f, "domainDefAltRecip", "ms-Exch-Domain-Def-Alt-Recip", 2, None, None, None),
        0x8076: (0x001f, "domainName", "ms-Exch-Domain-Name", 2, None, None, None),
        0x8077: (0x0102, "dSASignature", "DSA-Signature", 3, None, None, None),
        0x8078: (0x000b, "dXAAdminCopy", "ms-Exch-DXA-Admin-Copy", 2, None, None, None),
        0x8079: (0x000b, "dXAAdminForward", "ms-Exch-DXA-Admin-Forward", 2, None, None, None),
        0x807a: (0x0003, "dXAAdminUpdate", "ms-Exch-DXA-Admin-Update", 2, None, None, None),
        0x807b: (0x000b, "dXAAppendReqCN", "ms-Exch-DXA-Append-ReqCN", 2, None, None, None),
        0x807c: (0x101f, "dXAConfContainerList", "ms-Exch-DXA-Conf-Container-List", 2, None, None, None),
        0x807d: (0x0040, "dXAConfReqTime", "ms-Exch-DXA-Conf-Req-Time", 2, None, None, None),
        0x807e: (0x001f, "dXAConfSeq", "ms-Exch-DXA-Conf-Seq", 2, None, None, None),
        0x807f: (0x0003, "dXAConfSeqUSN", "ms-Exch-DXA-Conf-Seq-USN", 2, None, None, None),
        0x8080: (0x0003, "dXAExchangeOptions", "ms-Exch-DXA-Exchange-Options", 2, "PidLidEmail1DisplayName", "dispidEmail1DisplayName", None),
        0x8081: (0x000b, "dXAExportNow", "ms-Exch-DXA-Export-Now", 2, None, None, None),
        0x8082: (0x0003, "deletedItemFlags", "ms-Exch-Deleted-Item-Flags", 1, "PidLidEmail1AddressType", "dispidEmail1AddrType", None),
        0x8083: (0x001f, "dXAImpSeq", "ms-Exch-DXA-Imp-Seq", 2, "PidLidEmail1EmailAddress", "dispidEmail1EmailAddress", None),
        0x8084: (0x0040, "dXAImpSeqTime", "ms-Exch-DXA-Imp-Seq-Time", 2, "PidLidEmail1OriginalDisplayName", "dispidEmail1OriginalDisplayName", None),
        0x8085: (0x0003, "dXAImpSeqUSN", "ms-Exch-DXA-Imp-Seq-USN", 2, "PidLidEmail1OriginalEntryId", "dispidEmail1OriginalEntryID", None),
        0x8086: (0x000b, "dXAImportNow", "ms-Exch-DXA-Import-Now", 2, None, None, None),
        0x8087: (0x101f, "dXAInTemplateMap", "ms-Exch-DXA-In-Template-Map", 2, None, None, None),
        0x8088: (0x001f, "dXALocalAdmin", "ms-Exch-DXA-Local-Admin", 2, None, None, None),
        0x808a: (0x001f, "dXANativeAddressType", "ms-Exch-DXA-Native-Address-Type", 2, None, None, None),
        0x808b: (0x101f, "dXAOutTemplateMap", "ms-Exch-DXA-Out-Template-Map", 2, None, None, None),
        0x808c: (0x001f, "dXAPassword", "ms-Exch-DXA-Password", 2, None, None, None),
        0x808d: (0x0003, "dXAPrevExchangeOptions", "ms-Exch-DXA-Prev-Exchange-Options", 2, None, None, None),
        0x808e: (0x000b, "dXAPrevExportNativeOnly", "ms-Exch-DXA-Prev-Export-Native-Only", 2, None, None, None),
        0x808f: (0x0003, "dXAPrevInExchangeSensitivity", "ms-Exch-DXA-Prev-In-Exchange-Sensitivity", 2, None, None, None),
        0x8090: (0x001f, "dXAPrevRemoteEntries", "ms-Exch-DXA-Prev-Remote-Entries", 2, "PidLidEmail2DisplayName", "dispidEmail2DisplayName", None),
        0x8091: (0x0003, "dXAPrevReplicationSensitivity", "ms-Exch-DXA-Prev-Replication-Sensitivity", 2, None, None, None),
        0x8092: (0x0003, "dXAPrevTemplateOptions", "ms-Exch-DXA-Prev-Template-Options", 2, "PidLidEmail2AddressType", "dispidEmail2AddrType", None),
        0x8093: (0x0003, "dXAPrevTypes", "ms-Exch-DXA-Prev-Types", 2, "PidLidEmail2EmailAddress", "dispidEmail2EmailAddress", None),
        0x8094: (0x001f, "dXARecipientCP", "ms-Exch-DXA-Recipient-CP", 2, "PidLidEmail2OriginalDisplayName", "dispidEmail2OriginalDisplayName", None),
        0x8095: (0x001f, "dXARemoteClient", "ms-Exch-DXA-Remote-Client", 2, "PidLidEmail2OriginalEntryId", "dispidEmail2OriginalEntryID", None),
        0x8096: (0x001f, "dXAReqSeq", "ms-Exch-DXA-Req-Seq", 2, None, None, None),
        0x8097: (0x0040, "dXAReqSeqTime", "ms-Exch-DXA-Req-Seq-Time", 2, None, None, None),
        0x8098: (0x0003, "dXAReqSeqUSN", "ms-Exch-DXA-Req-Seq-USN", 2, None, None, None),
        0x8099: (0x001f, "dXAReqName", "ms-Exch-DXA-ReqName", 2, None, None, None),
        0x809a: (0x001f, "dXASvrSeq", "ms-Exch-DXA-Svr-Seq", 2, None, None, None),
        0x809b: (0x0040, "dXASvrSeqTime", "ms-Exch-DXA-Svr-Seq-Time", 2, None, None, None),
        0x809c: (0x0003, "dXASvrSeqUSN", "ms-Exch-DXA-Svr-Seq-USN", 2, None, None, None),
        0x809d: (0x0003, "messageSizeLimit", "ms-Exch-Message-Size-Limit", 2, None, None, None),
        0x809e: (0x0003, "dXATemplateOptions", "ms-Exch-DXA-Template-Options", 2, None, None, None),
        0x809f: (0x0040, "dXATemplateTimeStamp", "ms-Exch-DXA-Template-TimeStamp", 2, None, None, None),
        0x80a0: (0x0003, "dXATypes", "ms-Exch-DXA-Types", 2, "PidLidEmail3DisplayName", "dispidEmail3DisplayName", None),
        0x80a1: (0x101f, "dXAUnConfContainerList", "ms-Exch-DXA-UnConf-Container-List", 2, None, None, None),
        0x80a2: (0x0003, "encapsulationMethod", "ms-Exch-Encapsulation-Method", 2, "PidLidEmail3AddressType", "dispidEmail3AddrType", None),
        0x80a3: (0x000b, "encrypt", "ms-Exch-Encrypt", 2, "PidLidEmail3EmailAddress", "dispidEmail3EmailAddress", None),
        0x80a4: (0x000b, "expandDLsLocally", "ms-Exch-Expand-DLs-Locally", 2, "PidLidEmail3OriginalDisplayName", "dispidEmail3OriginalDisplayName", None),
        0x80a5: (0x101f, "exportContainers", "ms-Exch-Export-Containers", 2, "PidLidEmail3OriginalEntryId", "dispidEmail3OriginalEntryID", None),
        0x80a6: (0x000b, "exportCustomRecipients", "ms-Exch-Export-Custom-Recipients", 2, None, None, None),
        0x80a7: (0x000b, "extendedCharsAllowed", "Extended-Chars-Allowed", 3, None, None, None),
        0x80a8: (0x1102, "extensionData", "ms-Exch-Extension-Data", 2, None, None, None),
        0x80a9: (0x101f, "extensionName", "Extension-Name", 3, None, None, None),
        0x80ac: (0x0102, "fileVersion", "ms-Exch-File-Version", 2, None, None, None),
        0x80ad: (0x000b, "filterLocalAddresses", "ms-Exch-Filter-Local-Addresses", 2, None, None, None),
        0x80af: (0x0003, "garbageCollPeriod", "Garbage-Coll-Period", 1, None, None, None),
        0x80b0: (0x001f, "gatewayLocalCred", "ms-Exch-Gateway-Local-Cred", 2, None, None, None),
        0x80b1: (0x001f, "gatewayLocalDesig", "ms-Exch-Gateway-Local-Desig", 2, None, None, None),
        0x80b2: (0x101f, "gatewayProxy", "ms-Exch-Gateway-Proxy", 2, "PidLidFax1AddressType", "dispidFax1AddrType", None),
        0x80b3: (0x0102, "gatewayRoutingTree", "ms-Exch-Gateway-Routing-Tree", 2, "PidLidFax1EmailAddress", "dispidFax1EmailAddress", None),
        0x80b4: (0x0040, "gWARTLastModified", "ms-Exch-GWART-Last-Modified", 2, "PidLidFax1OriginalDisplayName", "dispidFax1OriginalDisplayName", None),
        0x80b5: (0x000d, "hasPartialReplicaNCs", "Has-Partial-Replica-NCs", 3, "PidLidFax1OriginalEntryId", "dispidFax1OriginalEntryID", None),
        0x80b6: (0x000d, "hasMasterNCs", "Has-Master-NCs", 3, None, None, None),
        0x80b7: (0x0003, "heuristics", "ms-Exch-Heuristics", 1, None, None, None),
        0x80b8: (0x000b, "hideDLMembership", "ms-Exch-Hide-DL-Membership", 1, None, None, "HideDLMembership"),
        0x80ba: (0x001f, "importContainer", "ms-Exch-Import-Container", 2, None, None, None),
        0x80bc: (0x101f, "inboundSites", "ms-Exch-Inbound-Sites", 2, None, None, None),
        0x80bd: (0x0003, "instanceType", "Instance-Type", 1, None, None, None),
        0x80be: (0x101f, "internationalISDNNumber", "International-ISDN-Number", 3, None, None, None),
        0x80bf: (0x0102, "invocationId", "Invocation-Id", 3, None, None, None),
        0x80c0: (0x000b, "isDeleted", "Is-Deleted", 1, None, None, None),
        0x80c1: (0x000b, "isSingleValued", "Is-Single-Valued", 3, None, None, None),
        0x80c2: (0x1102, "kCCStatus", "ms-Exch-KCC-Status", 2, "PidLidFax2AddressType", "dispidFax2AddrType", None),
        0x80c3: (0x101f, "knowledgeInformation", "Knowledge-Information", 3, "PidLidFax2EmailAddress", "dispidFax2EmailAddress", None),
        0x80c4: (0x0003, "lineWrap", "ms-Exch-Line-Wrap", 2, "PidLidFax2OriginalDisplayName", "dispidFax2OriginalDisplayName", None),
        0x80c5: (0x0003, "linkID", "Link-ID", 3, "PidLidFax2OriginalEntryId", "dispidFax2OriginalEntryID", None),
        0x80c6: (0x001f, "localBridgeHead", "ms-Exch-Local-Bridge-Head", 2, None, None, None),
        0x80c7: (0x001f, "localBridgeHeadAddress", "ms-Exch-Local-Bridge-Head-Address", 2, None, None, None),
        0x80c8: (0x000b, "localInitialTurn", "ms-Exch-Local-Initial-Turn", 2, None, None, None),
        0x80ca: (0x001f, "logFilename", "ms-Exch-Log-Filename", 2, None, None, None),
        0x80cb: (0x0003, "logRolloverInterval", "ms-Exch-Log-Rollover-Interval", 2, None, None, None),
        0x80ce: (0x0003, "mAPIID", "MAPI-ID", 3, None, None, None),
        0x80cf: (0x0003, "mDBBackoffInterval", "ms-Exch-MDB-Backoff-Interval", 2, None, None, None),
        0x80d0: (0x0003, "mDBMsgTimeOutPeriod", "ms-Exch-MDB-Msg-Time-Out-Period", 2, None, None, None),
        0x80d1: (0x0003, "mDBOverQuotaLimit", "ms-Exch-MDB-Over-Quota-Limit", 1, None, None, None),
        0x80d2: (0x0003, "mDBStorageQuota", "ms-Exch-MDB-Storage-Quota", 1, "PidLidFax3AddressType", "dispidFax3AddrType", None),
        0x80d3: (0x0003, "mDBUnreadLimit", "ms-Exch-MDB-Unread-Limit", 2, "PidLidFax3EmailAddress", "dispidFax3EmailAddress", None),
        0x80d4: (0x000b, "mDBUseDefaults", "ms-Exch-MDB-Use-Defaults", 1, "PidLidFax3OriginalDisplayName", "dispidFax3OriginalDisplayName", None),
        0x80d5: (0x000b, "messageTrackingEnabled", "ms-Exch-Message-Tracking-Enabled", 2, "PidLidFax3OriginalEntryId", "dispidFax3OriginalEntryID", None),
        0x80d6: (0x000b, "monitorClock", "ms-Exch-Monitor-Clock", 2, None, None, None),
        0x80d7: (0x000b, "monitorServers", "ms-Exch-Monitor-Servers", 2, None, None, None),
        0x80d8: (0x000b, "monitorServices", "ms-Exch-Monitor-Services", 2, "PidLidFreeBusyLocation", "dispidFreeBusyLocation", None),
        0x80d9: (0x101f, "monitoredConfigurations", "ms-Exch-Monitored-Configurations", 2, None, None, None),
        0x80da: (0x101f, "monitoredServers", "ms-Exch-Monitored-Servers", 2, "PidLidHomeAddressCountryCode", "dispidHomeAddressCountryCode", None),
        0x80db: (0x101f, "monitoredServices", "ms-Exch-Monitored-Services", 2, "PidLidWorkAddressCountryCode", "dispidWorkAddressCountryCode", None),
        0x80dc: (0x0003, "monitoringAlertDelay", "ms-Exch-Monitoring-Alert-Delay", 2, "PidLidOtherAddressCountryCode", "dispidOtherAddressCountryCode", None),
        0x80dd: (0x0003, "monitoringAlertUnits", "ms-Exch-Monitoring-Alert-Units", 2, "PidLidAddressCountryCode", "dispidAddressCountryCode", None),
        0x80de: (0x0003, "monitoringAvailabilityStyle", "ms-Exch-Monitoring-Availability-Style", 2, "PidLidBirthdayLocal", "dispidApptBirthdayLocal", None),
        0x80df: (0x0102, "monitoringAvailabilityWindow", "ms-Exch-Monitoring-Availability-Window", 2, "PidLidWeddingAnniversaryLocal", "dispidApptAnniversaryLocal", None),
        0x80e0: (0x101f, "monitoringCachedViaMail", "ms-Exch-Monitoring-Cached-Via-Mail", 2, "PidLidIsContactLinked", "dispidIsContactLinked", None),
        0x80e1: (0x101f, "monitoringCachedViaRPC", "ms-Exch-Monitoring-Cached-Via-RPC", 2, None, None, None),
        0x80e2: (0x1102, "monitoringEscalationProcedure", "ms-Exch-Monitoring-Escalation-Procedure", 2, "PidLidContactLinkedGlobalAddressListEntryId", "dispidContactLinkedGALEntryID", None),
        0x80e3: (0x0003, "monitoringHotsitePollInterval", "ms-Exch-Monitoring-Hotsite-Poll-Interval", 2, "PidLidContactLinkSMTPAddressCache", "dispidContactLinkSMTPAddressCache", None),
        0x80e4: (0x0003, "monitoringHotsitePollUnits", "ms-Exch-Monitoring-Hotsite-Poll-Units", 2, None, None, None),
        0x80e5: (0x0003, "monitoringMailUpdateInterval", "ms-Exch-Monitoring-Mail-Update-Interval", 2, "PidLidContactLinkLinkRejectHistory", "dispidContactLinkLinkRejectHistory", None),
        0x80e6: (0x0003, "monitoringMailUpdateUnits", "ms-Exch-Monitoring-Mail-Update-Units", 2, "PidLidContactLinkGlobalAddressListLinkState", "dispidContactLinkGALLinkState", None),
        0x80e7: (0x0003, "monitoringNormalPollInterval", "ms-Exch-Monitoring-Normal-Poll-Interval", 2, None, None, None),
        0x80e8: (0x0003, "monitoringNormalPollUnits", "ms-Exch-Monitoring-Normal-Poll-Units", 2, "PidLidContactLinkGlobalAddressListLinkId", "dispidContactLinkGALLinkID", None),
        0x80e9: (0x101f, "monitoringRecipients", "ms-Exch-Monitoring-Recipients", 2, None, None, None),
        0x80ea: (0x101f, "monitoringRecipientsNDR", "ms-Exch-Monitoring-Recipients-NDR", 2, None, None, None),
        0x80eb: (0x0003, "monitoringRPCUpdateInterval", "ms-Exch-Monitoring-RPC-Update-Interval", 2, None, None, None),
        0x80ec: (0x0003, "monitoringRPCUpdateUnits", "ms-Exch-Monitoring-RPC-Update-Units", 2, None, None, None),
        0x80ed: (0x0003, "monitoringWarningDelay", "ms-Exch-Monitoring-Warning-Delay", 2, None, None, None),
        0x80ee: (0x0003, "monitoringWarningUnits", "ms-Exch-Monitoring-Warning-Units", 2, None, None, None),
        0x80ef: (0x001f, "mTALocalCred", "ms-Exch-MTA-Local-Cred", 2, None, None, None),
        0x80f0: (0x001f, "mTALocalDesig", "ms-Exch-MTA-Local-Desig", 2, None, None, None),
        0x80f1: (0x0102, "nAddress", "ms-Exch-N-Address", 2, None, None, None),
        0x80f2: (0x0003, "nAddressType", "ms-Exch-N-Address-Type", 2, None, None, None),
        0x80f4: (0x0003, "numOfOpenRetries", "ms-Exch-Num-Of-Open-Retries", 2, None, None, None),
        0x80f5: (0x0003, "numOfTransferRetries", "ms-Exch-Num-Of-Transfer-Retries", 2, None, None, None),
        0x80f6: (0x0003, "objectClassCategory", "Object-Class-Category", 3, None, None, None),
        0x80f7: (0x0003, "objectVersion", "Object-Version", 3, None, None, None),
        0x80f8: (0x101f, "offLineABContainers", "ms-Exch-Off-Line-AB-Containers", 2, None, None, None),
        0x80f9: (0x0102, "offLineABSchedule", "ms-Exch-Off-Line-AB-Schedule", 2, None, None, None),
        0x80fa: (0x001f, "offLineABServer", "ms-Exch-Off-Line-AB-Server", 2, None, None, None),
        0x80fb: (0x0003, "offLineABStyle", "ms-Exch-Off-Line-AB-Style", 2, None, None, None),
        0x80fd: (0x0102, "oMObjectClass", "OM-Object-Class", 3, None, None, None),
        0x80fe: (0x0003, "oMSyntax", "OM-Syntax", 1, None, None, None),
        0x80ff: (0x000b, "oOFReplyToOriginator", "ms-Exch-OOF-Reply-To-Originator", 1, None, None, None),
        0x8100: (0x0003, "openRetryInterval", "ms-Exch-Open-Retry-Interval", 2, None, None, None),
        0x8101: (0x101f, "o", "Organization-Name", 1, "PidLidTaskStatus", "dispidTaskStatus", None),
        0x8102: (0x101f, "ou", "Organizational-Unit-Name", 1, "PidLidPercentComplete", "dispidPercentComplete", None),
        0x8103: (0x0102, "originalDisplayTable", "Original-Display-Table", 3, "PidLidTeamTask", "dispidTeamTask", None),
        0x8104: (0x0102, "originalDisplayTableMSDOS", "Original-Display-Table-MSDOS", 3, "PidLidTaskStartDate", "dispidTaskStartDate", None),
        0x8105: (0x101f, "outboundSites", "ms-Exch-Outbound-Sites", 2, "PidLidTaskDueDate", "dispidTaskDueDate", None),
        0x8106: (0x0102, "pSelector", "ms-Exch-P-Selector", 2, None, None, None),
        0x8107: (0x0102, "pSelectorInbound", "ms-Exch-P-Selector-Inbound", 2, "PidLidTaskResetReminder", "dispidTaskResetReminder", None),
        0x8108: (0x0102, "perMsgDialogDisplayTable", "Per-Msg-Dialog-Display-Table", 3, "PidLidTaskAccepted", "dispidTaskAccepted", None),
        0x8109: (0x0102, "perRecipDialogDisplayTable", "Per-Recip-Dialog-Display-Table", 3, "PidLidTaskDeadOccurrence", "dispidTaskDeadOccur", None),
        0x810c: (0x101f, "postalAddress", "Postal-Address", 3, None, None, None),
        0x810e: (0x001f, "pRMD", "ms-Exch-PRMD", 2, None, None, None),
        0x810f: (0x001f, "proxyGeneratorDLL", "ms-Exch-Proxy-Generator-DLL", 2, "PidLidTaskDateCompleted", "dispidTaskDateCompleted", None),
        0x8110: (0x000d, "publicDelegatesBL", "ms-Exch-Public-Delegates-BL", 2, "PidLidTaskActualEffort", "dispidTaskActualEffort", None),
        0x8111: (0x0102, "quotaNotificationSchedule", "ms-Exch-Quota-Notification-Schedule", 2, "PidLidTaskEstimatedEffort", "dispidTaskEstimatedEffort", None),
        0x8112: (0x0003, "quotaNotificationStyle", "ms-Exch-Quota-Notification-Style", 2, "PidLidTaskVersion", "dispidTaskVersion", None),
        0x8113: (0x0003, "rangeLower", "Range-Lower", 1, "PidLidTaskState", "dispidTaskState", None),
        0x8114: (0x0003, "rangeUpper", "Range-Upper", 1, None, None, None),
        0x8115: (0x001f, "rASCallbackNumber", "ms-Exch-RAS-Callback-Number", 2, "PidLidTaskLastUpdate", "dispidTaskLastUpdate", None),
        0x8116: (0x001f, "rASPhoneNumber", "ms-Exch-RAS-Phone-Number", 2, "PidLidTaskRecurrence", "dispidTaskRecur", None),
        0x8117: (0x001f, "rASPhonebookEntryName", "ms-Exch-RAS-Phonebook-Entry-Name", 2, "PidLidTaskAssigners", "dispidTaskMyDelegators", None),
        0x8118: (0x001f, "rASRemoteSRVRName", "ms-Exch-RAS-Remote-SRVR-Name", 2, None, None, None),
        0x8119: (0x1102, "registeredAddress", "Registered-Address", 3, "PidLidTaskStatusOnComplete", "dispidTaskSOC", None),
        0x811a: (0x001f, "remoteBridgeHead", "ms-Exch-Remote-Bridge-Head", 2, "PidLidTaskHistory", "dispidTaskHistory", None),
        0x811b: (0x001f, "remoteBridgeHeadAddress", "ms-Exch-Remote-Bridge-Head-Address", 2, "PidLidTaskUpdates", "dispidTaskUpdates", None),
        0x811d: (0x001f, "remoteSite", "ms-Exch-Remote-Site", 2, None, None, None),
        0x811e: (0x0003, "replicationSensitivity", "ms-Exch-Replication-Sensitivity", 2, "PidLidTaskFCreator", "dispidTaskFCreator", None),
        0x811f: (0x0003, "replicationStagger", "ms-Exch-Replication-Stagger", 2, "PidLidTaskOwner", "dispidTaskOwner", None),
        0x8120: (0x000b, "reportToOriginator", "ms-Exch-Report-To-Originator", 1, "PidLidTaskMultipleRecipients", "dispidTaskMultRecips", None),
        0x8121: (0x000b, "reportToOwner", "ms-Exch-Report-To-Owner", 1, "PidLidTaskAssigner", "dispidTaskDelegator", None),
        0x8122: (0x0003, "reqSeq", "ms-Exch-Req-Seq", 2, "PidLidTaskLastUser", "dispidTaskLastUser", None),
        0x8123: (0x000d, "responsibleLocalDXA", "ms-Exch-Responsible-Local-DXA", 2, "PidLidTaskOrdinal", "dispidTaskOrdinal", None),
        0x8124: (0x001f, "ridServer", "ms-Exch-Rid-Server", 2, "PidLidTaskNoCompute", "dispidTaskNoCompute", None),
        0x8125: (0x101f, "roleOccupant", "Role-Occupant", 3, "PidLidTaskLastDelegate", "dispidTaskLastDelegate", None),
        0x8126: (0x101f, "routingList", "ms-Exch-Routing-List", 2, "PidLidTaskFRecurring", "dispidTaskFRecur", None),
        0x8127: (0x0003, "rTSCheckpointSize", "ms-Exch-RTS-Checkpoint-Size", 2, "PidLidTaskRole", "dispidTaskRole", None),
        0x8128: (0x0003, "rTSRecoveryTimeout", "ms-Exch-RTS-Recovery-Timeout", 2, None, None, None),
        0x8129: (0x0003, "rTSWindowSize", "ms-Exch-RTS-Window-Size", 2, "PidLidTaskOwnership", "dispidTaskOwnership", None),
        0x812a: (0x101f, "runsOn", "ms-Exch-Runs-On", 2, "PidLidTaskAcceptanceState", "dispidTaskDelegValue", None),
        0x812b: (0x0102, "sSelector", "ms-Exch-S-Selector", 1, None, None, None),
        0x812c: (0x0102, "sSelectorInbound", "ms-Exch-S-Selector-Inbound", 1, "PidLidTaskFFixOffline", "dispidTaskFFixOffline", None),
        0x812d: (0x0003, "searchFlags", "Search-Flags", 3, None, None, None),
        0x812e: (0x1102, "searchGuide", "Search-Guide", 3, None, None, None),
        0x812f: (0x101f, "seeAlso", "See-Also", 3, None, None, None),
        0x8130: (0x101f, "serialNumber", "Serial-Number", 3, None, None, None),
        0x8131: (0x0003, "serviceActionFirst", "ms-Exch-Service-Action-First", 2, None, None, None),
        0x8132: (0x0003, "serviceActionOther", "ms-Exch-Service-Action-Other", 2, None, None, None),
        0x8133: (0x0003, "serviceActionSecond", "ms-Exch-Service-Action-Second", 2, None, None, None),
        0x8134: (0x0003, "serviceRestartDelay", "ms-Exch-Service-Restart-Delay", 2, None, None, None),
        0x8135: (0x001f, "serviceRestartMessage", "ms-Exch-Service-Restart-Message", 2, None, None, None),
        0x8136: (0x0003, "sessionDisconnectTimer", "ms-Exch-Session-Disconnect-Timer", 2, None, None, None),
        0x8138: (0x101f, "siteProxySpace", "ms-Exch-Site-Proxy-Space", 2, None, None, None),
        0x8139: (0x0040, "spaceLastComputed", "ms-Exch-Space-Last-Computed", 2, "PidLidTaskCustomFlags", "dispidTaskCustomFlags", None),
        0x813a: (0x001f, "street", "Street-Address", 1, None, None, None),
        0x813b: (0x101f, "subRefs", "Sub-Refs", 1, None, None, None),
        0x813c: (0x0003, "submissionContLength", "ms-Exch-Submission-Cont-Length", 1, None, None, None),
        0x813d: (0x1102, "supportedApplicationContext", "Supported-Application-Context", 3, None, None, None),
        0x813e: (0x000d, "supportingStack", "ms-Exch-Supporting-Stack", 2, None, None, None),
        0x813f: (0x000d, "supportingStackBL", "ms-Exch-Supporting-Stack-BL", 2, None, None, None),
        0x8140: (0x0102, "tSelector", "ms-Exch-T-Selector", 2, None, None, None),
        0x8142: (0x101f, "targetMTAs", "ms-Exch-Target-MTAs", 2, None, None, None),
        0x8143: (0x1102, "teletexTerminalIdentifier", "Teletex-Terminal-Identifier", 3, None, None, None),
        0x8144: (0x0003, "tempAssocThreshold", "ms-Exch-Temp-Assoc-Threshold", 2, None, None, None),
        0x8145: (0x0003, "tombstoneLifetime", "Tombstone-Lifetime", 3, None, None, None),
        0x8146: (0x001f, "trackingLogPathName", "ms-Exch-Tracking-Log-Path-Name", 2, None, None, None),
        0x8147: (0x0003, "transRetryMins", "ms-Exch-Trans-Retry-Mins", 2, None, None, None),
        0x8148: (0x0003, "transTimeoutMins", "ms-Exch-Trans-Timeout-Mins", 2, None, None, None),
        0x8149: (0x0003, "transferRetryInterval", "ms-Exch-Transfer-Retry-Interval", 2, None, None, None),
        0x814a: (0x0003, "transferTimeoutNonUrgent", "ms-Exch-Transfer-Timeout-Non-Urgent", 2, None, None, None),
        0x814b: (0x0003, "transferTimeoutNormal", "ms-Exch-Transfer-Timeout-Normal", 2, None, None, None),
        0x814c: (0x0003, "transferTimeoutUrgent", "ms-Exch-Transfer-Timeout-Urgent", 2, None, None, None),
        0x814d: (0x0003, "translationTableUsed", "ms-Exch-Translation-Table-Used", 2, None, None, None),
        0x814e: (0x000b, "transportExpeditedData", "ms-Exch-Transport-Expedited-Data", 2, None, None, None),
        0x814f: (0x0003, "trustLevel", "ms-Exch-Trust-Level", 2, None, None, None),
        0x8150: (0x0003, "turnRequestThreshold", "ms-Exch-Turn-Request-Threshold", 2, None, None, None),
        0x8151: (0x000b, "twoWayAlternateFacility", "ms-Exch-Two-Way-Alternate-Facility", 2, None, None, None),
        0x8152: (0x000d, "unauthOrigBL", "ms-Exch-Unauth-Orig-BL", 1, None, None, None),
        0x8153: (0x1102, "userPassword", "User-Password", 3, None, None, None),
        0x8154: (0x0003, "uSNCreated", "USN-Created", 1, None, None, None),
        0x8155: (0x0003, "uSNDSALastObjRemoved", "USN-DSA-Last-Obj-Removed", 3, None, None, None),
        0x8156: (0x0003, "uSNLastObjRem", "USN-Last-Obj-Rem", 1, None, None, None),
        0x8157: (0x0003, "uSNSource", "USN-Source", 3, None, None, None),
        0x8158: (0x101f, "x121Address", "X121-Address", 3, None, None, None),
        0x8159: (0x0102, "x25CallUserDataIncoming", "ms-Exch-X25-Call-User-Data-Incoming", 2, None, None, None),
        0x815a: (0x0102, "x25CallUserDataOutgoing", "ms-Exch-X25-Call-User-Data-Outgoing", 2, None, None, None),
        0x815b: (0x0102, "x25FacilitiesDataIncoming", "ms-Exch-X25-Facilities-Data-Incoming", 2, None, None, None),
        0x815c: (0x0102, "x25FacilitiesDataOutgoing", "ms-Exch-X25-Facilities-Data-Outgoing", 2, None, None, None),
        0x815d: (0x0102, "x25LeasedLinePort", "ms-Exch-X25-Leased-Line-Port", 2, None, None, None),
        0x815e: (0x000b, "x25LeasedOrSwitched", "ms-Exch-X25-Leased-Or-Switched", 2, None, None, None),
        0x815f: (0x001f, "x25RemoteMTAPhone", "ms-Exch-X25-Remote-MTA-Phone", 2, None, None, None),
        0x8160: (0x0102, "x400AttachmentType", "ms-Exch-X400-Attachment-Type", 2, None, None, None),
        0x8161: (0x0003, "x400SelectorSyntax", "ms-Exch-X400-Selector-Syntax", 2, None, None, None),
        0x8163: (0x0003, "xMITTimeoutNonUrgent", "ms-Exch-XMIT-Timeout-Non-Urgent", 2, None, None, None),
        0x8164: (0x0003, "xMITTimeoutNormal", "ms-Exch-XMIT-Timeout-Normal", 2, None, None, None),
        0x8165: (0x0003, "xMITTimeoutUrgent", "ms-Exch-XMIT-Timeout-Urgent", 2, None, None, None),
        0x8166: (0x0102, "siteFolderGUID", "ms-Exch-Site-Folder-GUID", 2, None, None, None),
        0x8167: (0x001f, "siteFolderServer", "ms-Exch-Site-Folder-Server", 2, None, None, None),
        0x8168: (0x0003, "replicationMailMsgSize", "ms-Exch-Replication-Mail-Msg-Size", 2, None, None, None),
        0x8169: (0x0102, "maximumObjectID", "ms-Exch-Maximum-Object-ID", 2, None, None, None),
        0x8170: (0x101f, "networkAddress", "Network-Address", 1, "PidTagAddressBookNetworkAddress", "PR_EMS_AB_NETWORK_ADDRESS", "AbNetworkAddress"),
        0x8171: (0x001f, "lDAPDisplayName", "LDAP-Display-Name", 1, None, None, None),
        0x8174: (0x101f, "bridgeheadServers", "ms-Exch-Bridgehead-Servers", 2, None, None, None),
        0x8175: (0x101f, "url", "WWW-Page-Other", 1, None, None, None),
        0x8177: (0x001f, "pOPContentFormat", "ms-Exch-POP-Content-Format", 2, None, None, None),
        0x8178: (0x0003, "languageCode", "ms-Exch-Language", 2, None, None, None),
        0x8179: (0x001f, "pOPCharacterSet", "ms-Exch-POP-Character-Set", 2, None, None, None),
        0x817a: (0x0003, "USNIntersite", "USN-Intersite", 3, None, None, None),
        0x817f: (0x0003, "enabledProtocols", "ms-Exch-Enabled-Protocols", 1, None, None, None),
        0x8180: (0x0102, "connectionListFilter", "ms-Exch-Connection-List-Filter", 2, None, None, None),
        0x8181: (0x101f, "availableAuthorizationPackages", "ms-Exch-Available-Authorization-Packages", 2, None, None, None),
        0x8182: (0x101f, "characterSetList", "ms-Exch-Character-Set-List", 2, None, None, None),
        0x8183: (0x000b, "useSiteValues", "ms-Exch-Use-Site-Values", 2, None, None, None),
        0x8184: (0x101f, "enabledAuthorizationPackages", "ms-Exch-Enabled-Authorization-Packages", 2, None, None, None),
        0x8185: (0x001f, "characterSet", "ms-Exch-Character-Set", 2, None, None, None),
        0x8186: (0x0003, "contentType", "ms-Exch-Content-Type", 2, None, None, None),
        0x8187: (0x000b, "anonymousAccess", "ms-Exch-Anonymous-Access", 2, None, None, None),
        0x8188: (0x0102, "controlMsgFolderID", "ms-Exch-Control-Msg-Folder-ID", 2, None, None, None),
        0x8189: (0x001f, "usenetSiteName", "ms-Exch-Usenet-Site-Name", 2, None, None, None),
        0x818a: (0x0102, "controlMsgRules", "ms-Exch-Control-Msg-Rules", 2, None, None, None),
        0x818b: (0x001f, "availableDistributions", "ms-Exch-Available-Distributions", 2, None, None, None),
        0x818f: (0x0003, "outgoingMsgSizeLimit", "ms-Exch-Outgoing-Msg-Size-Limit", 2, None, None, None),
        0x8190: (0x0003, "incomingMsgSizeLimit", "ms-Exch-Incoming-Msg-Size-Limit", 2, None, None, None),
        0x8191: (0x000b, "sendTNEF", "ms-Exch-Send-TNEF", 2, None, None, None),
        0x819b: (0x000b, "hTTPPubGAL", "ms-Exch-HTTP-Pub-GAL", 2, None, None, None),
        0x819c: (0x0003, "hTTPPubGALLimit", "ms-Exch-HTTP-Pub-GAL-Limit", 2, None, None, None),
        0x819e: (0x1102, "hTTPPubPF", "ms-Exch-HTTP-Pub-PF", 2, None, None, None),
        0x81a1: (0x001f, "x500RDN", "ms-Exch-X500-RDN", 2, None, None, None),
        0x81a2: (0x001f, "dnQualifier", "ms-Exch-X500-NC", 2, None, None, None),
        0x81a3: (0x101f, "referralList", "ms-Exch-Referral-List", 2, None, None, None),
        0x81a8: (0x000b, "enabledProtocolCfg", "ms-Exch-Enabled-Protocol-Cfg", 2, None, None, None),
        0x81a9: (0x101f, "hTTPPubABAttributes", "ms-Exch-HTTP-Pub-AB-Attributes", 2, None, None, None),
        0x81ab: (0x101f, "hTTPServers", "ms-Exch-HTTP-Servers", 2, None, None, None),
        0x81b1: (0x000b, "proxyGenerationEnabled", "Proxy-Generation-Enabled", 3, None, None, None),
        0x81b2: (0x0102, "rootNewsgroupsFolderID", "ms-Exch-Root-Newsgroups-Folder-ID", 2, None, None, None),
        0x81b4: (0x0003, "connectionListFilterType", "ms-Exch-Connection-List-Filter-Type", 2, None, None, None),
        0x81b5: (0x0003, "portNumber", "ms-Exch-Port-Number", 2, None, None, None),
        0x81b6: (0x101f, "protocolSettings", "ms-Exch-Protocol-Settings", 1, None, None, None),
        0x81c2: (0x0040, "promoExpiration", "ms-Exch-Promo-Expiration", 1, None, None, None),
        0x81c3: (0x101f, "disabledGatewayProxy", "ms-Exch-Disabled-Gateway-Proxy", 2, None, None, None),
        0x81c4: (0x0102, "compromisedKeyList", "ms-Exch-Compromised-Key-List", 2, None, None, None),
        0x81c5: (0x001f, "iNSAdmin", "ms-Exch-INSAdmin", 2, None, None, None),
        0x81c7: (0x101f, "objViewContainers", "ms-Exch-Obj-View-Containers", 2, None, None, None),
        0x8202: (0x001f, "name", "RDN", 1, "PidLidAppointmentSequenceTime", "dispidApptSeqTime", None),
        0x8c1c: (0x0102, "msExchMimeTypes", "ms-Exch-Mime-Types", 2, None, None, None),
        0x8c1d: (0x0003, "lDAPSearchCfg", "ms-Exch-LDAP-Search-Cfg", 2, None, None, None),
        0x8c21: (0x000b, "Enabled", "Enabled", 3, None, None, None),
        0x8c22: (0x000b, "preserveInternetContent", "ms-Exch-Preserve-Internet-Content", 2, None, None, None),
        0x8c24: (0x000b, "clientAccessEnabled", "ms-Exch-Client-Access-Enabled", 2, None, None, None),
        0x8c25: (0x000b, "requireSSL", "ms-Exch-Require-SSL", 2, None, None, None),
        0x8c26: (0x001f, "anonymousAccount", "ms-Exch-Anonymous-Account", 2, None, None, None),
        0x8c27: (0x0102, "certificateChainV3", "ms-Exch-Certificate-Chain-V3", 2, None, None, None),
        0x8c28: (0x0102, "certificateRevocationListV3", "ms-Exch-Certificate-Revocation-List-V3", 2, None, None, None),
        0x8c29: (0x0102, "certificateRevocationListV1", "ms-Exch-Certificate-Revocation-List-V1", 2, None, None, None),
        0x8c30: (0x1102, "crossCertificateCRL", "ms-Exch-Cross-Certificate-CRL", 2, None, None, None),
        0x8c31: (0x000b, "sendEMailMessage", "ms-Exch-Send-EMail-Message", 2, None, None, None),
        0x8c32: (0x000b, "enableCompatibility", "ms-Exch-Enable-Compatibility", 2, None, None, None),
        0x8c33: (0x101f, "sMIMEAlgListNA", "ms-Exch-SMIME-Alg-List-NA", 2, None, None, None),
        0x8c34: (0x101f, "sMIMEAlgListOther", "ms-Exch-SMIME-Alg-List-Other", 2, None, None, None),
        0x8c35: (0x001f, "sMIMEAlgSelectedNA", "ms-Exch-SMIME-Alg-Selected-NA", 2, None, None, None),
        0x8c36: (0x001f, "sMIMEAlgSelectedOther", "ms-Exch-SMIME-Alg-Selected-Other", 2, None, None, None),
        0x8c37: (0x000b, "defaultMessageFormat", "ms-Exch-Default-Message-Format", 2, None, None, None),
        0x8c38: (0x001f, "type", "ms-Exch-Type", 1, None, None, None),
        0x8c3a: (0x0003, "doOABVersion", "ms-Exch-Do-OAB-Version", 2, None, None, None),
        0x8c45: (0x1102, "attributeCertificate", "ms-Exch-Attribute-Certificate", 1, None, None, None),
        0x8c46: (0x1102, "deltaRevocationList", "Delta-Revocation-List", 3, None, None, None),
        0x8c47: (0x1102, "securityPolicy", "ms-Exch-Security-Policy", 2, None, None, None),
        0x8c48: (0x000b, "supportSMIMESignatures", "ms-Exch-Support-SMIME-Signatures", 2, None, None, None),
        0x8c49: (0x000b, "delegateUser", "ms-Exch-Delegate-User", 2, None, None, None),
        0x8c50: (0x000b, "listPublicFolders", "ms-Exch-List-Public-Folders", 2, None, None, None),
        0x8c51: (0x101f, "msExchLabeledURI", "ms-Exch-LabeledURI", 1, None, None, None),
        0x8c52: (0x000b, "returnExactMsgSize", "ms-Exch-Return-Exact-Msg-Size", 2, None, None, None),
        0x8c53: (0x001f, "generationQualifier", "Generation-Qualifier", 3, None, None, None),
        0x8c54: (0x001f, "msExchHouseIdentifier", "ms-Exch-House-Identifier", 3, None, None, None),
        0x8c55: (0x0102, "supportedAlgorithms", "ms-Exch-Supported-Algorithms", 1, None, None, None),
        0x8c56: (0x001f, "dmdName", "DMD-Name", 3, None, None, None),
        0x8c57: (0x001f, "extensionAttribute11", "ms-Exch-Extension-Attribute-11", 1, "PidTagAddressBookExtensionAttribute11", "PR_EMS_AB_EXTENSION_ATTRIBUTE_11", None),
        0x8c58: (0x001f, "extensionAttribute12", "ms-Exch-Extension-Attribute-12", 1, "PidTagAddressBookExtensionAttribute12", "PR_EMS_AB_EXTENSION_ATTRIBUTE_12", None),
        0x8c59: (0x001f, "extensionAttribute13", "ms-Exch-Extension-Attribute-13", 1, "PidTagAddressBookExtensionAttribute13", "PR_EMS_AB_EXTENSION_ATTRIBUTE_13", None),
        0x8c60: (0x001f, "extensionAttribute14", "ms-Exch-Extension-Attribute-14", 1, "PidTagAddressBookExtensionAttribute14", "PR_EMS_AB_EXTENSION_ATTRIBUTE_14", None),
        0x8c61: (0x001f, "extensionAttribute15", "ms-Exch-Extension-Attribute-15", 1, "PidTagAddressBookExtensionAttribute15", "PR_EMS_AB_EXTENSION_ATTRIBUTE_15", None),
        0x8c62: (0x0003, "replicatedObjectVersion", "ms-Exch-Replicated-Object-Version", 1, None, None, None),
        0x8c64: (0x001f, "forwardingAddress", "ms-Exch-Forwarding-Address", 2, None, None, None),
        0x8c65: (0x0102, "formData", "ms-Exch-Form-Data", 2, None, None, None),
        0x8c66: (0x001f, "oWAServer", "ms-Exch-OWA-Server", 2, None, None, None),
        0x8c67: (0x001f, "employeeNumber", "Employee-Number", 3, None, None, None),
        0x8c68: (0x001f, "personalPager", "ms-Exch-Telephone-Personal-Pager", 2, None, None, None),
        0x8c69: (0x001f, "employeeType", "Employee-Type", 3, None, None, None),
        0x8c6a: (0x1102, "userCertificate", "X509-Cert", 1, "PidTagAddressBookX509Certificate", "PR_EMS_AB_X509_CERT", "Certificate"),
        0x8c6b: (0x001f, "personalTitle", "Personal-Title", 3, None, None, None),
        0x8c6c: (0x001f, "language", "ms-Exch-Language-ISO639", 2, None, None, None),
        0x8c6d: (0x0102, "objectGUID", "Object-Guid", 1, "PidTagAddressBookObjectGuid", "PR_EMS_AB_OBJECT_GUID", "ObjectGuid"),
        0x8c6e: (0x101f, "otherPager", "Phone-Pager-Other", 1, None, None, None),
        0x8c73: (0x0102, "msExchMailboxGuid", "ms-Exch-Mailbox-Guid", 1, None, None, None),
        0x8c75: (0x0102, "msExchMasterAccountSid", "ms-Exch-Master-Account-Sid", 1, None, None, None),
        0x8c7e: (0x001f, "msExchFBURL", "ms-Exch-FB-URL", 1, None, None, None),
        0x8c7f: (0x001f, "msExchMailboxUrl", "ms-Exch-Mailbox-Url", 1, None, None, None),
        0x8c81: (0x001f, "textEncodedORAddress", "Text-Encoded-OR-Address", 1, None, None, None),
        0x8c82: (0x001f, "msExchPfRootUrl", "ms-Exch-Pf-Root-Url", 1, None, None, None),
        0x8c8e: (0x001f, "msDS-PhoneticFirstName", "ms-DS-Phonetic-First-Name", 1, "PidTagAddressBookPhoneticGivenName", "PR_EMS_AB_PHONETIC_GIVEN_NAME", None),
        0x8c8f: (0x001f, "msDS-PhoneticLastName", "ms-DS-Phonetic-Last-Name", 1, "PidTagAddressBookPhoneticSurname", "PR_EMS_AB_PHONETIC_SURNAME", None),
        0x8c90: (0x001f, "msDS-PhoneticDepartment", "ms-DS-Phonetic-Department", 1, "PidTagAddressBookPhoneticDepartmentName", "PR_EMS_AB_PHONETIC_DEPARTMENT_NAME", None),
        0x8c91: (0x001f, "msDS-PhoneticCompanyName", "ms-DS-Phonetic-Company-Name", 1, "PidTagAddressBookPhoneticCompanyName", "PR_EMS_AB_PHONETIC_COMPANY_NAME", None),
        0x8c92: (0x001f, "msDS-PhoneticDisplayName", "ms-DS-Phonetic-Display-Name", 1, "PidTagAddressBookPhoneticDisplayName", "PR_EMS_AB_PHONETIC_DISPLAY_NAME", None),
        0x8c94: (0x000d, "msExchHABShowInDepartments", "ms-Exch-HAB-Show-In-Departments", 1, "PidTagAddressBookHierarchicalShowInDepartments", "PR_EMS_AB_HAB_SHOW_IN_DEPARTMENTS", None),
        0x8c96: (0x101f, "msExchResourceAddressLists", "ms-Exch-Resource-Address-Lists", 1, "PidTagAddressBookRoomContainers", "PR_EMS_AB_ROOM_CONTAINERS", "AddressBookRoomContainers"),
        0x8c97: (0x000d, "msExchHABShowInDepartmentsBL", "ms-Exch-HAB-Show-In-Departments-BL", 2, "PidTagAddressBookHierarchicalDepartmentMembers", "PR_EMS_AB_HAB_DEPARTMENT_MEMBERS", None),
        0x8c98: (0x000d, "msExchHABRootDepartmentLink", "ms-Exch-HAB-Root-Department-Link", 1, "PidTagAddressBookHierarchicalRootDepartment", "PR_EMS_AB_HAB_ROOT_DEPARTMENT", None),
        0x8c99: (0x000d, "msExchHABChildDepartmentsBL", "ms-Exch-HAB-Child-Departments-BL", 2, "PidTagAddressBookHierarchicalParentDepartment", "PR_EMS_AB_HAB_PARENT_DEPARTMENT", None),
        0x8c9a: (0x000d, "msExchHABChildDepartmentsLink", "ms-Exch-HAB-Child-Departments-Link", 2, "PidTagAddressBookHierarchicalChildDepartments", "PR_EMS_AB_HAB_CHILD_DEPARTMENTS", None),
        0x8c9b: (0x000d, "msExchHABRootDepartmentBL", "ms-Exch-HAB-Root-Department-BL", 2, None, None, None),
        0x8c9e: (0x0102, "thumbnailPhoto", "Picture", 1, "PidTagThumbnailPhoto", "PR_EMS_AB_THUMBNAIL_PHOTO", "ThumbnailPhoto"),
        0x8c9f: (0x001f, "msExchUserCulture", "ms-Exch-User-Culture", 1, None, None, None),
        0x8ca0: (0x0003, "msDS-HABSeniorityIndex", "ms-DS-HAB-Seniority-Index", 1, "PidTagAddressBookSeniorityIndex", None, None),
        0x8ca1: (0x000b, "msExchPhoneticSupport", "ms-Exch-Phonetic-Support", 2, None, None, None),
        0x8ca2: (0x0003, "msExchMaxSafeSenders", "ms-Exch-Max-Safe-Senders", 1, None, None, None),
        0x8ca3: (0x0003, "msExchMaxBlockedSenders", "ms-Exch-Max-Blocked-Senders", 1, None, None, None),
        0x8ca5: (0x000d, "msExchConfigurationUnitLink", "ms-Exch-Configuration-Unit-Link", 2, None, None, None),
        0x8ca6: (0x000d, "msExchConfigurationUnitBL", "ms-Exch-Configuration-Unit-BL", 1, None, None, None),
        0x8ca7: (0x001f, "msExchCU", "ms-Exch-CU", 1, None, None, None),
        0x8ca8: (0x001f, "msExchOURoot", "ms-Exch-OU-Root", 1, "PidTagAddressBookOrganizationalUnitRootDistinguishedName", "PR_EMS_AB_ORG_UNIT_ROOT_DN", None),
        0x8ca9: (0x0003, "msExchSenderHintsEnabled", "ms-Exch-Sender-Hints-Enabled", 2, None, None, None),
        0x8caa: (0x0003, "msExchSenderHintLargeAudienceThreshold", "ms-Exch-Sender-Hint-Large-Audience-Threshold", 2, None, None, None),
        0x8cac: (0x101f, "msExchSenderHintTranslations", "ms-Exch-Sender-Hint-Translations", 1, "PidTagAddressBookSenderHintTranslations", "PR_EMS_AB_DL_SENDER_HINT_TRANSLATIONS_W", None),
        0x8cad: (0x000d, "msExchModeratedByLink", "ms-Exch-Moderated-By-Link", 1, None, None, None),
        0x8cae: (0x000d, "msExchCoManagedByLink", "ms-Exch-Co-Managed-By-Link", 1, None, None, None),
        0x8caf: (0x000d, "msExchModeratedObjectsBL", "ms-Exch-Moderated-Objects-BL", 1, None, None, None),
        0x8cb0: (0x000d, "msExchCoManagedObjectsBL", "ms-Exch-Co-Managed-Objects-BL", 1, None, None, None),
        0x8cb1: (0x001f, "msExchArbitrationMailbox", "ms-Exch-Arbitration-Mailbox", 1, None, None, None),
        0x8cb3: (0x0003, "msExchGroupJoinRestriction", "ms-Exch-Group-Join-Restriction", 1, None, None, None),
        0x8cb4: (0x0003, "msExchGroupDepartRestriction", "ms-Exch-Group-Depart-Restriction", 1, None, None, None),
        0x8cb5: (0x000b, "msExchEnableModeration", "ms-Exch-Enable-Moderation", 1, "PidTagAddressBookModerationEnabled", None, None),
        0x8cb6: (0x0003, "msExchModerationFlags", "ms-Exch-Moderation-Flags", 1, None, None, None),
        0x8cb7: (0x101f, "msExchSignupAddresses", "ms-Exch-Signup-Addresses", 1, None, None, None),
        0x8cc1: (0x001f, "msExchWindowsLiveID", "ms-Exch-Windows-Live-ID", 1, None, None, None),
        0x8cc2: (0x0102, "msExchUMSpokenName", "ms-Exch-UM-Spoken-Name", 1, "PidTagSpokenName", "PR_EMS_AB_UM_SPOKEN_NAME", None),
        0x8cc3: (0x001f, "msExchPrivacyStatementURL", "ms-Exch-Privacy-Statement-URL", 2, None, None, None),
        0x8cc4: (0x001f, "msExchControlPanelFeedbackURL", "ms-Exch-Control-Panel-Feedback-URL", 2, None, None, None),
        0x8cc5: (0x001f, "msExchControlPanelHelpURL", "ms-Exch-Control-Panel-Help-URL", 2, None, None, None),
        0x8cc6: (0x000b, "msExchExchangeHelpAppOnline", "ms-Exch-Exchange-Help-App-Online", 2, None, None, None),
        0x8cc7: (0x001f, "msExchManagementConsoleFeedbackURL", "ms-Exch-Management-Console-Feedback-URL", 2, None, None, None),
        0x8cc8: (0x001f, "msExchManagementConsoleHelpURL", "ms-Exch-Management-Console-Help-URL", 2, None, None, None),
        0x8cc9: (0x001f, "msExchOWAFeedbackURL", "ms-Exch-OWA-Feedback-URL", 2, None, None, None),
        0x8cca: (0x001f, "msExchOWAHelpURL", "ms-Exch-OWA-Help-URL", 2, None, None, None),
        0x8ccb: (0x001f, "msExchWindowsLiveAccountURL", "ms-Exch-Windows-Live-Account-URL", 2, None, None, None),
        0x8ccc: (0x0003, "msExchTransportRecipientSettingsFlags", "ms-Exch-Transport-Recipient-Settings-Flags", 1, None, None, None),
        0x8ccd: (0x000b, "msExchControlPanelFeedbackEnabled", "ms-Exch-Control-Panel-Feedback-Enabled", 2, None, None, None),
        0x8cce: (0x000b, "msExchManagementConsoleFeedbackEnabled", "ms-Exch-Management-Console-Feedback-Enabled", 2, None, None, None),
        0x8ccf: (0x000b, "msExchOWAFeedbackEnabled", "ms-Exch-OWA-Feedback-Enabled", 2, None, None, None),
        0x8cd0: (0x000b, "msExchPrivacyStatementURLEnabled", "ms-Exch-Privacy-Statement-URL-Enabled", 2, None, None, None),
        0x8cd1: (0x000b, "msExchWindowsLiveAccountURLEnabled", "ms-Exch-Windows-Live-Account-URL-Enabled", 2, None, None, None),
        0x8cd2: (0x000d, "msExchBypassModerationLink", "ms-Exch-Bypass-Moderation-Link", 1, None, None, None),
        0x8cd3: (0x000d, "msExchBypassModerationBL", "ms-Exch-Bypass-Moderation-BL", 1, None, None, None),
        0x8cd4: (0x000d, "msExchBypassModerationFromDLMembersLink", "ms-Exch-Bypass-Moderation-From-DL-Members-Link", 1, None, None, None),
        0x8cd5: (0x000d, "msExchBypassModerationFromDLMembersBL", "ms-Exch-Bypass-Moderation-From-DL-Members-BL", 1, None, None, None),
        0x8cd6: (0x001f, "msExchRetentionComment", "ms-Exch-Retention-Comment", 1, None, None, None),
        0x8cd7: (0x001f, "msExchRetentionURL", "ms-Exch-Retention-URL", 1, None, None, None),
        0x8cd8: (0x000d, "authOrig", "ms-Exch-Auth-Orig", 1, "PidTagAddressBookAuthorizedSenders", "PR_EMS_AB_AUTH_ORIG", None),
        0x8cd9: (0x000d, "unauthOrig", "ms-Exch-Unauth-Orig", 1, "PidTagAddressBookUnauthorizedSenders", "PR_EMS_AB_UNAUTH_ORIG", None),
        0x8cda: (0x000d, "dLMemSubmitPerms", "ms-Exch-DL-Mem-Submit-Perms", 1, "PidTagAddressBookDistributionListMemberSubmitRejected", "PR_EMS_AB_DL_MEM_SUBMIT_PERMS", None),
        0x8cdb: (0x000d, "dLMemRejectPerms", "ms-Exch-DL-Mem-Reject-Perms", 1, "PidTagAddressBookDistributionListRejectMessagesFromDLMembers", "PR_EMS_AB_DL_MEM_REJECT_PERMS", None),
        0x8cdc: (0x000d, "msOrg-LeadersBL", "ms-Org-Leaders-BL", 2, None, None, None),
        0x8cdd: (0x000b, "msOrg-IsOrganizational", "ms-Org-Is-Organizational-Group", 1, "PidTagAddressBookHierarchicalIsHierarchicalGroup", "PR_EMS_AB_HAB_IS_HIERARCHICAL_GROUP", None),
        0x8cde: (0x000d, "msOrg-Leaders", "ms-Org-Leaders", 1, None, None, None),
        0x8cdf: (0x001f, "msOrg-GroupSubtypeName", "ms-Org-Group-Subtype-Name", 1, None, None, None),
        0x8ce0: (0x101f, "msOrg-OtherDisplayNames", "ms-Org-Other-Display-Names", 1, None, None, None),
        0x8ce2: (0x0003, "msExchGroupMemberCount", "ms-Exch-Group-Member-Count", 1, "PidTagAddressBookDistributionListMemberCount", "PR_EMS_AB_DL_TOTAL_MEMBER_COUNT", None),
        0x8ce3: (0x0003, "msExchGroupExternalMemberCount", "ms-Exch-Group-External-Member-Count", 1, "PidTagAddressBookDistributionListExternalMemberCount", "PR_EMS_AB_DL_EXTERNAL_MEMBER_COUNT", None),
        0x8ce5: (0x000d, "msExchDelegateListLink", "ms-Exch-Delegate-List-Link", 1, None, None, None),
        0x8ce6: (0x000d, "msExchDelegateListBL", "ms-Exch-Delegate-List-BL", 2, None, None, None),
        0x8ce7: (0x001f, "msExchArchiveAddress", "ms-Exch-Archive-Address", 1, None, None, None),
        0x8ce8: (0x0003, "msExchArchiveStatus", "ms-Exch-Archive-Status", 1, None, None, None),
        0x8ce9: (0x001f, "msExchDistributionGroupNamingPolicy", "ms-Exch-Distribution-Group-Naming-Policy", 2, None, None, None),
        0x8cea: (0x101f, "msExchDistributionGroupNameBlockedWordsList", "ms-Exch-Distribution-Group-Name-Blocked-Words-List", 2, None, None, None),
        0x8ceb: (0x001f, "msExchDistributionGroupDefaultOU", "ms-Exch-Distribution-Group-Default-OU", 2, None, None, None),
        0x8cec: (0x0003, "msExchAddressBookFlags", "ms-Exch-Address-Book-Flags", 1, None, None, None),
        0x8ced: (0x001f, "msExchExtensionAttribute16", "ms-Exch-Extension-Attribute-16", 1, None, None, None),
        0x8cee: (0x001f, "msExchExtensionAttribute17", "ms-Exch-Extension-Attribute-17", 1, None, None, None),
        0x8cef: (0x001f, "msExchExtensionAttribute18", "ms-Exch-Extension-Attribute-18", 1, None, None, None),
        0x8cf0: (0x001f, "msExchExtensionAttribute19", "ms-Exch-Extension-Attribute-19", 1, None, None, None),
        0x8cf1: (0x001f, "msExchExtensionAttribute20", "ms-Exch-Extension-Attribute-20", 1, None, None, None),
        0x8cf2: (0x001f, "msExchExtensionAttribute21", "ms-Exch-Extension-Attribute-21", 1, None, None, None),
        0x8cf3: (0x001f, "msExchExtensionAttribute22", "ms-Exch-Extension-Attribute-22", 1, None, None, None),
        0x8cf4: (0x001f, "msExchExtensionAttribute23", "ms-Exch-Extension-Attribute-23", 1, None, None, None),
        0x8cf5: (0x001f, "msExchExtensionAttribute24", "ms-Exch-Extension-Attribute-24", 1, None, None, None),
        0x8cf6: (0x001f, "msExchExtensionAttribute25", "ms-Exch-Extension-Attribute-25", 1, None, None, None),
        0x8cf7: (0x001f, "msExchExtensionAttribute26", "ms-Exch-Extension-Attribute-26", 1, None, None, None),
        0x8cf8: (0x001f, "msExchExtensionAttribute27", "ms-Exch-Extension-Attribute-27", 1, None, None, None),
        0x8cf9: (0x001f, "msExchExtensionAttribute28", "ms-Exch-Extension-Attribute-28", 1, None, None, None),
        0x8cfa: (0x001f, "msExchExtensionAttribute29", "ms-Exch-Extension-Attribute-29", 1, None, None, None),
        0x8cfb: (0x001f, "msExchExtensionAttribute30", "ms-Exch-Extension-Attribute-30", 1, None, None, None),
        0x8cfc: (0x001f, "msExchExtensionAttribute31", "ms-Exch-Extension-Attribute-31", 1, None, None, None),
        0x8cfd: (0x001f, "msExchExtensionAttribute32", "ms-Exch-Extension-Attribute-32", 1, None, None, None),
        0x8cfe: (0x001f, "msExchExtensionAttribute33", "ms-Exch-Extension-Attribute-33", 1, None, None, None),
        0x8cff: (0x001f, "msExchExtensionAttribute34", "ms-Exch-Extension-Attribute-34", 1, None, None, None),
        0x8d00: (0x001f, "msExchExtensionAttribute35", "ms-Exch-Extension-Attribute-35", 1, None, None, None),
        0x8d01: (0x001f, "msExchExtensionAttribute36", "ms-Exch-Extension-Attribute-36", 1, None, None, None),
        0x8d02: (0x001f, "msExchExtensionAttribute37", "ms-Exch-Extension-Attribute-37", 1, None, None, None),
        0x8d03: (0x001f, "msExchExtensionAttribute38", "ms-Exch-Extension-Attribute-38", 1, None, None, None),
        0x8d04: (0x001f, "msExchExtensionAttribute39", "ms-Exch-Extension-Attribute-39", 1, None, None, None),
        0x8d05: (0x001f, "msExchExtensionAttribute40", "ms-Exch-Extension-Attribute-40", 1, None, None, None),
        0x8d06: (0x001f, "msExchExtensionAttribute41", "ms-Exch-Extension-Attribute-41", 1, None, None, None),
        0x8d07: (0x001f, "msExchExtensionAttribute42", "ms-Exch-Extension-Attribute-42", 1, None, None, None),
        0x8d08: (0x001f, "msExchExtensionAttribute43", "ms-Exch-Extension-Attribute-43", 1, None, None, None),
        0x8d09: (0x001f, "msExchExtensionAttribute44", "ms-Exch-Extension-Attribute-44", 1, None, None, None),
        0x8d0a: (0x001f, "msExchExtensionAttribute45", "ms-Exch-Extension-Attribute-45", 1, None, None, None),
        0x8d0b: (0x000d, "msExchUGMemberLink", "ms-Exch-UG-Member-Link", 1, None, None, None),
        0x8d0c: (0x000d, "msExchUGMemberBL", "ms-Exch-UG-Member-BL", 1, None, None, None),
        0x8d0e: (0x000d, "msExchAdministrativeUnitLink", "ms-Exch-Administrative-Unit-Link", 1, None, None, None),
        0x8d0f: (0x0003, "msExchGroupSecurityFlags", "ms-Exch-Group-Security-Flags", 1, None, None, None),
        0xfff8: (0x101f, "msExchTemplateRDNs", "ms-Exch-Template-RDNs", 2, None, None, None),
# ====================
        0x850e: (0x000b, None, None, 4, "PidLidAgingDontAgeMe", "dispidAgingDontAgeMe", None),
        0x8238: (0x001f, None, None, 4, "PidLidAllAttendeesString", "dispidAllAttendeesString", None),
        0x8246: (0x000b, None, None, 4, "PidLidAllowExternalCheck", "dispidAllowExternCheck", None),
        0x8207: (0x0003, None, None, 4, "PidLidAppointmentAuxiliaryFlags", "dispidApptAuxFlags", None),
        0x8214: (0x0003, None, None, 4, "PidLidAppointmentColor", "dispidApptColor", None),
        0x8257: (0x000b, None, None, 4, "PidLidAppointmentCounterProposal", "dispidApptCounterProposal", None),
        0x8213: (0x0003, None, None, 4, "PidLidAppointmentDuration", "dispidApptDuration", None),
        0x8211: (0x0040, None, None, 4, "PidLidAppointmentEndDate", "dispidApptEndDate", None),
        0x8210: (0x0040, None, None, 4, "PidLidAppointmentEndTime", "dispidApptEndTime", None),
        0x820e: (0x0040, None, None, 4, "PidLidAppointmentEndWhole", "dispidApptEndWhole", None),
        0x8203: (0x0003, None, None, 4, "PidLidAppointmentLastSequence", "dispidApptLastSequence", None),
        0x0024: (0x001f, None, None, 4, "PidLidAppointmentMessageClass", "dispidApptMessageClass", "OriginatorReturnAddress"),
        0x825a: (0x000b, None, None, 4, "PidLidAppointmentNotAllowPropose", "dispidApptNotAllowPropose", None),
        0x8259: (0x0003, None, None, 4, "PidLidAppointmentProposalNumber", "dispidApptProposalNum", None),
        0x8256: (0x0003, None, None, 4, "PidLidAppointmentProposedDuration", "dispidApptProposedDuration", None),
        0x8251: (0x0040, None, None, 4, "PidLidAppointmentProposedEndWhole", "dispidApptProposedEndWhole", None),
        0x8250: (0x0040, None, None, 4, "PidLidAppointmentProposedStartWhole", "dispidApptProposedStartWhole", None),
        0x8216: (0x0102, None, None, 4, "PidLidAppointmentRecur", "dispidApptRecur", None),
        0x8230: (0x001f, None, None, 4, "PidLidAppointmentReplyName", "dispidApptReplyName", None),
        0x8220: (0x0040, None, None, 4, "PidLidAppointmentReplyTime", "dispidApptReplyTime", None),
        0x8201: (0x0003, None, None, 4, "PidLidAppointmentSequence", "dispidApptSequence", None),
        0x8212: (0x0040, None, None, 4, "PidLidAppointmentStartDate", "dispidApptStartDate", None),
        0x820f: (0x0040, None, None, 4, "PidLidAppointmentStartTime", "dispidApptStartTime", None),
        0x820d: (0x0040, None, None, 4, "PidLidAppointmentStartWhole", "dispidApptStartWhole", None),
        0x8217: (0x0003, None, None, 4, "PidLidAppointmentStateFlags", "dispidApptStateFlags", None),
        0x8215: (0x000b, None, None, 4, "PidLidAppointmentSubType", "dispidApptSubType", None),
        0x825f: (0x0102, None, None, 4, "PidLidAppointmentTimeZoneDefinitionEndDisplay", "dispidApptTZDefEndDisplay", None),
        0x8260: (0x0102, None, None, 4, "PidLidAppointmentTimeZoneDefinitionRecur", "dispidApptTZDefRecur", None),
        0x825e: (0x0102, None, None, 4, "PidLidAppointmentTimeZoneDefinitionStartDisplay", "dispidApptTZDefStartDisplay", None),
        0x825d: (0x0102, None, None, 4, "PidLidAppointmentUnsendableRecipients", "dispidApptUnsendableRecips", None),
        0x8226: (0x0040, None, None, 4, "PidLidAppointmentUpdateTime", "dispidApptUpdateTime", None),
        0x0001: (0x0102, None, None, 4, "PidTagTemplateData", "PR_EMS_TEMPLATE_BLOB", "AcknowledgementMode"),
        0x823a: (0x000b, None, None, 4, "PidLidAutoFillLocation", "dispidAutoFillLocation", None),
        0x851a: (0x0003, None, None, 4, "PidLidAutoProcessState", "dispidSniffState", None),
        0x8244: (0x000b, None, None, 4, "PidLidAutoStartCheck", "dispidAutoStartCheck", None),
        0x8535: (0x001f, None, None, 4, "PidLidBilling", "dispidBilling", None),
        0x804d: (0x0102, None, None, 4, "PidLidBirthdayEventEntryId", "dispidBirthdayEventEID", None),
        0x8205: (0x0003, None, None, 4, "PidLidBusyStatus", "dispidBusyStatus", None),
        0x001c: (0x0003, None, None, 4, "PidLidCalendarType", "LID_CALENDAR_TYPE", None),
        0x9000: (0x101f, None, None, 4, "PidLidCategories", "dispidCategories", None),
        0x823c: (0x001f, None, None, 4, "PidLidCcAttendeesString", "dispidCCAttendeesString", None),
        0x8204: (0x0003, None, None, 4, "PidLidChangeHighlight", "dispidChangeHighlight", None),
        0x85b6: (0x001f, None, None, 4, "PidLidClassification", "dispidClassification", None),
        0x85b7: (0x001f, None, None, 4, "PidLidClassificationDescription", "dispidClassDesc", None),
        0x85b8: (0x001f, None, None, 4, "PidLidClassificationGuid", "dispidClassGuid", None),
        0x85ba: (0x000b, None, None, 4, "PidLidClassificationKeep", "dispidClassKeep", None),
        0x85b5: (0x000b, None, None, 4, "PidLidClassified", "dispidClassified", None),
        0x0023: (0x000b, None, None, 4, "PidTagOriginatorDeliveryReportRequested", "PR_ORIGINATOR_DELIVERY_REPORT_REQUESTED", "OriginatorDeliveryReportRequested"),
        0x0015: (0x0040, None, None, 4, "PidTagExpiryTime", "PR_EXPIRY_TIME", "ExpiryTime"),
        0x8236: (0x0040, None, None, 4, "PidLidClipEnd", "dispidClipEnd", None),
        0x8235: (0x0040, None, None, 4, "PidLidClipStart", "dispidClipStart", None),
        0x8247: (0x001f, None, None, 4, "PidLidCollaborateDoc", "dispidCollaborateDoc", None),
        0x8517: (0x0040, None, None, 4, "PidLidCommonEnd", "dispidCommonEnd", None),
        0x8516: (0x0040, None, None, 4, "PidLidCommonStart", "dispidCommonStart", None),
        0x8539: (0x101f, None, None, 4, "PidLidCompanies", "dispidCompanies", None),
        0x8240: (0x000b, None, None, 4, "PidLidConferencingCheck", "dispidConfCheck", None),
        0x8241: (0x0003, None, None, 4, "PidLidConferencingType", "dispidConfType", None),
        0x8585: (0x0102, None, None, 4, "PidLidContactLinkEntry", "dispidContactLinkEntry", None),
        0x8586: (0x001f, None, None, 4, "PidLidContactLinkName", "dispidContactLinkName", None),
        0x8584: (0x0102, None, None, 4, "PidLidContactLinkSearchKey", "dispidContactLinkSearchKey", None),
        0x853a: (0x101f, None, None, 4, "PidLidContacts", "dispidContacts", None),
        0x8050: (0x001f, None, None, 4, "PidLidContactUserField2", "dispidContactUserField2", None),
        0x85ca: (0x0040, None, None, 4, "PidLidConversationActionLastAppliedTime", "dispidConvActionLastAppliedTime", None),
        0x85c8: (0x0040, None, None, 4, "PidLidConversationActionMaxDeliveryTime", "dispidConvActionMaxDeliveryTime", None),
        0x85c6: (0x0102, None, None, 4, "PidLidConversationActionMoveFolderEid", "dispidConvActionMoveFolderEid", None),
        0x85c7: (0x0102, None, None, 4, "PidLidConversationActionMoveStoreEid", "dispidConvActionMoveStoreEid", None),
        0x85cb: (0x0003, None, None, 4, "PidLidConversationActionVersion", "dispidConvActionVersion", None),
        0x85c9: (0x0003, None, None, 4, "PidLidConversationProcessed", "dispidConvExLegacyProcessedRand", None),
        0x8552: (0x0003, None, None, 4, "PidLidCurrentVersion", "dispidCurrentVersion", None),
        0x8554: (0x001f, None, None, 4, "PidLidCurrentVersionName", "dispidCurrentVersionName", None),
        0x0011: (0x0002, None, None, 4, "PidLidDayInterval", "LID_DAY_INTERVAL", "DiscardReason"),
        0x1000: (0x001f, None, None, 4, "PidTagBody", "PR_BODY", "Body"),
        0x0009: (0x000b, None, None, 4, "PidLidDelegateMail", "LID_DELEGATE_MAIL", "ContentLength"),
        0x8242: (0x001f, None, None, 4, "PidLidDirectory", "dispidDirectory", None),
        0x000f: (0x0040, None, None, 4, "PidTagDeferredDeliveryTime", "PR_DEFERRED_DELIVERY_TIME", "DeferredDeliveryTime"),
        0x0010: (0x0040, None, None, 4, "PidTagDeliverTime", "PR_DELIVER_TIME", "DeliverTime"),
        0x8228: (0x0040, None, None, 4, "PidLidExceptionReplaceTime", "dispidExceptionReplaceTime", None),
        0x822b: (0x000b, None, None, 4, "PidLidFExceptionalAttendees", "dispidFExceptionalAttendees", None),
        0x8206: (0x000b, None, None, 4, "PidLidFExceptionalBody", "dispidFExceptionalBody", None),
        0x8229: (0x000b, None, None, 4, "PidLidFInvited", "dispidFInvited", None),
        0x8530: (0x001f, None, None, 4, "PidLidFlagRequest", "dispidRequest", None),
        0x85c0: (0x0003, None, None, 4, "PidLidFlagString", "dispidFlagStringEnum", None),
        0x820a: (0x000b, None, None, 4, "PidLidForwardInstance", "dispidFwrdInstance", None),
        0x8261: (0x0102, None, None, 4, "PidLidForwardNotificationRecipients", "dispidForwardNotificationRecipients", None),
        0x822f: (0x000b, None, None, 4, "PidLidFOthersAppointment", "dispidFOthersAppt", None),
        0x0003: (0x0102, None, None, 4, "PidLidGlobalObjectId", "LID_GLOBAL_OBJID", "AuthorizingUsers"),
        0x801a: (0x001f, None, None, 4, "PidLidHomeAddress", "dispidHomeAddress", None),
        0x802b: (0x001f, None, None, 4, "PidLidHtml", "dispidHTML", None),
        0x1001: (0x001f, None, None, 4, "PidTagReportText", "PR_REPORT_TEXT", "ReportText"),
        0x827a: (0x0102, None, None, 4, "PidLidInboundICalStream", "InboundICalStream", None),
        0x85b1: (0x001f, None, None, 4, "PidLidInfoPathFormName", None, None),
        0x8224: (0x0003, None, None, 4, "PidLidIntendedBusyStatus", "dispidIntendedBusyStatus", None),
        0x8580: (0x001f, None, None, 4, "PidLidInternetAccountName", "dispidInetAcctName", None),
        0x8581: (0x001f, None, None, 4, "PidLidInternetAccountStamp", "dispidInetAcctStamp", None),
        0x000a: (0x000b, None, None, 4, "PidLidIsException", "LID_IS_EXCEPTION", "ContentReturnRequested"),
        0x0005: (0x000b, None, None, 4, "PidTagAutoForwarded", "PR_AUTO_FORWARDED", "AutoForwarded"),
        0x0004: (0x0102, None, None, 4, "PidTagScriptData", "PR_EMS_SCRIPT_BLOB", "AutoForwardComment"),
        0x820c: (0x1102, None, None, 4, "PidLidLinkedTaskItems", "dispidLinkedTaskItems", None),
        0x8208: (0x001f, None, None, 4, "PidLidLocation", "dispidLocation", None),
        0x8711: (0x000b, None, None, 4, "PidLidLogDocumentPosted", "dispidLogDocPosted", None),
        0x870e: (0x000b, None, None, 4, "PidLidLogDocumentPrinted", "dispidLogDocPrinted", None),
        0x8710: (0x000b, None, None, 4, "PidLidLogDocumentRouted", "dispidLogDocRouted", None),
        0x870f: (0x000b, None, None, 4, "PidLidLogDocumentSaved", "dispidLogDocSaved", None),
        0x8707: (0x0003, None, None, 4, "PidLidLogDuration", "dispidLogDuration", None),
        0x8708: (0x0040, None, None, 4, "PidLidLogEnd", "dispidLogEnd", None),
        0x870c: (0x0003, None, None, 4, "PidLidLogFlags", "dispidLogFlags", None),
        0x8706: (0x0040, None, None, 4, "PidLidLogStart", "dispidLogStart", None),
        0x8700: (0x001f, None, None, 4, "PidLidLogType", "dispidLogType", None),
        0x8712: (0x001f, None, None, 4, "PidLidLogTypeDesc", "dispidLogTypeDesc", None),
        0x0026: (0x0003, None, None, 4, "PidTagPriority", "PR_PRIORITY", "Priority"),
        0x8209: (0x001f, None, None, 4, "PidLidMeetingWorkspaceUrl", "dispidMWSURL", None),
        0x0013: (0x0002, None, None, 4, "PidLidMonthInterval", "LID_MONTH_INTERVAL", "DlExpansionHistory"),
        0x1006: (0x0003, None, None, 4, "PidLidMonthOfYear", None, "RtfSyncBodyCrc"),
        0x0017: (0x0003, None, None, 4, "PidTagImportance", "PR_IMPORTANCE", "Importance"),
        0x8248: (0x001f, None, None, 4, "PidLidNetShowUrl", "dispidNetShowURL", None),
        0x100b: (0x000b, None, None, 4, "PidLidNoEndDateFlag", "http://schemas.microsoft.com/mapi/fnoenddate", "IsIntegJobProgress"),
        0x8538: (0x001f, None, None, 4, "PidLidNonSendableBcc", "dispidNonSendableBCC", None),
        0x8537: (0x001f, None, None, 4, "PidLidNonSendableCc", "dispidNonSendableCC", None),
        0x8536: (0x001f, None, None, 4, "PidLidNonSendableTo", "dispidNonSendableTo", None),
        0x8545: (0x1003, None, None, 4, "PidLidNonSendBccTrackStatus", "dispidNonSendBccTrackStatus", None),
        0x8544: (0x1003, None, None, 4, "PidLidNonSendCcTrackStatus", "dispidNonSendCcTrackStatus", None),
        0x8543: (0x1003, None, None, 4, "PidLidNonSendToTrackStatus", "dispidNonSendToTrackStatus", None),
        0x8b00: (0x0003, None, None, 4, "PidLidNoteColor", "dispidNoteColor", None),
        0x8b03: (0x0003, None, None, 4, "PidLidNoteHeight", "dispidNoteHeight", None),
        0x8b02: (0x0003, None, None, 4, "PidLidNoteWidth", "dispidNoteWidth", None),
        0x8b04: (0x0003, None, None, 4, "PidLidNoteX", "dispidNoteX", None),
        0x8b05: (0x0003, None, None, 4, "PidLidNoteY", "dispidNoteY", None),
        0x1005: (0x0003, None, None, 4, "PidLidOccurrences", None, "IsIntegJobCreationTime"),
        0x0028: (0x001f, None, None, 4, "PidLidOldLocation", "dispidOldLocation", "ProofOfSubmissionRequested"),
        0x0018: (0x0002, None, None, 4, "PidLidOldRecurrenceType", "LID_RECUR_TYPE", "IpmId"),
        0x002a: (0x0040, None, None, 4, "PidTagReceiptTime", "PR_RECEIPT_TIME", "ReceiptTime"),
        0x0029: (0x000b, None, None, 4, "PidTagReadReceiptRequested", "PR_READ_RECEIPT_REQUESTED", "ReadReceiptRequested"),
        0x8249: (0x001f, None, None, 4, "PidLidOnlinePassword", "dispidOnlinePassword", None),
        0x0007: (0x001f, None, None, 4, "PidLidOptionalAttendees", "LID_OPTIONAL_ATTENDEES", "ContentCorrelator"),
        0x8243: (0x001f, None, None, 4, "PidLidOrganizerAlias", "dispidOrgAlias", None),
        0x8237: (0x0102, None, None, 4, "PidLidOriginalStoreEntryId", "dispidOrigStoreEid", None),
        0x801c: (0x001f, None, None, 4, "PidLidOtherAddress", "dispidOtherAddress", None),
        0x001a: (0x001f, None, None, 4, "PidTagMessageClass", "PR_MESSAGE_CLASS", "MessageClass"),
        0x822e: (0x001f, None, None, 4, "PidLidOwnerName", "dispidOwnerName", None),
        0x85e0: (0x0003, None, None, 4, "PidLidPendingStateForSiteMailboxDocument", "dispidPendingStateforTMDocument", None),
        0x8022: (0x0003, None, None, 4, "PidLidPostalAddressId", "dispidPostalAddressId", None),
        0x8904: (0x001f, None, None, 4, "PidLidPostRssChannel", "dispidPostRssChannel", None),
        0x8900: (0x001f, None, None, 4, "PidLidPostRssChannelLink", "dispidPostRssChannelLink", None),
        0x8903: (0x001f, None, None, 4, "PidLidPostRssItemGuid", "dispidPostRssItemGuid", None),
        0x8902: (0x0003, None, None, 4, "PidLidPostRssItemHash", "dispidPostRssItemHash", None),
        0x8901: (0x001f, None, None, 4, "PidLidPostRssItemLink", "dispidPostRssItemLink", None),
        0x8905: (0x001f, None, None, 4, "PidLidPostRssItemXml", "dispidPostRssItemXml", None),
        0x8906: (0x001f, None, None, 4, "PidLidPostRssSubscription", "dispidPostRssSubscription", None),
        0x8506: (0x000b, None, None, 4, "PidLidPrivate", "dispidPrivate", None),
        0x100d: (0x0003, None, None, 4, "PidLidRecurrenceDuration", None, "IsIntegJobSource"),
        0x8232: (0x001f, None, None, 4, "PidLidRecurrencePattern", "dispidRecurPattern", None),
        0x8231: (0x0003, None, None, 4, "PidLidRecurrenceType", "dispidRecurType", None),
        0x8223: (0x000b, None, None, 4, "PidLidRecurring", "dispidRecurring", None),
        0x85bd: (0x0102, None, None, 4, "PidLidReferenceEntryId", "dispidReferenceEID", None),
        0x8501: (0x0003, None, None, 4, "PidLidReminderDelta", "dispidReminderDelta", None),
        0x851f: (0x001f, None, None, 4, "PidLidReminderFileParameter", "dispidReminderFileParam", None),
        0x851c: (0x000b, None, None, 4, "PidLidReminderOverride", "dispidReminderOverride", None),
        0x851e: (0x000b, None, None, 4, "PidLidReminderPlaySound", "dispidReminderPlaySound", None),
        0x8503: (0x000b, None, None, 4, "PidLidReminderSet", "dispidReminderSet", None),
        0x8560: (0x0040, None, None, 4, "PidLidReminderSignalTime", "dispidReminderNextTime", None),
        0x8502: (0x0040, None, None, 4, "PidLidReminderTime", "dispidReminderTime", None),
        0x8505: (0x0040, None, None, 4, "PidLidReminderTimeDate", "dispidReminderTimeDate", None),
        0x8504: (0x0040, None, None, 4, "PidLidReminderTimeTime", "dispidReminderTimeTime", None),
        0x851d: (0x0003, None, None, 4, "PidLidReminderType", "dispidReminderType", None),
        0x8511: (0x0003, None, None, 4, "PidLidRemoteStatus", "dispidRemoteStatus", None),
        0x0006: (0x001f, None, None, 4, "PidLidRequiredAttendees", "LID_REQUIRED_ATTENDEES", "ContentConfidentialityAlgorithmId"),
        0x0008: (0x001f, None, None, 4, "PidLidResourceAttendees", "LID_RESOURCE_ATTENDEES", "ContentIdentifier"),
        0x8218: (0x0003, None, None, 4, "PidLidResponseStatus", "dispidResponseStatus", None),
        0x85cc: (0x000b, None, None, 4, "PidLidServerProcessed", "dispidExchangeProcessed", None),
        0x85cd: (0x0003, None, None, 4, "PidLidServerProcessingActions", "dispidExchangeProcessingAction", None),
        0x8a19: (0x0003, None, None, 4, "PidLidSharingAnonymity", "dispidSharingAnonymity", None),
        0x8a2d: (0x0102, None, None, 4, "PidLidSharingBindingEntryId", "dispidSharingBindingEid", None),
        0x8a51: (0x001f, None, None, 4, "PidLidSharingBrowseUrl", "dispidSharingBrowseUrl", None),
        0x8a17: (0x0003, None, None, 4, "PidLidSharingCapabilities", "dispidSharingCaps", None),
        0x8a24: (0x001f, None, None, 4, "PidLidSharingConfigurationUrl", "dispidSharingConfigUrl", None),
        0x8a45: (0x0040, None, None, 4, "PidLidSharingDataRangeEnd", "dispidSharingDataRangeEnd", None),
        0x8a44: (0x0040, None, None, 4, "PidLidSharingDataRangeStart", "dispidSharingDataRangeStart", None),
        0x8a2b: (0x0003, None, None, 4, "PidLidSharingDetail", "dispidSharingDetail", None),
        0x8a21: (0x001f, None, None, 4, "PidLidSharingExtensionXml", "dispidSharingExtXml", None),
        0x8a13: (0x0102, None, None, 4, "PidLidSharingFilter", "dispidSharingFilter", None),
        0x8a0a: (0x0003, None, None, 4, "PidLidSharingFlags", "dispidSharingFlags", None),
        0x8a18: (0x0003, None, None, 4, "PidLidSharingFlavor", "dispidSharingFlavor", None),
        0x8a15: (0x0102, None, None, 4, "PidLidSharingFolderEntryId", "dispidSharingFolderEid", None),
        0x8a2e: (0x0102, None, None, 4, "PidLidSharingIndexEntryId", "dispidSharingIndexEid", None),
        0x8a09: (0x0102, None, None, 4, "PidLidSharingInitiatorEntryId", "dispidSharingInitiatorEid", None),
        0x8a07: (0x001f, None, None, 4, "PidLidSharingInitiatorName", "dispidSharingInitiatorName", None),
        0x8a08: (0x001f, None, None, 4, "PidLidSharingInitiatorSmtp", "dispidSharingInitiatorSmtp", None),
        0x8a1c: (0x0102, None, None, 4, "PidLidSharingInstanceGuid", "dispidSharingInstanceGuid", None),
        0x8a55: (0x0040, None, None, 4, "PidLidSharingLastAutoSyncTime", "dispidSharingLastAutoSync", None),
        0x8a1f: (0x0040, None, None, 4, "PidLidSharingLastSyncTime", "dispidSharingLastSync", None),
        0x8a4d: (0x001f, None, None, 4, "PidLidSharingLocalComment", "dispidSharingLocalComment", None),
        0x8a23: (0x0040, None, None, 4, "PidLidSharingLocalLastModificationTime", "dispidSharingLocalLastMod", None),
        0x8a0f: (0x001f, None, None, 4, "PidLidSharingLocalName", "dispidSharingLocalName", None),
        0x8a0e: (0x001f, None, None, 4, "PidLidSharingLocalPath", "dispidSharingLocalPath", None),
        0x8a49: (0x001f, None, None, 4, "PidLidSharingLocalStoreUid", "dispidSharingLocalStoreUid", None),
        0x8a14: (0x001f, None, None, 4, "PidLidSharingLocalType", "dispidSharingLocalType", None),
        0x8a10: (0x001f, None, None, 4, "PidLidSharingLocalUid", "dispidSharingLocalUid", None),
        0x8a29: (0x0102, None, None, 4, "PidLidSharingOriginalMessageEntryId", "dispidSharingOriginalMessageEid", None),
        0x8a5c: (0x0102, None, None, 4, "PidLidSharingParentBindingEntryId", "dispidSharingParentBindingEid", None),
        0x8a1e: (0x001f, None, None, 4, "PidLidSharingParticipants", "dispidSharingParticipants", None),
        0x8a1b: (0x0003, None, None, 4, "PidLidSharingPermissions", "dispidSharingPermissions", None),
        0x8a0b: (0x001f, None, None, 4, "PidLidSharingProviderExtension", "dispidSharingProviderExtension", None),
        0x8a01: (0x0102, None, None, 4, "PidLidSharingProviderGuid", "dispidSharingProviderGuid", None),
        0x8a02: (0x001f, None, None, 4, "PidLidSharingProviderName", "dispidSharingProviderName", None),
        0x8a03: (0x001f, None, None, 4, "PidLidSharingProviderUrl", "dispidSharingProviderUrl", None),
        0x8a47: (0x0003, None, None, 4, "PidLidSharingRangeEnd", "dispidSharingRangeEnd", None),
        0x8a46: (0x0003, None, None, 4, "PidLidSharingRangeStart", "dispidSharingRangeStart", None),
        0x8a1a: (0x0003, None, None, 4, "PidLidSharingReciprocation", "dispidSharingReciprocation", None),
        0x8a4b: (0x0003, None, None, 4, "PidLidSharingRemoteByteSize", "dispidSharingRemoteByteSize", None),
        0x8a2f: (0x001f, None, None, 4, "PidLidSharingRemoteComment", "dispidSharingRemoteComment", None),
        0x8a4c: (0x0003, None, None, 4, "PidLidSharingRemoteCrc", "dispidSharingRemoteCrc", None),
        0x8a22: (0x0040, None, None, 4, "PidLidSharingRemoteLastModificationTime", "dispidSharingRemoteLastMod", None),
        0x8a4f: (0x0003, None, None, 4, "PidLidSharingRemoteMessageCount", "dispidSharingRemoteMsgCount", None),
        0x8a05: (0x001f, None, None, 4, "PidLidSharingRemoteName", "dispidSharingRemoteName", None),
        0x8a0d: (0x001f, None, None, 4, "PidLidSharingRemotePass", "dispidSharingRemotePass", None),
        0x8a04: (0x001f, None, None, 4, "PidLidSharingRemotePath", "dispidSharingRemotePath", None),
        0x8a48: (0x001f, None, None, 4, "PidLidSharingRemoteStoreUid", "dispidSharingRemoteStoreUid", None),
        0x8a1d: (0x001f, None, None, 4, "PidLidSharingRemoteType", "dispidSharingRemoteType", None),
        0x8a06: (0x001f, None, None, 4, "PidLidSharingRemoteUid", "dispidSharingRemoteUid", None),
        0x8a0c: (0x001f, None, None, 4, "PidLidSharingRemoteUser", "dispidSharingRemoteUser", None),
        0x8a5b: (0x001f, None, None, 4, "PidLidSharingRemoteVersion", "dispidSharingRemoteVersion", None),
        0x8a28: (0x0040, None, None, 4, "PidLidSharingResponseTime", "dispidSharingResponseTime", None),
        0x8a27: (0x0003, None, None, 4, "PidLidSharingResponseType", "dispidSharingResponseType", None),
        0x8a4e: (0x0003, None, None, 4, "PidLidSharingRoamLog", "dispidSharingRoamLog", None),
        0x8a25: (0x0040, None, None, 4, "PidLidSharingStart", "dispidSharingStart", None),
        0x8a00: (0x0003, None, None, 4, "PidLidSharingStatus", "dispidSharingStatus", None),
        0x8a26: (0x0040, None, None, 4, "PidLidSharingStop", "dispidSharingStop", None),
        0x8a60: (0x0003, None, None, 4, "PidLidSharingSyncFlags", "dispidSharingSyncFlags", None),
        0x8a2a: (0x0003, None, None, 4, "PidLidSharingSyncInterval", "dispidSharingSyncInterval", None),
        0x8a2c: (0x0003, None, None, 4, "PidLidSharingTimeToLive", "dispidSharingTimeToLive", None),
        0x8a56: (0x0003, None, None, 4, "PidLidSharingTimeToLiveAuto", "dispidSharingTimeToLiveAuto", None),
        0x8a42: (0x0003, None, None, 4, "PidLidSharingWorkingHoursDays", "dispidSharingWorkingHoursDays", None),
        0x8a41: (0x0040, None, None, 4, "PidLidSharingWorkingHoursEnd", "dispidSharingWorkingHoursEnd", None),
        0x8a40: (0x0040, None, None, 4, "PidLidSharingWorkingHoursStart", "dispidSharingWorkingHoursStart", None),
        0x8a43: (0x0102, None, None, 4, "PidLidSharingWorkingHoursTimeZone", "dispidSharingWorkingHoursTZ", None),
        0x8510: (0x0003, None, None, 4, "PidLidSideEffects", "dispidSideEffects", None),
        0x827b: (0x000b, None, None, 4, "PidLidSingleBodyICal", "IsSingleBodyICal", None),
        0x8514: (0x000b, None, None, 4, "PidLidSmartNoAttach", "dispidSmartNoAttach", None),
        0x859c: (0x0102, None, None, 4, "PidLidSpamOriginalFolder", "dispidSpamOriginalFolder", None),
        0x000d: (0x0003, None, None, 4, "PidLidStartRecurrenceDate", "LID_START_RECUR_DATE", "ConversionWithLossProhibited"),
        0x000e: (0x0003, None, None, 4, "PidLidStartRecurrenceTime", "LID_START_RECUR_TIME", "ConvertedEits"),
        0x811c: (0x000b, None, None, 4, "PidLidTaskComplete", "dispidTaskComplete", None),
        0x8519: (0x0102, None, None, 4, "PidLidTaskGlobalId", "dispidTaskGlobalObjId", None),
        0x8518: (0x0003, None, None, 4, "PidLidTaskMode", "dispidTaskMode", None),
        0x000c: (0x0003, None, None, 4, "PidLidTimeZone", "LID_TIME_ZONE", "ConversionEits"),
        0x8234: (0x001f, None, None, 4, "PidLidTimeZoneDescription", "dispidTimeZoneDesc", None),
        0x8233: (0x0102, None, None, 4, "PidLidTimeZoneStruct", "dispidTimeZoneStruct", None),
        0x823b: (0x001f, None, None, 4, "PidLidToAttendeesString", "dispidToAttendeesString", None),
        0x85a0: (0x0040, None, None, 4, "PidLidToDoOrdinalDate", "dispidToDoOrdinalDate", None),
        0x85a1: (0x001f, None, None, 4, "PidLidToDoSubOrdinal", "dispidToDoSubOrdinal", None),
        0x85a4: (0x001f, None, None, 4, "PidLidToDoTitle", "dispidToDoTitle", None),
        0x8582: (0x000b, None, None, 4, "PidLidUseTnef", "dispidUseTNEF", None),
        0x85bf: (0x0040, None, None, 4, "PidLidValidFlagStringProof", "dispidValidFlagStringProof", None),
        0x8524: (0x001f, None, None, 4, "PidLidVerbResponse", "dispidVerbResponse", None),
        0x8520: (0x0102, None, None, 4, "PidLidVerbStream", "dispidVerbStream", None),
        0x0012: (0x0002, None, None, 4, "PidLidWeekInterval", "LID_WEEK_INTERVAL", "DisclosureOfRecipients"),
        0x0002: (0x000b, None, None, 4, "PidTagAlternateRecipientAllowed", "PR_ALTERNATE_RECIPIENT_ALLOWED", "AlternateRecipientAllowed"),
        0x801b: (0x001f, None, None, 4, "PidLidWorkAddress", "dispidWorkAddress", None),
        0x0014: (0x0002, None, None, 4, "PidLidYearInterval", "LID_YEAR_INTERVAL", "DlExpansionProhibited"),
        0x802c: (0x001f, None, None, 4, "PidLidYomiFirstName", "dispidYomiFirstName", None),
        0x0ff4: (0x0003, None, None, 4, "PidTagAccess", "PR_ACCESS", "Access"),
        0x3fe0: (0x0102, None, None, 4, "PidTagAccessControlListData", "PR_ACL_DATA", "AclTable"),
        0x0ff7: (0x0003, None, None, 4, "PidTagAccessLevel", "PR_ACCESS_LEVEL", "AccessLevel"),
        0x36d8: (0x1102, None, None, 4, "PidTagAdditionalRenEntryIds", "PR_ADDITIONAL_REN_ENTRYIDS", "AdditionalRenEntryIds"),
        0x36d9: (0x0102, None, None, 4, "PidTagAdditionalRenEntryIdsEx", "PR_ADDITIONAL_REN_ENTRYIDS_EX", "AdditionalRenEntryIdsEx"),
        0xfffd: (0x0003, None, None, 4, "PidTagAddressBookContainerId", "PR_EMS_AB_CONTAINERID", "AbContainerId"),
        0x8c93: (0x0003, None, None, 4, "PidTagAddressBookDisplayTypeExtended", "PR_EMS_AB_DISPLAY_TYPE_EX", None),
        0x663b: (0x0102, None, None, 4, "PidTagAddressBookEntryId", "PR_ADDRESS_BOOK_ENTRYID", "AddressBookEntryId"),
        0xfffb: (0x000b, None, None, 4, "PidTagAddressBookIsMaster", "PR_EMS_AB_IS_MASTER", "AbIsMaster"),
        0x6704: (0x000d, None, None, 4, "PidTagAddressBookManageDistributionList", "PR_EMS_AB_MANAGE_DL", "ClientVersion"),
        0x674f: (0x0014, None, None, 4, "PidTagAddressBookMessageId", "ptagAddrbookMID", None),
        0xfffc: (0x0102, None, None, 4, "PidTagAddressBookParentEntryId", "PR_EMS_AB_PARENT_ENTRYID", "AbParentEntryId"),
        0x3002: (0x001f, None, None, 4, "PidTagAddressType", "PR_ADDRTYPE", "AddrType"),
        0x360c: (0x001f, None, None, 4, "PidTagAnr", "PR_ANR", "Anr"),
        0x301f: (0x0040, None, None, 4, "PidTagArchiveDate", "PR_ARCHIVE_DATE", "ArchiveDate"),
        0x301e: (0x0003, None, None, 4, "PidTagArchivePeriod", "PR_ARCHIVE_PERIOD", "ArchivePeriod"),
        0x3018: (0x0102, None, None, 4, "PidTagArchiveTag", "PR_ARCHIVE_TAG", "ArchiveTag"),
        0x67aa: (0x000b, None, None, 4, "PidTagAssociated", "ptagAssociated", "Associated"),
        0x370f: (0x0102, None, None, 4, "PidTagAttachAdditionalInformation", "PR_ATTACH_ADDITIONAL_INFO", "AttachAdditionalInfo"),
        0x3711: (0x001f, None, None, 4, "PidTagAttachContentBase", None, "AttachmentContentBase"),
        0x3712: (0x001f, None, None, 4, "PidTagAttachContentId", "PR_ATTACH_CONTENT_ID", "AttachmentContentId"),
        0x3713: (0x001f, None, None, 4, "PidTagAttachContentLocation", "PR_ATTACH_CONTENT_LOCATION", "AttachContentLocation"),
        0x3701: (0x000d, None, None, 4, "PidTagAttachDataObject", "PR_ATTACH_DATA_OBJ", "AttachDataObj"),
        0x3702: (0x0102, None, None, 4, "PidTagAttachEncoding", "PR_ATTACH_ENCODING", "AttachEncoding"),
        0x3703: (0x001f, None, None, 4, "PidTagAttachExtension", "PR_ATTACH_EXTENSION", "AttachExtension"),
        0x3704: (0x001f, None, None, 4, "PidTagAttachFilename", "PR_ATTACH_FILENAME", "AttachFileName"),
        0x3714: (0x0003, None, None, 4, "PidTagAttachFlags", "PR_ATTACH_FLAGS", "AttachFlags"),
        0x3707: (0x001f, None, None, 4, "PidTagAttachLongFilename", "PR_ATTACH_LONG_FILENAME", "AttachLongFileName"),
        0x370d: (0x001f, None, None, 4, "PidTagAttachLongPathname", "PR_ATTACH_LONG_PATHNAME", "AttachLongPathName"),
        0x7fff: (0x000b, None, None, 4, "PidTagAttachmentContactPhoto", "PR_ATTACHMENT_CONTACTPHOTO", "IsContactPhoto"),
        0x7ffd: (0x0003, None, None, 4, "PidTagAttachmentFlags", "PR_ATTACHMENT_FLAGS", "AttachmentCalendarFlags"),
        0x7ffe: (0x000b, None, None, 4, "PidTagAttachmentHidden", "PR_ATTACHMENT_HIDDEN", "AttachmentCalendarHidden"),
        0x7ffa: (0x0003, None, None, 4, "PidTagAttachmentLinkId", "PR_ATTACHMENT_LINKID", "AttachmentCalendarLinkId"),
        0x3705: (0x0003, None, None, 4, "PidTagAttachMethod", "PR_ATTACH_METHOD", "AttachMethod"),
        0x370e: (0x001f, None, None, 4, "PidTagAttachMimeTag", "PR_ATTACH_MIME_TAG", "AttachMimeTag"),
        0x0e21: (0x0003, None, None, 4, "PidTagAttachNumber", "PR_ATTACH_NUM", "AttachNum"),
        0x3708: (0x001f, None, None, 4, "PidTagAttachPathname", "PR_ATTACH_PATHNAME", "AttachPathName"),
        0x371a: (0x001f, None, None, 4, "PidTagAttachPayloadClass", "PR_ATTACH_PAYLOAD_CLASS", "AttachmentPayloadClass"),
        0x3719: (0x001f, None, None, 4, "PidTagAttachPayloadProviderGuidString", "PR_ATTACH_PAYLOAD_PROV_GUID_STR", "AttachmentPayloadProviderGuidString"),
        0x3709: (0x0102, None, None, 4, "PidTagAttachRendering", "PR_ATTACH_RENDERING", "AttachRendering"),
        0x0e20: (0x0003, None, None, 4, "PidTagAttachSize", "PR_ATTACH_SIZE", "AttachSize"),
        0x370a: (0x0102, None, None, 4, "PidTagAttachTag", "PR_ATTACH_TAG", "AttachTag"),
        0x370c: (0x001f, None, None, 4, "PidTagAttachTransportName", "PR_ATTACH_TRANSPORT_NAME", "AttachTransportName"),
        0x10f4: (0x000b, None, None, 4, "PidTagAttributeHidden", "PR_ATTR_HIDDEN", "AttrHidden"),
        0x10f6: (0x000b, None, None, 4, "PidTagAttributeReadOnly", "PR_ATTR_READONLY", "AttrReadOnly"),
        0x3fdf: (0x0003, None, None, 4, "PidTagAutoResponseSuppress", "PR_AUTO_RESPONSE_SUPPRESS", "AutoResponseSuppress"),
        0x3a42: (0x0040, None, None, 4, "PidTagBirthday", "PR_BIRTHDAY", "Birthday"),
        0x1096: (0x0003, None, None, 4, "PidTagBlockStatus", "PR_BLOCK_STATUS", "BlockStatus"),
        0x1015: (0x001f, None, None, 4, "PidTagBodyContentId", "PR_BODY_CONTENT_ID", "BodyContentId"),
        0x1014: (0x001f, None, None, 4, "PidTagBodyContentLocation", "PR_BODY_CONTENT_LOCATION", "BodyContentLocation"),
        0x1013: (0x0102, None, None, 4, "PidTagHtml", "PR_HTML", "BodyHtml"),
        0x3a24: (0x001f, None, None, 4, "PidTagBusinessFaxNumber", "PR_BUSINESS_FAX_NUMBER", "BusinessFaxNumber"),
        0x3a51: (0x001f, None, None, 4, "PidTagBusinessHomePage", "PR_BUSINESS_HOME_PAGE", "BusinessHomePage"),
        0x3a02: (0x001f, None, None, 4, "PidTagCallbackTelephoneNumber", "PR_CALLBACK_TELEPHONE_NUMBER", "CallbackTelephoneNumber"),
        0x6806: (0x001f, None, None, 4, "PidTagCallId", "InternalSchemaCallID", "MailboxMiscFlags"),
        0x3a1e: (0x001f, None, None, 4, "PidTagCarTelephoneNumber", "PR_CAR_TELEPHONE_NUMBER", "CarTelephoneNumber"),
        0x10c5: (0x0040, None, None, 4, "PidTagCdoRecurrenceid", "PR_CDO_RECURRENCEID", None),
        0x65e2: (0x0102, None, None, 4, "PidTagChangeKey", "PR_CHANGE_KEY", "ChangeKey"),
        0x67a4: (0x0014, None, None, 4, "PidTagChangeNumber", "ptagCn", "Cn"),
        0x3a58: (0x101f, None, None, 4, "PidTagChildrensNames", "PR_CHILDRENS_NAMES", "ChildrensNames"),
        0x6645: (0x0102, None, None, 4, "PidTagClientActions", "PR_CLIENT_ACTIONS", "PromotedProperties"),
        0x0039: (0x0040, None, None, 4, "PidTagClientSubmitTime", "PR_CLIENT_SUBMIT_TIME", "ClientSubmitTime"),
        0x66c3: (0x0003, None, None, 4, "PidTagCodePageId", "PR_CODE_PAGE_ID", "CodePageId"),
        0x3a57: (0x001f, None, None, 4, "PidTagCompanyMainTelephoneNumber", "PR_COMPANY_MAIN_PHONE_NUMBER", "CompanyMainPhoneNumber"),
        0x3a49: (0x001f, None, None, 4, "PidTagComputerNetworkName", "PR_COMPUTER_NETWORK_NAME", "ComputerNetworkName"),
        0x3ff0: (0x0102, None, None, 4, "PidTagConflictEntryId", "PR_CONFLICT_ENTRYID", "BackfillTimeout"),
        0x3613: (0x001f, None, None, 4, "PidTagContainerClass", "PR_CONTAINER_CLASS", "ContainerClass"),
        0x360f: (0x000d, None, None, 4, "PidTagContainerContents", "PR_CONTAINER_CONTENTS", "ContainerContents"),
        0x3600: (0x0003, None, None, 4, "PidTagContainerFlags", "PR_CONTAINER_FLAGS", "ContainerFlags"),
        0x360e: (0x000d, None, None, 4, "PidTagContainerHierarchy", "PR_CONTAINER_HIERARCHY", "ContainerHierarchy"),
        0x3602: (0x0003, None, None, 4, "PidTagContentCount", "PR_CONTENT_COUNT", "ContentCount"),
        0x4076: (0x0003, None, None, 4, "PidTagContentFilterSpamConfidenceLevel", "PR_CONTENT_FILTER_SCL", "SpamConfidenceLevel"),
        0x3603: (0x0003, None, None, 4, "PidTagContentUnreadCount", "PR_CONTENT_UNREAD", "ContentUnread"),
        0x3013: (0x0102, None, None, 4, "PidTagConversationId", "PR_CONVERSATION_ID", "ConversationId"),
        0x0071: (0x0102, None, None, 4, "PidTagConversationIndex", "PR_CONVERSATION_INDEX", "ConversationIndex"),
        0x3016: (0x000b, None, None, 4, "PidTagConversationIndexTracking", "PR_CONVERSATION_INDEX_TRACKING", "ConversationIndexTracking"),
        0x0070: (0x001f, None, None, 4, "PidTagConversationTopic", "PR_CONVERSATION_TOPIC", "ConversationTopic"),
        0x3ff9: (0x0102, None, None, 4, "PidTagCreatorEntryId", "PR_CREATOR_ENTRYID", "CreatorEntryId"),
        0x3ff8: (0x001f, None, None, 4, "PidTagCreatorName", "PR_CREATOR_NAME", "CreatorName"),
        0x3a4a: (0x001f, None, None, 4, "PidTagCustomerId", "PR_CUSTOMER_ID", "CustomerId"),
        0x6647: (0x000b, None, None, 4, "PidTagDamBackPatched", "PR_DAM_BACK_PATCHED", "DeferredActionMessageBackPatched"),
        0x6646: (0x0102, None, None, 4, "PidTagDamOriginalEntryId", "PR_DAM_ORIGINAL_ENTRYID", "HiddenPromotedProperties"),
        0x36e5: (0x001f, None, None, 4, "PidTagDefaultPostMessageClass", "PR_DEF_POST_MSGCLASS", "DefaultPostMsgClass"),
        0x6741: (0x00fb, None, None, 4, "PidTagDeferredActionMessageOriginalEntryId", "PR_DAM_ORIG_MSG_SVREID", "OriginalMessageSvrEId"),
        0x3feb: (0x0003, None, None, 4, "PidTagDeferredSendNumber", "PR_DEFERRED_SEND_NUMBER", "DeferredSendNumber"),
        0x3fef: (0x0040, None, None, 4, "PidTagDeferredSendTime", "PR_DEFERRED_SEND_TIME", "DeferredSendTime"),
        0x3fec: (0x0003, None, None, 4, "PidTagDeferredSendUnits", "PR_DEFERRED_SEND_UNITS", "DeferredSendUnits"),
        0x3fe3: (0x000b, None, None, 4, "PidTagDelegatedByRule", "PR_DELEGATED_BY_RULE", "OofHistory"),
        0x686b: (0x1003, None, None, 4, "PidTagDelegateFlags", "PR_DELEGATE_FLAGS", "DelegateFlags"),
        0x0e01: (0x000b, None, None, 4, "PidTagDeleteAfterSubmit", "PR_DELETE_AFTER_SUBMIT", "DeleteAfterSubmit"),
        0x670b: (0x0003, None, None, 4, "PidTagDeletedCountTotal", "PR_DELETED_COUNT_TOTAL", "DeletedCountTotal"),
        0x668f: (0x0040, None, None, 4, "PidTagDeletedOn", "PR_DELETED_ON", "DeletedOn"),
        0x3005: (0x0003, None, None, 4, "PidTagDepth", "PR_DEPTH", "Depth"),
        0x0e02: (0x001f, None, None, 4, "PidTagDisplayBcc", "PR_DISPLAY_BCC", "DisplayBcc"),
        0x0e03: (0x001f, None, None, 4, "PidTagDisplayCc", "PR_DISPLAY_CC", "DisplayCc"),
        0x3001: (0x001f, None, None, 4, "PidTagDisplayName", "PR_DISPLAY_NAME", "DisplayName"),
        0x3a45: (0x001f, None, None, 4, "PidTagDisplayNamePrefix", "PR_DISPLAY_NAME_PREFIX", "DisplayNamePrefix"),
        0x0e04: (0x001f, None, None, 4, "PidTagDisplayTo", "PR_DISPLAY_TO", "DisplayTo"),
        0x3900: (0x0003, None, None, 4, "PidTagDisplayType", "PR_DISPLAY_TYPE", "DisplayType"),
        0x3003: (0x001f, None, None, 4, "PidTagEmailAddress", "PR_EMAIL_ADDRESS", "EmailAddress"),
        0x0061: (0x0040, None, None, 4, "PidTagEndDate", "PR_END_DATE", "EndDate"),
        0x0fff: (0x0102, None, None, 4, "PidTagEntryId", "PR_ENTRYID", "EntryId"),
        0x7ffc: (0x0040, None, None, 4, "PidTagExceptionEndTime", "PR_EXCEPTION_ENDTIME", "AppointmentExceptionEndTime"),
        0x7ff9: (0x0040, None, None, 4, "PidTagExceptionReplaceTime", "PR_EXCEPTION_REPLACETIME", "SExceptionReplaceTime"),
        0x7ffb: (0x0040, None, None, 4, "PidTagExceptionStartTime", "PR_EXCEPTION_STARTTIME", "AppointmentExceptionStartTime"),
        0x0e84: (0x0102, None, None, 4, "PidTagExchangeNTSecurityDescriptor", "http://schemas.microsoft.com/exchange/ntsecuritydescriptor", None),
        0x3fed: (0x0003, None, None, 4, "PidTagExpiryNumber", "PR_EXPIRY_NUMBER", "ExpiryNumber"),
        0x3fee: (0x0003, None, None, 4, "PidTagExpiryUnits", "PR_EXPIRY_UNITS", "ExpiryUnits"),
        0x36da: (0x0102, None, None, 4, "PidTagExtendedFolderFlags", "PR_EXTENDED_FOLDER_FLAGS", "ExtendedFolderFlags"),
        0x0e99: (0x0102, None, None, 4, "PidTagExtendedRuleMessageActions", "PR_EXTENDED_RULE_MSG_ACTIONS", "ExtendedRuleActions"),
        0x0e9a: (0x0102, None, None, 4, "PidTagExtendedRuleMessageCondition", "PR_EXTENDED_RULE_MSG_CONDITION", "ExtendedRuleCondition"),
        0x0e9b: (0x0003, None, None, 4, "PidTagExtendedRuleSizeLimit", "PR_EXTENDED_RULE_SIZE_LIMIT", "ExtendedRuleSizeLimit"),
        0x6804: (0x001e, None, None, 4, "PidTagOfflineAddressBookDistinguishedName", "PR_OAB_DN", "ShutoffQuota"),
        0x1091: (0x0040, None, None, 4, "PidTagFlagCompleteTime", "PR_FLAG_COMPLETE_TIME", "FlagCompleteTime"),
        0x1090: (0x0003, None, None, 4, "PidTagFlagStatus", "PR_FLAG_STATUS", "FlagStatus"),
        0x670e: (0x001f, None, None, 4, "PidTagFlatUrlName", "PR_FLAT_URL_NAME", None),
        0x3610: (0x000d, None, None, 4, "PidTagFolderAssociatedContents", "PR_FOLDER_ASSOCIATED_CONTENTS", "FolderAssociatedContents"),
        0x6748: (0x0014, None, None, 4, "PidTagFolderId", "ptagFID", "Fid"),
        0x66a8: (0x0003, None, None, 4, "PidTagFolderFlags", "PR_FOLDER_FLAGS", "FolderFlags"),
        0x3601: (0x0003, None, None, 4, "PidTagFolderType", "PR_FOLDER_TYPE", "FolderType"),
        0x1095: (0x0003, None, None, 4, "PidTagFollowupIcon", "PR_FOLLOWUP_ICON", "FollowupIcon"),
        0x6869: (0x0003, None, None, 4, "PidTagFreeBusyCountMonths", "PR_FREEBUSY_COUNT_MONTHS", "OutlookFreeBusyMonthCount"),
        0x36e4: (0x1102, None, None, 4, "PidTagFreeBusyEntryIds", "PR_FREEBUSY_ENTRYIDS", "FreeBusyEntryIds"),
        0x6849: (0x0003, None, None, 4, "PidTagWlinkType", None, "ScheduleInfoRecipientLegacyDn"),
        0x6848: (0x0003, None, None, 4, "PidTagSearchFolderEfpFlags", "PR_WB_SF_EFP_FLAGS", "AssociatedSearchFolderFlags"),
        0x6847: (0x0003, None, None, 4, "PidTagWlinkSaveStamp", None, "AssociatedSearchFolderTag"),
        0x6868: (0x0040, None, None, 4, "PidTagFreeBusyRangeTimestamp", "PR_FREEBUSY_RANGE_TIMESTAMP", None),
        0x3a4c: (0x001f, None, None, 4, "PidTagFtpSite", "PR_FTP_SITE", "FtpSite"),
        0x6846: (0x0003, None, None, 4, "PidTagSearchFolderStorageType", "PR_WB_SF_STORAGE_TYPE", "AssociatedSearchFolderStorageType"),
        0x3a4d: (0x0002, None, None, 4, "PidTagGender", "PR_GENDER", "Gender"),
        0x3a05: (0x001f, None, None, 4, "PidTagGeneration", "PR_GENERATION", "Generation"),
        0x3a07: (0x001f, None, None, 4, "PidTagGovernmentIdNumber", "PR_GOVERNMENT_ID_NUMBER", "GovernmentIdNumber"),
        0x0e1b: (0x000b, None, None, 4, "PidTagHasAttachments", "PR_HASATTACH", "Hasattach"),
        0x3fea: (0x000b, None, None, 4, "PidTagHasDeferredActionMessages", "PR_HAS_DAMS", "HasDeferredActionMessage"),
        0x664a: (0x000b, None, None, 4, "PidTagHasNamedProperties", "PR_HAS_NAMED_PROPERTIES", "HasNamedProperties"),
        0x663a: (0x000b, None, None, 4, "PidTagHasRules", "PR_HAS_RULES", "HasRules"),
        0x663e: (0x0003, None, None, 4, "PidTagHierarchyChangeNumber", "PR_HIERARCHY_CHANGE_NUM", "HierarchyChangeNumber"),
        0x4082: (0x0040, None, None, 4, "PidTagHierRev", "PR_HIER_REV", "HierRev"),
        0x3a43: (0x001f, None, None, 4, "PidTagHobbies", "PR_HOBBIES", "Hobbies"),
        0x3a59: (0x001f, None, None, 4, "PidTagHomeAddressCity", "PR_HOME_ADDRESS_CITY", "HomeAddressCity"),
        0x3a5a: (0x001f, None, None, 4, "PidTagHomeAddressCountry", "PR_HOME_ADDRESS_COUNTRY", "HomeAddressCountry"),
        0x3a5b: (0x001f, None, None, 4, "PidTagHomeAddressPostalCode", "PR_HOME_ADDRESS_POSTAL_CODE", "HomeAddressPostalCode"),
        0x3a5e: (0x001f, None, None, 4, "PidTagHomeAddressPostOfficeBox", "PR_HOME_ADDRESS_POST_OFFICE_BOX", "HomeAddressPostOfficeBox"),
        0x3a5c: (0x001f, None, None, 4, "PidTagHomeAddressStateOrProvince", "PR_HOME_ADDRESS_STATE_OR_PROVINCE", "HomeAddressStateOrProvince"),
        0x3a25: (0x001f, None, None, 4, "PidTagHomeFaxNumber", "PR_HOME_FAX_NUMBER", "HomeFaxNumber"),
        0x10c4: (0x0040, None, None, 4, "PidTagICalendarEndTime", "urn:schemas:calendar:dtend", None),
        0x10ca: (0x0040, None, None, 4, "PidTagICalendarReminderNextTime", "urn:schemas:calendar:remindernexttime", None),
        0x10c3: (0x0040, None, None, 4, "PidTagICalendarStartTime", "urn:schemas:calendar:dtstart", None),
        0x1080: (0x0003, None, None, 4, "PidTagIconIndex", "PR_ICON_INDEX", "IconIndex"),
        0x666c: (0x000b, None, None, 4, "PidTagInConflict", "PR_IN_CONFLICT", "AttachmentInConflict"),
        0x3f08: (0x0003, None, None, 4, "PidTagInitialDetailsPane", "PR_INITIAL_DETAILS_PANE", "InitialDetailsPane"),
        0x1042: (0x001f, None, None, 4, "PidTagInReplyToId", "PR_IN_REPLY_TO_ID", "InReplyTo"),
        0x0ff6: (0x0102, None, None, 4, "PidTagInstanceKey", "PR_INSTANCE_KEY", "InstanceKey"),
        0x674e: (0x0003, None, None, 4, "PidTagInstanceNum", "ptagInstanceNum", "InstanceNum"),
        0x674d: (0x0014, None, None, 4, "PidTagInstID", "ptagInstID", "InstanceId"),
        0x3fde: (0x0003, None, None, 4, "PidTagInternetCodepage", "PR_INTERNET_CPID", "InternetCPID"),
        0x5902: (0x0003, None, None, 4, "PidTagInternetMailOverrideFormat", "PR_INETMAIL_OVERRIDE_FORMAT", "INetMailOverrideFormat"),
        0x1035: (0x001f, None, None, 4, "PidTagInternetMessageId", "PR_INTERNET_MESSAGE_ID", "InternetMessageId"),
        0x1039: (0x001f, None, None, 4, "PidTagInternetReferences", "PR_INTERNET_REFERENCES", "InternetReferences"),
        0x36d0: (0x0102, None, None, 4, "PidTagIpmAppointmentEntryId", "PR_IPM_APPOINTMENT_ENTRYID", "CalendarFolderEntryId"),
        0x36d1: (0x0102, None, None, 4, "PidTagIpmContactEntryId", "PR_IPM_CONTACT_ENTRYID", "ContactsFolderEntryId"),
        0x36d7: (0x0102, None, None, 4, "PidTagIpmDraftsEntryId", "PR_IPM_DRAFTS_ENTRYID", "DraftsFolderEntryId"),
        0x36d2: (0x0102, None, None, 4, "PidTagIpmJournalEntryId", "PR_IPM_JOURNAL_ENTRYID", "JournalFolderEntryId"),
        0x36d3: (0x0102, None, None, 4, "PidTagIpmNoteEntryId", "PR_IPM_NOTE_ENTRYID", "NotesFolderEntryId"),
        0x36d4: (0x0102, None, None, 4, "PidTagIpmTaskEntryId", "PR_IPM_TASK_ENTRYID", "TasksFolderEntryId"),
        0x3a2d: (0x001f, None, None, 4, "PidTagIsdnNumber", "PR_ISDN_NUMBER", "IsdnNumber"),
        0x6103: (0x0003, None, None, 4, "PidTagJunkAddRecipientsToSafeSendersList", "PR_JUNK_ADD_RECIPS_TO_SSL", None),
        0x6100: (0x0003, None, None, 4, "PidTagJunkIncludeContacts", "PR_JUNK_INCLUDE_CONTACTS", "JunkIncludeContacts"),
        0x6102: (0x0003, None, None, 4, "PidTagJunkPermanentlyDelete", "PR_JUNK_PERMANENTLY_DELETE", None),
        0x6107: (0x000b, None, None, 4, "PidTagJunkPhishingEnableLinks", "PR_JUNK_PHISHING_ENABLE_LINKS", None),
        0x6101: (0x0003, None, None, 4, "PidTagJunkThreshold", "PR_JUNK_THRESHOLD", "JunkThreshold"),
        0x3a0b: (0x001f, None, None, 4, "PidTagKeyword", "PR_KEYWORD", "Keyword"),
        0x3a0c: (0x001f, None, None, 4, "PidTagLanguage", "PR_LANGUAGE", "Language"),
        0x3ffb: (0x0102, None, None, 4, "PidTagLastModifierEntryId", "PR_LAST_MODIFIER_ENTRYID", "LastModifierEntryId"),
        0x3ffa: (0x001f, None, None, 4, "PidTagLastModifierName", "PR_LAST_MODIFIER_NAME", "LastModifierName"),
        0x1081: (0x0003, None, None, 4, "PidTagLastVerbExecuted", "PR_LAST_VERB_EXECUTED", "LastVerbExecuted"),
        0x1082: (0x0040, None, None, 4, "PidTagLastVerbExecutionTime", "PR_LAST_VERB_EXECUTION_TIME", "LastVerbExecutionTime"),
        0x1043: (0x001f, None, None, 4, "PidTagListHelp", "PR_LIST_HELP", "ListHelp"),
        0x1044: (0x001f, None, None, 4, "PidTagListSubscribe", "PR_LIST_SUBSCRIBE", "ListSubscribe"),
        0x1045: (0x001f, None, None, 4, "PidTagListUnsubscribe", "PR_LIST_UNSUBSCRIBE", "ListUnsubscribe"),
        0x6709: (0x0040, None, None, 4, "PidTagLocalCommitTime", "PR_LOCAL_COMMIT_TIME", "LocalCommitTime"),
        0x670a: (0x0040, None, None, 4, "PidTagLocalCommitTimeMax", "PR_LOCAL_COMMIT_TIME_MAX", "LocalCommitTimeMax"),
        0x66a1: (0x0003, None, None, 4, "PidTagLocaleId", "PR_LOCALE_ID", "LocaleId"),
        0x3a0d: (0x001f, None, None, 4, "PidTagLocation", "PR_LOCATION", "Location"),
        0x661b: (0x0102, None, None, 4, "PidTagMailboxOwnerEntryId", "PR_MAILBOX_OWNER_ENTRYID", "InternetRFC821From"),
        0x661c: (0x001f, None, None, 4, "PidTagMailboxOwnerName", "PR_MAILBOX_OWNER_NAME", "MailboxOwnerName"),
        0x3a4e: (0x001f, None, None, 4, "PidTagManagerName", "PR_MANAGER_NAME", "ManagerName"),
        0x0ff8: (0x0102, None, None, 4, "PidTagMappingSignature", "PR_MAPPING_SIGNATURE", "MappingSignature"),
        0x666d: (0x0003, None, None, 4, "PidTagMaximumSubmitMessageSize", "PR_MAX_SUBMIT_MESSAGE_SIZE", "SearchAttachments"),
        0x6671: (0x0014, None, None, 4, "PidTagMemberId", "PR_MEMBER_ID", "MemberId"),
        0x6672: (0x001f, None, None, 4, "PidTagMemberName", "PR_MEMBER_NAME", "MemberName"),
        0x6673: (0x0003, None, None, 4, "PidTagMemberRights", "PR_MEMBER_RIGHTS", "MemberRights"),
        0x0e13: (0x000d, None, None, 4, "PidTagMessageAttachments", "PR_MESSAGE_ATTACHMENTS", "MessageAttachments"),
        0x0058: (0x000b, None, None, 4, "PidTagMessageCcMe", "PR_MESSAGE_CC_ME", "MessageCcMe"),
        0x3ffd: (0x0003, None, None, 4, "PidTagMessageCodepage", "PR_MESSAGE_CODEPAGE", "MessageCodePage"),
        0x0e06: (0x0040, None, None, 4, "PidTagMessageDeliveryTime", "PR_MESSAGE_DELIVERY_TIME", "MessageDeliveryTime"),
        0x5909: (0x0003, None, None, 4, "PidTagMessageEditorFormat", "PR_MSG_EDITOR_FORMAT", "MessageEditorFormat"),
        0x0e07: (0x0003, None, None, 4, "PidTagMessageFlags", "PR_MESSAGE_FLAGS", "MessageFlags"),
        0x3ff1: (0x0003, None, None, 4, "PidTagMessageLocaleId", "PR_MESSAGE_LOCALE_ID", "MessageLocaleId"),
        0x0059: (0x000b, None, None, 4, "PidTagMessageRecipientMe", "PR_MESSAGE_RECIP_ME", "MessageRecipMe"),
        0x0e12: (0x000d, None, None, 4, "PidTagMessageRecipients", "PR_MESSAGE_RECIPIENTS", "MessageRecipients"),
        0x0e08: (0x0014, None, None, 4, "PidTagMessageSizeExtended", "PR_MESSAGE_SIZE_EXTENDED", "MessageSize"),
        0x0e17: (0x0003, None, None, 4, "PidTagMessageStatus", "PR_MSG_STATUS", "MsgStatus"),
        0x0047: (0x0102, None, None, 4, "PidTagMessageSubmissionId", "PR_MESSAGE_SUBMISSION_ID", "MessageSubmissionId"),
        0x0057: (0x000b, None, None, 4, "PidTagMessageToMe", "PR_MESSAGE_TO_ME", "MessageToMe"),
        0x674a: (0x0014, None, None, 4, "PidTagMid", "ptagMID", "Mid"),
        0x3a44: (0x001f, None, None, 4, "PidTagMiddleName", "PR_MIDDLE_NAME", "MiddleName"),
        0x64f0: (0x0102, None, None, 4, "PidTagMimeSkeleton", "ptagMimeSkeleton", "MimeSkeleton"),
        0x1016: (0x0003, None, None, 4, "PidTagNativeBody", "PR_NATIVE_BODY_INFO", "NativeBodyInfo"),
        0x0e29: (0x001f, None, None, 4, "PidTagNextSendAcct", "PR_NEXT_SEND_ACCT", "NextSendAccount"),
        0x3a4f: (0x001f, None, None, 4, "PidTagNickname", "PR_NICKNAME", "Nickname"),
        0x0c05: (0x0003, None, None, 4, "PidTagNonDeliveryReportDiagCode", "PR_NDR_DIAG_CODE", "NdrDiagCode"),
        0x0c04: (0x0003, None, None, 4, "PidTagNonDeliveryReportReasonCode", "PR_NDR_REASON_CODE", "NdrReasonCode"),
        0x0c20: (0x0003, None, None, 4, "PidTagNonDeliveryReportStatusCode", "PR_NDR_STATUS_CODE", "NDRStatusCode"),
        0x0c06: (0x000b, None, None, 4, "PidTagNonReceiptNotificationRequested", "PR_NON_RECEIPT_NOTIFICATION_REQUESTED", "NonReceiptNotificationRequested"),
        0x0e1d: (0x001f, None, None, 4, "PidTagNormalizedSubject", "PR_NORMALIZED_SUBJECT", "NormalizedSubject"),
        0x0ffe: (0x0003, None, None, 4, "PidTagObjectType", "PR_OBJECT_TYPE", "ObjectType"),
        0x6802: (0x001f, None, None, 4, "PidTagSenderTelephoneNumber", "InternalSchemaSenderTelephoneNumber", "SenderTelephoneNumber"),
        0x6803: (0x001f, None, None, 4, "PidTagVoiceMessageSenderName", "InternalSchemaVoiceMessageSenderName", "SendOutlookRecallReport"),
        0x6800: (0x001f, None, None, 4, "PidTagOfflineAddressBookName", "PR_OAB_NAME", None),
        0x6801: (0x0003, None, None, 4, "PidTagVoiceMessageDuration", "InternalSchemaVoiceMessageDuration", "VoiceMessageDuration"),
        0x6805: (0x001f, None, None, 4, "PidTagVoiceMessageAttachmentOrder", "InternalSchemaVoiceMessageAttachmentOrder", "VoiceMessageAttachmentOrder"),
        0x36e2: (0x0003, None, None, 4, "PidTagOrdinalMost", "PR_ORDINAL_MOST", None),
        0x3a10: (0x001f, None, None, 4, "PidTagOrganizationalIdNumber", "PR_ORGANIZATIONAL_ID_NUMBER", "OrganizationalIdNumber"),
        0x004c: (0x0102, None, None, 4, "PidTagOriginalAuthorEntryId", "PR_ORIGINAL_AUTHOR_ENTRYID", "OriginalAuthorEntryId"),
        0x004d: (0x001f, None, None, 4, "PidTagOriginalAuthorName", "PR_ORIGINAL_AUTHOR_NAME_W", "OriginalAuthorName"),
        0x0055: (0x0040, None, None, 4, "PidTagOriginalDeliveryTime", "PR_ORIGINAL_DELIVERY_TIME", "OriginalDeliveryTime"),
        0x0072: (0x001f, None, None, 4, "PidTagOriginalDisplayBcc", "PR_ORIGINAL_DISPLAY_BCC", "OriginalDisplayBcc"),
        0x0073: (0x001f, None, None, 4, "PidTagOriginalDisplayCc", "PR_ORIGINAL_DISPLAY_CC", "OriginalDisplayCc"),
        0x0074: (0x001f, None, None, 4, "PidTagOriginalDisplayTo", "PR_ORIGINAL_DISPLAY_TO", "OriginalDisplayTo"),
        0x3a12: (0x0102, None, None, 4, "PidTagOriginalEntryId", "PR_ORIGINAL_ENTRYID", "OriginalEntryId"),
        0x004b: (0x001f, None, None, 4, "PidTagOriginalMessageClass", "PR_ORIG_MESSAGE_CLASS", "OrigMessageClass"),
        0x1046: (0x001f, None, None, 4, "PidTagOriginalMessageId", "ptagOriginalInternetMessageID", "OriginalInternetMessageId"),
        0x0066: (0x001f, None, None, 4, "PidTagOriginalSenderAddressType", "PR_ORIGINAL_SENDER_ADDRTYPE", "OriginalSenderAddrType"),
        0x0067: (0x001f, None, None, 4, "PidTagOriginalSenderEmailAddress", "PR_ORIGINAL_SENDER_EMAIL_ADDRESS", "OriginalSenderEmailAddress"),
        0x005b: (0x0102, None, None, 4, "PidTagOriginalSenderEntryId", "PR_ORIGINAL_SENDER_ENTRYID", "OriginalSenderEntryId"),
        0x005a: (0x001f, None, None, 4, "PidTagOriginalSenderName", "PR_ORIGINAL_SENDER_NAME", "OriginalSenderName"),
        0x005c: (0x0102, None, None, 4, "PidTagOriginalSenderSearchKey", "PR_ORIGINAL_SENDER_SEARCH_KEY", "OriginalSenderSearchKey"),
        0x002e: (0x0003, None, None, 4, "PidTagOriginalSensitivity", "PR_ORIGINAL_SENSITIVITY", "OriginalSensitivity"),
        0x0068: (0x001f, None, None, 4, "PidTagOriginalSentRepresentingAddressType", "PR_ORIGINAL_SENT_REPRESENTING_ADDRTYPE", "OriginalSentRepresentingAddrType"),
        0x0069: (0x001f, None, None, 4, "PidTagOriginalSentRepresentingEmailAddress", "PR_ORIGINAL_SENT_REPRESENTING_EMAIL_ADDRESS", "OriginalSentRepresentingEmailAddress"),
        0x005e: (0x0102, None, None, 4, "PidTagOriginalSentRepresentingEntryId", "PR_ORIGINAL_SENT_REPRESENTING_ENTRYID", "OriginalSentRepresentingEntryId"),
        0x005d: (0x001f, None, None, 4, "PidTagOriginalSentRepresentingName", "PR_ORIGINAL_SENT_REPRESENTING_NAME", "OriginalSentRepresentingName"),
        0x005f: (0x0102, None, None, 4, "PidTagOriginalSentRepresentingSearchKey", "PR_ORIGINAL_SENT_REPRESENTING_SEARCH_KEY", "OriginalSentRepresentingSearchKey"),
        0x0049: (0x001f, None, None, 4, "PidTagOriginalSubject", "PR_ORIGINAL_SUBJECT", "OriginalSubject"),
        0x004e: (0x0040, None, None, 4, "PidTagOriginalSubmitTime", "PR_ORIGINAL_SUBMIT_TIME", "OriginalSubmitTime"),
        0x0c08: (0x000b, None, None, 4, "PidTagOriginatorNonDeliveryReportRequested", "PR_ORIGINATOR_NON_DELIVERY_REPORT_REQUESTED", "OriginatorNonDeliveryReportRequested"),
        0x7c24: (0x000b, None, None, 4, "PidTagOscSyncEnabled", "PR_OSC_SYNC_ENABLEDONSERVER", "OscSyncEnabledOnServer"),
        0x3a5f: (0x001f, None, None, 4, "PidTagOtherAddressCity", "PR_OTHER_ADDRESS_CITY", "OtherAddressCity"),
        0x3a60: (0x001f, None, None, 4, "PidTagOtherAddressCountry", "PR_OTHER_ADDRESS_COUNTRY", "OtherAddressCountry"),
        0x3a61: (0x001f, None, None, 4, "PidTagOtherAddressPostalCode", "PR_OTHER_ADDRESS_POSTAL_CODE", "OtherAddressPostalCode"),
        0x3a64: (0x001f, None, None, 4, "PidTagOtherAddressPostOfficeBox", "PR_OTHER_ADDRESS_POST_OFFICE_BOX", "OtherAddressPostOfficeBox"),
        0x3a62: (0x001f, None, None, 4, "PidTagOtherAddressStateOrProvince", "PR_OTHER_ADDRESS_STATE_OR_PROVINCE", "OtherAddressStateOrProvince"),
        0x3a63: (0x001f, None, None, 4, "PidTagOtherAddressStreet", "PR_OTHER_ADDRESS_STREET", "OtherAddressStreet"),
        0x3a1f: (0x001f, None, None, 4, "PidTagOtherTelephoneNumber", "PR_OTHER_TELEPHONE_NUMBER", "OtherTelephoneNumber"),
        0x661d: (0x000b, None, None, 4, "PidTagOutOfOfficeState", "PR_OOF_STATE", "OofState"),
        0x0062: (0x0003, None, None, 4, "PidTagOwnerAppointmentId", "PR_OWNER_APPT_ID", "OwnerApptId"),
        0x0e09: (0x0102, None, None, 4, "PidTagParentEntryId", "PR_PARENT_ENTRYID", "ParentEntryId"),
        0x6749: (0x0014, None, None, 4, "PidTagParentFolderId", "ptagParentFID", "ParentFid"),
        0x0025: (0x0102, None, None, 4, "PidTagParentKey", "PR_PARENT_KEY", "ParentKey"),
        0x65e1: (0x0102, None, None, 4, "PidTagParentSourceKey", "PR_PARENT_SOURCE_KEY", "ParentSourceKey"),
        0x3a50: (0x001f, None, None, 4, "PidTagPersonalHomePage", "PR_PERSONAL_HOME_PAGE", "PersonalHomePage"),
        0x3019: (0x0102, None, None, 4, "PidTagPolicyTag", "PR_POLICY_TAG", "PolicyTag"),
        0x3a15: (0x001f, None, None, 4, "PidTagPostalAddress", "PR_POSTAL_ADDRESS", "PostalAddress"),
        0x65e3: (0x0102, None, None, 4, "PidTagPredecessorChangeList", "PR_PREDECESSOR_CHANGE_LIST", "PredecessorChangeList"),
        0x0e28: (0x001f, None, None, 4, "PidTagPrimarySendAccount", "PR_PRIMARY_SEND_ACCT", "PrimarySendAccount"),
        0x3a1a: (0x001f, None, None, 4, "PidTagPrimaryTelephoneNumber", "PR_PRIMARY_TELEPHONE_NUMBER", "PrimaryTelephoneNumber"),
        0x7d01: (0x000b, None, None, 4, "PidTagProcessed", "PR_PROCESSED", "IsProcessed"),
        0x3a46: (0x001f, None, None, 4, "PidTagProfession", "PR_PROFESSION", "Profession"),
        0x666a: (0x0003, None, None, 4, "PidTagProhibitReceiveQuota", "PR_PROHIBIT_RECEIVE_QUOTA", "ProhibitReceiveQuota"),
        0x666e: (0x0003, None, None, 4, "PidTagProhibitSendQuota", "PR_PROHIBIT_SEND_QUOTA", "ProhibitSendQuota"),
        0x4083: (0x001f, None, None, 4, "PidTagPurportedSenderDomain", "PR_PURPORTED_SENDER_DOMAIN", "PurportedSenderDomain"),
        0x3a1d: (0x001f, None, None, 4, "PidTagRadioTelephoneNumber", "PR_RADIO_TELEPHONE_NUMBER", "RadioTelephoneNumber"),
        0x0e69: (0x000b, None, None, 4, "PidTagRead", "PR_READ", "Read"),
        0x4029: (0x001f, None, None, 4, "PidTagReadReceiptAddressType", "ptagReadReceiptAddrType", "ReadReceiptAddrType"),
        0x402a: (0x001f, None, None, 4, "PidTagReadReceiptEmailAddress", "ptagReadReceiptEmailAddr", "ReadReceiptEmailAddress"),
        0x0046: (0x0102, None, None, 4, "PidTagReadReceiptEntryId", "PR_READ_RECEIPT_ENTRYID", "ReadReceiptEntryId"),
        0x402b: (0x001f, None, None, 4, "PidTagReadReceiptName", "ptagReadReceiptDisplayName", "ReadReceiptDisplayName"),
        0x0053: (0x0102, None, None, 4, "PidTagReadReceiptSearchKey", "PR_READ_RECEIPT_SEARCH_KEY", "ReadReceiptSearchKey"),
        0x5d05: (0x001f, None, None, 4, "PidTagReadReceiptSmtpAddress", "ptagRecipientReadReceiptSmtpAddress", "ReadReceiptSMTPAddress"),
        0x0075: (0x001f, None, None, 4, "PidTagReceivedByAddressType", "PR_RECEIVED_BY_ADDRTYPE", "ReceivedByAddrType"),
        0x0076: (0x001f, None, None, 4, "PidTagReceivedByEmailAddress", "PR_RECEIVED_BY_EMAIL_ADDRESS", "ReceivedByEmailAddress"),
        0x003f: (0x0102, None, None, 4, "PidTagReceivedByEntryId", "PR_RECEIVED_BY_ENTRYID", "ReceivedByEntryId"),
        0x0040: (0x001f, None, None, 4, "PidTagReceivedByName", "PR_RECEIVED_BY_NAME", "ReceivedByName"),
        0x0051: (0x0102, None, None, 4, "PidTagReceivedBySearchKey", "PR_RECEIVED_BY_SEARCH_KEY", "ReceivedBySearchKey"),
        0x5d07: (0x001f, None, None, 4, "PidTagReceivedBySmtpAddress", "ptagRecipientRcvdBySmtpAddress", "ReceivedBySmtpAddress"),
        0x0077: (0x001f, None, None, 4, "PidTagReceivedRepresentingAddressType", "PR_RCVD_REPRESENTING_ADDRTYPE", "RcvdRepresentingAddrType"),
        0x0078: (0x001f, None, None, 4, "PidTagReceivedRepresentingEmailAddress", "PR_RCVD_REPRESENTING_EMAIL_ADDRESS", "RcvdRepresentingEmailAddress"),
        0x0043: (0x0102, None, None, 4, "PidTagReceivedRepresentingEntryId", "PR_RCVD_REPRESENTING_ENTRYID", "RcvdRepresentingEntryId"),
        0x0044: (0x001f, None, None, 4, "PidTagReceivedRepresentingName", "PR_RCVD_REPRESENTING_NAME", "RcvdRepresentingName"),
        0x0052: (0x0102, None, None, 4, "PidTagReceivedRepresentingSearchKey", "PR_RCVD_REPRESENTING_SEARCH_KEY", "RcvdRepresentingSearchKey"),
        0x5d08: (0x001f, None, None, 4, "PidTagReceivedRepresentingSmtpAddress", "ptagRecipientRcvdRepresentingSmtpAddress", "RcvdRepresentingSmtpAddress"),
        0x5ff6: (0x001f, None, None, 4, "PidTagRecipientDisplayName", "PR_RECIPIENT_DISPLAY_NAME", "RecipientDisplayName"),
        0x5ff7: (0x0102, None, None, 4, "PidTagRecipientEntryId", "PR_RECIPIENT_ENTRYID", "RecipientEntryId"),
        0x5ffd: (0x0003, None, None, 4, "PidTagRecipientFlags", "PR_RECIPIENT_FLAGS", "RecipientFlags"),
        0x5fdf: (0x0003, None, None, 4, "PidTagRecipientOrder", "PR_RECIPIENT_ORDER", "RecipientOrder"),
        0x5fe1: (0x000b, None, None, 4, "PidTagRecipientProposed", "PR_RECIPIENT_PROPOSED", "RecipientProposed"),
        0x5fe4: (0x0040, None, None, 4, "PidTagRecipientProposedEndTime", "PR_RECIPIENT_PROPOSEDENDTIME", "RecipientProposedEndTime"),
        0x5fe3: (0x0040, None, None, 4, "PidTagRecipientProposedStartTime", "PR_RECIPIENT_PROPOSEDSTARTTIME", "RecipientProposedStartTime"),
        0x002b: (0x000b, None, None, 4, "PidTagRecipientReassignmentProhibited", "PR_RECIPIENT_REASSIGNMENT_PROHIBITED", "RecipientReassignmentProhibited"),
        0x5fff: (0x0003, None, None, 4, "PidTagRecipientTrackStatus", "PR_RECIPIENT_TRACKSTATUS", "RecipientTrackStatus"),
        0x5ffb: (0x0040, None, None, 4, "PidTagRecipientTrackStatusTime", "PR_RECIPIENT_TRACKSTATUS_TIME", "RecipientTrackStatusTime"),
        0x0c15: (0x0003, None, None, 4, "PidTagRecipientType", "PR_RECIPIENT_TYPE", "RecipientType"),
        0x0ff9: (0x0102, None, None, 4, "PidTagRecordKey", "PR_RECORD_KEY", "RecordKey"),
        0x3a47: (0x001f, None, None, 4, "PidTagReferredByName", "PR_REFERRED_BY_NAME", "PreferredByName"),
        0x36d5: (0x0102, None, None, 4, "PidTagRemindersOnlineEntryId", "PR_REM_ONLINE_ENTRYID", "RemindersSearchFolderEntryId"),
        0x0c21: (0x001f, None, None, 4, "PidTagRemoteMessageTransferAgent", "PR_DSN_REMOTE_MTA", "RemoteMta"),
        0x370b: (0x0003, None, None, 4, "PidTagRenderingPosition", "PR_RENDERING_POSITION", "RenderingPosition"),
        0x004f: (0x0102, None, None, 4, "PidTagReplyRecipientEntries", "PR_REPLY_RECIPIENT_ENTRIES", "ReplyRecipientEntries"),
        0x0050: (0x001f, None, None, 4, "PidTagReplyRecipientNames", "PR_REPLY_RECIPIENT_NAMES", "ReplyRecipientNames"),
        0x0c17: (0x000b, None, None, 4, "PidTagReplyRequested", "PR_REPLY_REQUESTED", "ReplyRequested"),
        0x65c2: (0x0102, None, None, 4, "PidTagReplyTemplateId", "PR_REPLY_TEMPLATE_ID", "ReplyTemplateID"),
        0x0030: (0x0040, None, None, 4, "PidTagReplyTime", "PR_REPLY_TIME", "ReplyTime"),
        0x0080: (0x001f, None, None, 4, "PidTagReportDisposition", "PR_REPORT_DISPOSITION_W", "ReportDisposition"),
        0x0081: (0x001f, None, None, 4, "PidTagReportDispositionMode", "PR_REPORT_DISPOSITION_MODE_W", "ReportDispositionMode"),
        0x0045: (0x0102, None, None, 4, "PidTagReportEntryId", "PR_REPORT_ENTRYID", "ReportEntryId"),
        0x6820: (0x001f, None, None, 4, "PidTagReportingMessageTransferAgent", "ptagDsnReportingMta", "ReportingMta"),
        0x003a: (0x001f, None, None, 4, "PidTagReportName", "PR_REPORT_NAME", "ReportName"),
        0x0054: (0x0102, None, None, 4, "PidTagReportSearchKey", "PR_REPORT_SEARCH_KEY", "ReportSearchKey"),
        0x0031: (0x0102, None, None, 4, "PidTagReportTag", "PR_REPORT_TAG", "ReportTag"),
        0x0032: (0x0040, None, None, 4, "PidTagReportTime", "PR_REPORT_TIME", "ReportTime"),
        0x3fe7: (0x0003, None, None, 4, "PidTagResolveMethod", "PR_RESOLVE_METHOD", "ResolveMethod"),
        0x0063: (0x000b, None, None, 4, "PidTagResponseRequested", "PR_RESPONSE_REQUESTED", "ResponseRequested"),
        0x0e0f: (0x000b, None, None, 4, "PidTagResponsibility", "PR_RESPONSIBILITY", "Responsibility"),
        0x301c: (0x0040, None, None, 4, "PidTagRetentionDate", "PR_RETENTION_DATE", "RetentionDate"),
        0x301d: (0x0003, None, None, 4, "PidTagRetentionFlags", "PR_RETENTION_FLAGS", "RetentionFlags"),
        0x301a: (0x0003, None, None, 4, "PidTagRetentionPeriod", "PR_RETENTION_PERIOD", "RetentionPeriod"),
        0x6639: (0x0003, None, None, 4, "PidTagRights", "PR_RIGHTS", "AccessRights"),
        0x7c06: (0x0003, None, None, 4, "PidTagRoamingDatatypes", "PR_ROAMING_DATATYPES", "UserConfigurationType"),
        0x7c07: (0x0102, None, None, 4, "PidTagRoamingDictionary", "PR_ROAMING_DICTIONARY", "UserConfigurationDictionary"),
        0x7c08: (0x0102, None, None, 4, "PidTagRoamingXmlStream", "PR_ROAMING_XMLSTREAM", "UserConfigurationXml"),
        0x3000: (0x0003, None, None, 4, "PidTagRowid", "PR_ROWID", "RowId"),
        0x0ff5: (0x0003, None, None, 4, "PidTagRowType", "PR_ROW_TYPE", "RowType"),
        0x1009: (0x0102, None, None, 4, "PidTagRtfCompressed", "PR_RTF_COMPRESSED", "RtfCompressed"),
        0x0e1f: (0x000b, None, None, 4, "PidTagRtfInSync", "PR_RTF_IN_SYNC", "RtfInSync"),
        0x6650: (0x0003, None, None, 4, "PidTagRuleActionNumber", "PR_RULE_ACTION_NUMBER", "RuleActionNumber"),
        0x6680: (0x00fe, None, None, 4, "PidTagRuleActions", "PR_RULE_ACTIONS", "RuleActions"),
        0x6649: (0x0003, None, None, 4, "PidTagRuleActionType", "PR_RULE_ACTION_TYPE", "RuleActionType"),
        0x6679: (0x00fd, None, None, 4, "PidTagRuleCondition", "PR_RULE_CONDITION", "RuleCondition"),
        0x6648: (0x0003, None, None, 4, "PidTagRuleError", "PR_RULE_ERROR", "RuleError"),
        0x6651: (0x0102, None, None, 4, "PidTagRuleFolderEntryId", "PR_RULE_FOLDER_ENTRYID", "RuleFolderEntryID"),
        0x6674: (0x0014, None, None, 4, "PidTagRuleId", "PR_RULE_ID", "RuleID"),
        0x6675: (0x0102, None, None, 4, "PidTagRuleIds", "PR_RULE_IDS", "RuleIDs"),
        0x6683: (0x0003, None, None, 4, "PidTagRuleLevel", "PR_RULE_LEVEL", "RuleLevel"),
        0x65ed: (0x0003, None, None, 4, "PidTagRuleMessageLevel", "PR_RULE_MSG_LEVEL", "RuleMsgLevel"),
        0x65ec: (0x001f, None, None, 4, "PidTagRuleMessageName", "ptagRuleMsgName", "RuleMsgName"),
        0x65eb: (0x001f, None, None, 4, "PidTagRuleMessageProvider", "ptagRuleMsgProvider", "RuleMsgProvider"),
        0x65ee: (0x0102, None, None, 4, "PidTagRuleMessageProviderData", "PR_RULE_MSG_PROVIDER_DATA", "RuleMsgProviderData"),
        0x65f3: (0x0003, None, None, 4, "PidTagRuleMessageSequence", "PR_RULE_MSG_SEQUENCE", "RuleMsgSequence"),
        0x65e9: (0x0003, None, None, 4, "PidTagRuleMessageState", "PR_RULE_MSG_STATE", "RuleMsgState"),
        0x65ea: (0x0003, None, None, 4, "PidTagRuleMessageUserFlags", "PR_RULE_MSG_USER_FLAGS", "RuleMsgUserFlags"),
        0x6682: (0x001f, None, None, 4, "PidTagRuleName", "PR_RULE_NAME", "RuleName"),
        0x6681: (0x001f, None, None, 4, "PidTagRuleProvider", "PR_RULE_PROVIDER", "RuleProvider"),
        0x6684: (0x0102, None, None, 4, "PidTagRuleProviderData", "PR_RULE_PROVIDER_DATA", "RuleProviderData"),
        0x6676: (0x0003, None, None, 4, "PidTagRuleSequence", "PR_RULE_SEQUENCE", "RuleSequence"),
        0x6677: (0x0003, None, None, 4, "PidTagRuleState", "PR_RULE_STATE", "RuleState"),
        0x6678: (0x0003, None, None, 4, "PidTagRuleUserFlags", "PR_RULE_USER_FLAGS", "RuleUserFlags"),
        0x686a: (0x0102, None, None, 4, "PidTagScheduleInfoAppointmentTombstone", "PR_SCHDINFO_APPT_TOMBSTONE", "AppointmentTombstonesId"),
        0x686d: (0x000b, None, None, 4, "PidTagScheduleInfoAutoAcceptAppointments", "PR_SCHDINFO_AUTO_ACCEPT_APPTS", None),
        0x6845: (0x0102, None, None, 4, "PidTagSearchFolderDefinition", "PR_WB_SF_DEFINITION", "DelegateEntryIds"),
        0x6844: (0x0102, None, None, 4, "PidTagSearchFolderRecreateInfo", "PR_WB_SF_RECREATE_INFO", "ActivityLogicalItemId"),
        0x684a: (0x0003, None, None, 4, "PidTagWlinkFlags", "PR_WLINK_FLAGS", "DelegateNames"),
        0x6842: (0x0102, None, None, 4, "PidTagWlinkGroupHeaderID", None, "DelegateBossWantsCopy"),
        0x684b: (0x0102, None, None, 4, "PidTagWlinkOrdinal", "PR_WLINK_ORDINAL", "DelegateBossWantsInfo"),
        0x686f: (0x000b, None, None, 4, "PidTagScheduleInfoDisallowOverlappingAppts", "PR_SCHDINFO_DISALLOW_OVERLAPPING_APPTS", None),
        0x686e: (0x000b, None, None, 4, "PidTagScheduleInfoDisallowRecurringAppts", "PR_SCHDINFO_DISALLOW_RECURRING_APPTS", None),
        0x6843: (0x000b, None, None, 4, "PidTagScheduleInfoDontMailDelegates", "PR_SCHDINFO_DONT_MAIL_DELEGATES", "ActivityContainerType"),
        0x686c: (0x0102, None, None, 4, "PidTagScheduleInfoFreeBusy", "PR_SCHDINFO_FREEBUSY", None),
        0x6856: (0x1102, None, None, 4, "PidTagScheduleInfoFreeBusyAway", "PR_SCHDINFO_FREEBUSY_OOF", "AgingFileName9AndPrev"),
        0x6854: (0x0102, None, None, 4, "PidTagWlinkAddressBookEID", "PR_WLINK_ABEID", "NavigationNodeAddressBookEntryId"),
        0x6850: (0x0102, None, None, 4, "PidTagWlinkGroupClsid", "PR_WLINK_GROUP_CLSID", "ScheduleInfoFreeBusyMerged"),
        0x6852: (0x0003, None, None, 4, "PidTagWlinkSection", "PR_WLINK_SECTION", "NavigationNodeGroupSection"),
        0x6855: (0x1003, None, None, 4, "PidTagScheduleInfoMonthsAway", "PR_SCHDINFO_MONTHS_OOF", "ScheduleInfoMonthsOof"),
        0x6853: (0x0003, None, None, 4, "PidTagWlinkCalendarColor", "PR_WLINK_CALENDAR_COLOR", "NavigationNodeCalendarColor"),
        0x684f: (0x0102, None, None, 4, "PidTagWlinkFolderType", "PR_WLINK_FOLDER_TYPE", "ScheduleInfoMonthsMerged"),
        0x6851: (0x001f, None, None, 4, "PidTagWlinkGroupName", "PR_WLINK_GROUP_NAME", "NavigationNodeGroupName"),
        0x6841: (0x0003, None, None, 4, "PidTagSearchFolderTemplateId", "PR_WB_SF_TEMPLATE_ID", "AssociatedSearchFolderTemplateId"),
        0x6622: (0x0102, None, None, 4, "PidTagSchedulePlusFreeBusyEntryId", "PR_SPLUS_FREE_BUSY_ENTRYID", "FreeBusyEntryId"),
        0x683a: (0x0003, None, None, 4, "PidTagSearchFolderExpiration", "PR_WB_SF_EXPIRATION", "ActivitySequenceNumber"),
        0x6834: (0x0003, None, None, 4, "PidTagSearchFolderLastUsed", "PR_WB_SF_LAST_USED", "AssociatedSearchFolderLastUsedTime"),
        0x300b: (0x0102, None, None, 4, "PidTagSearchKey", "PR_SEARCH_KEY", "SearchKey"),
        0x0e6a: (0x001f, None, None, 4, "PidTagSecurityDescriptorAsXml", "PR_NT_SECURITY_DESCRIPTOR_AS_XML", "NTSDAsXML"),
        0x3609: (0x000b, None, None, 4, "PidTagSelectable", "PR_SELECTABLE", "Selectable"),
        0x0c1e: (0x001f, None, None, 4, "PidTagSenderAddressType", "PR_SENDER_ADDRTYPE", "SenderAddrType"),
        0x0c1f: (0x001f, None, None, 4, "PidTagSenderEmailAddress", "PR_SENDER_EMAIL_ADDRESS", "SenderEmailAddress"),
        0x0c19: (0x0102, None, None, 4, "PidTagSenderEntryId", "PR_SENDER_ENTRYID", "SenderEntryId"),
        0x4079: (0x0003, None, None, 4, "PidTagSenderIdStatus", "PR_SENDER_ID_STATUS", "SenderIdStatus"),
        0x0c1a: (0x001f, None, None, 4, "PidTagSenderName", "PR_SENDER_NAME", "SenderName"),
        0x0c1d: (0x0102, None, None, 4, "PidTagSenderSearchKey", "PR_SENDER_SEARCH_KEY", "SenderSearchKey"),
        0x5d01: (0x001f, None, None, 4, "PidTagSenderSmtpAddress", "SenderSmtpAddress", "SenderSmtpAddress"),
        0x0036: (0x0003, None, None, 4, "PidTagSensitivity", "PR_SENSITIVITY", "Sensitivity"),
        0x6740: (0x00fb, None, None, 4, "PidTagSentMailSvrEID", "ptagSentMailSvrEID", "SentMailSvrEId"),
        0x0064: (0x001f, None, None, 4, "PidTagSentRepresentingAddressType", "PR_SENT_REPRESENTING_ADDRTYPE", "SentRepresentingAddrType"),
        0x0065: (0x001f, None, None, 4, "PidTagSentRepresentingEmailAddress", "PR_SENT_REPRESENTING_EMAIL_ADDRESS", "SentRepresentingEmailAddress"),
        0x0041: (0x0102, None, None, 4, "PidTagSentRepresentingEntryId", "PR_SENT_REPRESENTING_ENTRYID", "SentRepresentingEntryId"),
        0x401a: (0x0003, None, None, 4, "PidTagSentRepresentingFlags", "ptagSentRepresentingFlags", "SentRepresentingFlags"),
        0x0042: (0x001f, None, None, 4, "PidTagSentRepresentingName", "PR_SENT_REPRESENTING_NAME", "SentRepresentingName"),
        0x003b: (0x0102, None, None, 4, "PidTagSentRepresentingSearchKey", "PR_SENT_REPRESENTING_SEARCH_KEY", "SentRepresentingSearchKey"),
        0x5d02: (0x001f, None, None, 4, "PidTagSentRepresentingSmtpAddress", "ptagRecipientSentRepresentingSMTPAddress", "SentRepresentingSmtpAddress"),
        0x6638: (0x0102, None, None, 4, "PidTagSerializedReplidGuidMap", "ptagSerializedReplidGuidMap", "FolderChildCount32"),
        0x6705: (0x0003, None, None, 4, "PidTagSortLocaleId", "PR_SORT_LOCALE_ID", "SortLocaleId"),
        0x65e0: (0x0102, None, None, 4, "PidTagSourceKey", "PR_SOURCE_KEY", "SourceKey"),
        0x3a48: (0x001f, None, None, 4, "PidTagSpouseName", "PR_SPOUSE_NAME", "SpouseName"),
        0x0060: (0x0040, None, None, 4, "PidTagStartDate", "PR_START_DATE", "StartDate"),
        0x301b: (0x0102, None, None, 4, "PidTagStartDateEtc", "PR_START_DATE_ETC", "StartDateEtc"),
        0x0ffb: (0x0102, None, None, 4, "PidTagStoreEntryId", "PR_STORE_ENTRYID", "StoreEntryid"),
        0x340e: (0x0003, None, None, 4, "PidTagStoreState", "PR_STORE_STATE", "StoreState"),
        0x340d: (0x0003, None, None, 4, "PidTagStoreSupportMask", "PR_STORE_SUPPORT_MASK", "StoreSupportMask"),
        0x360a: (0x000b, None, None, 4, "PidTagSubfolders", "PR_SUBFOLDERS", "SubFolders"),
        0x0037: (0x001f, None, None, 4, "PidTagSubject", "PR_SUBJECT", "Subject"),
        0x003d: (0x001f, None, None, 4, "PidTagSubjectPrefix", "PR_SUBJECT_PREFIX", "SubjectPrefix"),
        0x0c1b: (0x001f, None, None, 4, "PidTagSupplementaryInfo", "PR_SUPPLEMENTARY_INFO", "SupplementaryInfo"),
        0x0e2d: (0x0102, None, None, 4, "PidTagSwappedToDoData", "PR_SWAPPED_TODO_DATA", "SwappedToDoData"),
        0x0e2c: (0x0102, None, None, 4, "PidTagSwappedToDoStore", "PR_SWAPPED_TODO_STORE", "SwappedToDoStore"),
        0x3010: (0x0102, None, None, 4, "PidTagTargetEntryId", "PR_TARGET_ENTRYID", "TargetEntryId"),
        0x3a4b: (0x001f, None, None, 4, "PidTagTelecommunicationsDeviceForDeafTelephoneNumber", "PR_TTYTDD_PHONE_NUMBER", "TtytddPhoneNumber"),
        0x3902: (0x0102, None, None, 4, "PidTagTemplateid", "PR_TEMPLATEID", "TemplateId"),
        0x371b: (0x001f, None, None, 4, "PidTagTextAttachmentCharset", "ptagTextAttachmentCharset", "TextAttachmentCharset"),
        0x007f: (0x0102, None, None, 4, "PidTagTnefCorrelationKey", "PR_TNEF_CORRELATION_KEY", "TnefCorrelationKey"),
        0x0e2b: (0x0003, None, None, 4, "PidTagToDoItemFlags", "PR_TODO_ITEM_FLAGS", "ToDoItemFlags"),
        0x3a20: (0x001f, None, None, 4, "PidTagTransmittableDisplayName", "PR_TRANSMITABLE_DISPLAY_NAME", "TransmitableDisplayName"),
        0x007d: (0x001f, None, None, 4, "PidTagTransportMessageHeaders", "PR_TRANSPORT_MESSAGE_HEADERS", "TransportMessageHeaders"),
        0x0e79: (0x0003, None, None, 4, "PidTagTrustSender", "PR_TRUST_SENDER", "TrustSender"),
        0x6619: (0x0102, None, None, 4, "PidTagUserEntryId", "PR_USER_ENTRYID", "InternetAddressConversion"),
        0x7001: (0x0102, None, None, 4, "PidTagViewDescriptorBinary", "PR_VD_BINARY", "UserInformationLastMaintenanceTime"),
        0x7006: (0x001f, None, None, 4, "PidTagViewDescriptorName", "PR_VD_NAME", None),
        0x7002: (0x001f, None, None, 4, "PidTagViewDescriptorStrings", "PR_VD_STRINGS", None),
        0x7007: (0x0003, None, None, 4, "PidTagViewDescriptorVersion", "PR_VD_VERSION", None),
        0x3a41: (0x0040, None, None, 4, "PidTagWeddingAnniversary", "PR_WEDDING_ANNIVERSARY", "WeddingAnniversary"),
        0x6891: (0x0102, None, None, 4, "PidTagWlinkAddressBookStoreEID", "PR_WLINK_AB_EXSTOREEID", "ConversationContentUnreadMailboxWide"),
        0x6890: (0x0102, None, None, 4, "PidTagWlinkClientID", "PR_WLINK_CLIENTID", "ConversationContentUnread"),
        0x684c: (0x0102, None, None, 4, "PidTagWlinkEntryId", "PR_WLINK_ENTRYID", "NavigationNodeEntryId"),
        0x684d: (0x0102, None, None, 4, "PidTagWlinkRecordKey", "PR_WLINK_RECKEY", "NavigationNodeRecordKey"),
        0x6892: (0x0003, None, None, 4, "PidTagWlinkROGroupType", "PR_WLINK_RO_GROUP_TYPE", "ConversationMessageSize"),
        0x684e: (0x0102, None, None, 4, "PidTagWlinkStoreEntryId", "PR_WLINK_STORE_ENTRYID", "NavigationNodeStoreEntryId"),
# ====================
        0x0000: (0x0001, None, None, 4, None, None, "Null"),
        0x6700: (0x001f, None, None, 4, None, None, "PstPath"),
        0x6603: (0x001f, None, None, 4, None, None, "ProfileUser"),
        0x660b: (0x001f, None, None, 4, None, None, "ProfileMailbox"),
        0x6602: (0x001f, None, None, 4, None, None, "ProfileHomeServer"),
        0x6612: (0x001f, None, None, 4, None, None, "ProfileHomeServerDn"),
        0x660c: (0x001f, None, None, 4, None, None, "ProfileServer"),
        0x6614: (0x001e, None, None, 4, None, None, "ProfileServerDn"),
        0x6601: (0x0003, None, None, 4, None, None, "ProfileConfigFlags"),
        0x6605: (0x0003, None, None, 4, None, None, "ProfileTransportFlags"),
        0x6600: (0x0003, None, None, 4, None, None, "ProfileVersion"),
        0x6604: (0x0003, None, None, 4, None, None, "ProfileConnectFlags"),
        0x6606: (0x0003, None, None, 4, None, None, "ProfileUiState"),
        0x000b: (0x0102, None, None, 4, None, None, "ConversationKey"),
        0x0016: (0x000b, None, None, 4, None, None, "ImplicitConversionProhibited"),
        0x0019: (0x0040, None, None, 4, None, None, "LatestDeliveryTime"),
        0x001b: (0x0102, None, None, 4, None, None, "MessageDeliveryId"),
        0x001e: (0x0102, None, None, 4, None, None, "MessageSecurityLabel"),
        0x001f: (0x0102, None, None, 4, None, None, "ObsoletedIpms"),
        0x0020: (0x0102, None, None, 4, None, None, "OriginallyIntendedRecipientName"),
        0x0021: (0x0102, None, None, 4, None, None, "OriginalEits"),
        0x0022: (0x0102, None, None, 4, None, None, "OriginatorCertificate"),
        0x0027: (0x0102, None, None, 4, None, None, "OriginCheck"),
        0x002c: (0x0102, None, None, 4, None, None, "RedirectionHistory"),
        0x002d: (0x0102, None, None, 4, None, None, "RelatedIpms"),
        0x002f: (0x001f, None, None, 4, None, None, "Languages"),
        0x0033: (0x000b, None, None, 4, None, None, "ReturnedIpm"),
        0x0034: (0x0003, None, None, 4, None, None, "Security"),
        0x0035: (0x000b, None, None, 4, None, None, "IncompleteCopy"),
        0x0038: (0x0102, None, None, 4, None, None, "SubjectIpm"),
        0x003c: (0x0102, None, None, 4, None, None, "X400ContentType"),
        0x003e: (0x0003, None, None, 4, None, None, "NonReceiptReason"),
        0x0048: (0x0040, None, None, 4, None, None, "ProviderSubmitTime"),
        0x004a: (0x000b, None, None, 4, None, None, "DiscVal"),
        0x0056: (0x0102, None, None, 4, None, None, "OriginalAuthorSearchKey"),
        0x0079: (0x001f, None, None, 4, None, None, "OriginalAuthorAddrType"),
        0x007a: (0x001f, None, None, 4, None, None, "OriginalAuthorEmailAddress"),
        0x007b: (0x001f, None, None, 4, None, None, "OriginallyIntendedRecipAddrType"),
        0x007c: (0x001f, None, None, 4, None, None, "OriginallyIntendedRecipEmailAddress"),
        0x007e: (0x0102, None, None, 4, None, None, "Delegation"),
        0x4031: (0x001f, None, None, 4, None, None, "SentRepresentingSimpleDisplayName"),
        0x4032: (0x001f, None, None, 4, None, None, "OriginalSenderSimpleDisplayName"),
        0x4033: (0x001f, None, None, 4, None, None, "OriginalSentRepresentingSimpleDisplayName"),
        0x4034: (0x001f, None, None, 4, None, None, "ReceivedBySimpleDisplayName"),
        0x4035: (0x001f, None, None, 4, None, None, "RcvdRepresentingSimpleDisplayName"),
        0x4036: (0x001f, None, None, 4, None, None, "ReadReceiptSimpleDisplayName"),
        0x4060: (0x001f, None, None, 4, None, None, "OriginalAuthorSimpleDisplayName"),
        0x1002: (0x0102, None, None, 4, None, None, "OriginatorAndDlExpansionHistory"),
        0x1003: (0x0102, None, None, 4, None, None, "ReportingDlName"),
        0x1004: (0x0102, None, None, 4, None, None, "ReportingMtaCertificate"),
        0x1007: (0x0003, None, None, 4, None, None, "RtfSyncBodyCount"),
        0x1008: (0x001f, None, None, 4, None, None, "RtfSyncBodyTag"),
        0x1010: (0x0003, None, None, 4, None, None, "RtfSyncPrefixCount"),
        0x1011: (0x0003, None, None, 4, None, None, "RtfSyncTrailingCount"),
        0x1012: (0x0102, None, None, 4, None, None, "OriginallyIntendedRecipEntryId"),
        0x1097: (0x0003, None, None, 4, None, None, "ItemTemporaryFlag"),
        0x10c6: (0x001f, None, None, 4, None, None, "DavSubmitData"),
        0x0c00: (0x0102, None, None, 4, None, None, "ContentIntegrityCheck"),
        0x0c01: (0x0003, None, None, 4, None, None, "ExplicitConversion"),
        0x0c02: (0x000b, None, None, 4, None, None, "IpmReturnRequested"),
        0x0c03: (0x0102, None, None, 4, None, None, "MessageToken"),
        0x0c07: (0x0003, None, None, 4, None, None, "DeliveryPoint"),
        0x0c09: (0x0102, None, None, 4, None, None, "OriginatorRequestedAlternateRecipient"),
        0x0c0a: (0x000b, None, None, 4, None, None, "PhysicalDeliveryBureauFaxDelivery"),
        0x0c0b: (0x0003, None, None, 4, None, None, "PhysicalDeliveryMode"),
        0x0c0c: (0x0003, None, None, 4, None, None, "PhysicalDeliveryReportRequest"),
        0x0c0d: (0x0102, None, None, 4, None, None, "PhysicalForwardingAddress"),
        0x0c0e: (0x000b, None, None, 4, None, None, "PhysicalForwardingAddressRequested"),
        0x0c0f: (0x000b, None, None, 4, None, None, "PhysicalForwardingProhibited"),
        0x0c10: (0x0102, None, None, 4, None, None, "PhysicalRenditionAttributes"),
        0x0c11: (0x0102, None, None, 4, None, None, "ProofOfDelivery"),
        0x0c12: (0x000b, None, None, 4, None, None, "ProofOfDeliveryRequested"),
        0x0c13: (0x0102, None, None, 4, None, None, "RecipientCertificate"),
        0x0c14: (0x001f, None, None, 4, None, None, "RecipientNumberForAdvice"),
        0x0c16: (0x0003, None, None, 4, None, None, "RegisteredMailType"),
        0x0c18: (0x0003, None, None, 4, None, None, "RequestedDeliveryMethod"),
        0x0c1c: (0x0003, None, None, 4, None, None, "TypeOfMtsUser"),
        0x5903: (0x001f, None, None, 4, None, None, "INetMailOverrideCharset"),
        0x4030: (0x001f, None, None, 4, None, None, "SenderSimpleDisplayName"),
        0x0e00: (0x0014, None, None, 4, None, None, "CurrentVersion"),
        0x0e05: (0x001f, None, None, 4, None, None, "ParentDisplay"),
        0x0e0a: (0x0102, None, None, 4, None, None, "SentMailEntryId"),
        0x0e0c: (0x000b, None, None, 4, None, None, "Correlate"),
        0x0e0d: (0x0102, None, None, 4, None, None, "CorrelateMtsid"),
        0x0e0e: (0x000b, None, None, 4, None, None, "DiscreteValues"),
        0x0e10: (0x0003, None, None, 4, None, None, "SpoolerStatus"),
        0x0e11: (0x0003, None, None, 4, None, None, "TransportStatus"),
        0x0e14: (0x0003, None, None, 4, None, None, "SubmitFlags"),
        0x0e15: (0x0003, None, None, 4, None, None, "RecipientStatus"),
        0x0e16: (0x0003, None, None, 4, None, None, "TransportKey"),
        0x0e18: (0x0003, None, None, 4, None, None, "MessageDownloadTime"),
        0x0e19: (0x0014, None, None, 4, None, None, "CreationVersion"),
        0x0e1a: (0x0014, None, None, 4, None, None, "ModifyVersion"),
        0x0e1c: (0x0003, None, None, 4, None, None, "BodyCrc"),
        0x0e22: (0x000b, None, None, 4, None, None, "Preprocess"),
        0x0e23: (0x0003, None, None, 4, None, None, "InternetArticleNumber"),
        0x0e25: (0x0102, None, None, 4, None, None, "OriginatingMtaCertificate"),
        0x0e26: (0x0102, None, None, 4, None, None, "ProofOfSubmission"),
        0x0e3a: (0x000d, None, None, 4, None, None, "MessageDeepAttachments"),
        0x0e85: (0x001f, None, None, 4, None, None, "AntiVirusVendor"),
        0x0e86: (0x0003, None, None, 4, None, None, "AntiVirusVersion"),
        0x0e87: (0x0003, None, None, 4, None, None, "AntiVirusScanStatus"),
        0x0e88: (0x001f, None, None, 4, None, None, "AntiVirusScanInfo"),
        0x0e96: (0x101f, None, None, 4, None, None, "TransportAntiVirusStamp"),
        0x0e7c: (0x0003, None, None, 4, None, None, "MessageDatabasePage"),
        0x0e7d: (0x0003, None, None, 4, None, None, "MessageHeaderDatabasePage"),
        0x0ffd: (0x0102, None, None, 4, None, None, "Icon"),
        0x0ffc: (0x0102, None, None, 4, None, None, "MiniIcon"),
        0x0ffa: (0x0102, None, None, 4, None, None, "StoreRecordKey"),
        0x0f0e: (0x001f, None, None, 4, None, None, "MessageAnnotation"),
        0x3006: (0x001f, None, None, 4, None, None, "ProviderDisplay"),
        0x3009: (0x0003, None, None, 4, None, None, "ResourceFlags"),
        0x300a: (0x001f, None, None, 4, None, None, "ProviderDllName"),
        0x300c: (0x0102, None, None, 4, None, None, "ProviderUid"),
        0x300d: (0x0003, None, None, 4, None, None, "ProviderOrdinal"),
        0x3012: (0x001f, None, None, 4, None, None, "ConversationIdObsolete"),
        0x3014: (0x0102, None, None, 4, None, None, "BodyTag"),
        0x3015: (0x0014, None, None, 4, None, None, "ConversationIndexTrackingObsolete"),
        0x6827: (0x0003, None, None, 4, None, None, "ConversationIdHash"),
        0x6200: (0x0003, None, None, 4, None, None, "InternetMessageIdHash"),
        0x6201: (0x0003, None, None, 4, None, None, "ConversationTopicHash"),
        0x3660: (0x0102, None, None, 4, None, None, "ConversationTopicHashEntries"),
        0x3301: (0x001f, None, None, 4, None, None, "FormVersion"),
        0x3302: (0x0048, None, None, 4, None, None, "FormClsid"),
        0x3303: (0x001f, None, None, 4, None, None, "FormContactName"),
        0x3304: (0x001f, None, None, 4, None, None, "FormCategory"),
        0x3305: (0x001f, None, None, 4, None, None, "FormCategorySub"),
        0x3306: (0x1003, None, None, 4, None, None, "FormHostMap"),
        0x3307: (0x000b, None, None, 4, None, None, "FormHidden"),
        0x3308: (0x001f, None, None, 4, None, None, "FormDesignerName"),
        0x3309: (0x0048, None, None, 4, None, None, "FormDesignerGuid"),
        0x330a: (0x0003, None, None, 4, None, None, "FormMessageBehavior"),
        0x3400: (0x000b, None, None, 4, None, None, "DefaultStore"),
        0x3410: (0x0102, None, None, 4, None, None, "IpmSubtreeSearchKey"),
        0x3411: (0x0102, None, None, 4, None, None, "IpmOutboxSearchKey"),
        0x3412: (0x0102, None, None, 4, None, None, "IpmWastebasketSearchKey"),
        0x3413: (0x0102, None, None, 4, None, None, "IpmSentMailSearchKey"),
        0x3414: (0x0102, None, None, 4, None, None, "MdbProvider"),
        0x3415: (0x000d, None, None, 4, None, None, "ReceiveFolderSettings"),
        0x35df: (0x0003, None, None, 4, None, None, "ValidFolderMask"),
        0x35e0: (0x0102, None, None, 4, None, None, "IpmSubtreeEntryId"),
        0x35e2: (0x0102, None, None, 4, None, None, "IpmOutboxEntryId"),
        0x35e4: (0x0102, None, None, 4, None, None, "IpmSentMailEntryId"),
        0x35e5: (0x0102, None, None, 4, None, None, "ViewsEntryId"),
        0x35e6: (0x0102, None, None, 4, None, None, "CommonViewsEntryId"),
        0x35e7: (0x0102, None, None, 4, None, None, "FinderEntryId"),
        0x35ec: (0x0102, None, None, 4, None, None, "ConversationsFolderEntryId"),
        0x35ee: (0x0102, None, None, 4, None, None, "AllItemsEntryId"),
        0x35ef: (0x0102, None, None, 4, None, None, "SharingFolderEntryId"),
        0x0e5c: (0x000b, None, None, 4, None, None, "CISearchEnabled"),
        0x3416: (0x0102, None, None, 4, None, None, "LocalDirectoryEntryId"),
        0x3646: (0x0102, None, None, 4, None, None, "OwnerLogonUserConfigurationCache"),
        0x3420: (0x0102, None, None, 4, None, None, "ControlDataForCalendarRepairAssistant"),
        0x3421: (0x0102, None, None, 4, None, None, "ControlDataForSharingPolicyAssistant"),
        0x3422: (0x0102, None, None, 4, None, None, "ControlDataForElcAssistant"),
        0x3423: (0x0102, None, None, 4, None, None, "ControlDataForTopNWordsAssistant"),
        0x3424: (0x0102, None, None, 4, None, None, "ControlDataForJunkEmailAssistant"),
        0x3425: (0x0102, None, None, 4, None, None, "ControlDataForCalendarSyncAssistant"),
        0x3426: (0x0003, None, None, 4, None, None, "ExternalSharingCalendarSubscriptionCount"),
        0x3427: (0x0102, None, None, 4, None, None, "ControlDataForUMReportingAssistant"),
        0x3428: (0x000b, None, None, 4, None, None, "HasUMReportData"),
        0x3429: (0x0003, None, None, 4, None, None, "InternetCalendarSubscriptionCount"),
        0x342a: (0x0003, None, None, 4, None, None, "ExternalSharingContactSubscriptionCount"),
        0x342b: (0x0003, None, None, 4, None, None, "JunkEmailSafeListDirty"),
        0x342c: (0x000b, None, None, 4, None, None, "IsTopNEnabled"),
        0x342d: (0x0102, None, None, 4, None, None, "LastSharingPolicyAppliedId"),
        0x342e: (0x0102, None, None, 4, None, None, "LastSharingPolicyAppliedHash"),
        0x342f: (0x0040, None, None, 4, None, None, "LastSharingPolicyAppliedTime"),
        0x3430: (0x0040, None, None, 4, None, None, "OofScheduleStart"),
        0x3431: (0x0040, None, None, 4, None, None, "OofScheduleEnd"),
        0x35fe: (0x0102, None, None, 4, None, None, "UnsearchableItemsStream"),
        0x3604: (0x000d, None, None, 4, None, None, "CreateTemplates"),
        0x3605: (0x000d, None, None, 4, None, None, "DetailsTable"),
        0x3607: (0x000d, None, None, 4, None, None, "Search"),
        0x360b: (0x0003, None, None, 4, None, None, "Status"),
        0x360d: (0x1003, None, None, 4, None, None, "ContentsSortOrder"),
        0x3611: (0x0102, None, None, 4, None, None, "DefCreateDl"),
        0x3612: (0x0102, None, None, 4, None, None, "DefCreateMailuser"),
        0x3614: (0x0014, None, None, 4, None, None, "ContainerModifyVersion"),
        0x3615: (0x0102, None, None, 4, None, None, "AbProviderId"),
        0x3616: (0x0102, None, None, 4, None, None, "DefaultViewEntryId"),
        0x3617: (0x0003, None, None, 4, None, None, "AssocContentCount"),
        0x3644: (0x0003, None, None, 4, None, None, "SearchFolderMsgCount"),
        0x361f: (0x000b, None, None, 4, None, None, "AllowAgeout"),
        0x6787: (0x101f, None, None, 4, None, None, "SearchBacklinkNames"),
        0x3700: (0x0102, None, None, 4, None, None, "AttachmentX400Parameters"),
        0x3716: (0x001f, None, None, 4, None, None, "AttachDisposition"),
        0x0eb0: (0x0003, None, None, 4, None, None, "SearchResultKind"),
        0x0eab: (0x001f, None, None, 4, None, None, "SearchFullText"),
        0x0eac: (0x001f, None, None, 4, None, None, "SearchFullTextSubject"),
        0x0ead: (0x001f, None, None, 4, None, None, "SearchFullTextBody"),
        0x0eae: (0x001f, None, None, 4, None, None, "SearchFullTextConversationIndex"),
        0x0eaf: (0x001f, None, None, 4, None, None, "SearchAllIndexedProps"),
        0x0eb1: (0x001f, None, None, 4, None, None, "SearchRecipients"),
        0x0eb2: (0x001f, None, None, 4, None, None, "SearchRecipientsTo"),
        0x0eb3: (0x001f, None, None, 4, None, None, "SearchRecipientsCc"),
        0x0eb4: (0x001f, None, None, 4, None, None, "SearchRecipientsBcc"),
        0x0eb5: (0x001f, None, None, 4, None, None, "SearchAccountTo"),
        0x0eb6: (0x001f, None, None, 4, None, None, "SearchAccountCc"),
        0x0eb7: (0x001f, None, None, 4, None, None, "SearchAccountBcc"),
        0x0eb8: (0x001f, None, None, 4, None, None, "SearchEmailAddressTo"),
        0x0eb9: (0x001f, None, None, 4, None, None, "SearchEmailAddressCc"),
        0x0eba: (0x001f, None, None, 4, None, None, "SearchEmailAddressBcc"),
        0x0ebb: (0x001f, None, None, 4, None, None, "SearchSmtpAddressTo"),
        0x0ebc: (0x001f, None, None, 4, None, None, "SearchSmtpAddressCc"),
        0x0ebd: (0x001f, None, None, 4, None, None, "SearchSmtpAddressBcc"),
        0x0ebe: (0x001f, None, None, 4, None, None, "SearchSender"),
        0x0ebf: (0x001f, None, None, 4, None, None, "SendYearHigh"),
        0x0ec0: (0x001f, None, None, 4, None, None, "SendYearLow"),
        0x0ec1: (0x001f, None, None, 4, None, None, "SendMonth"),
        0x0ec2: (0x001f, None, None, 4, None, None, "SendDayHigh"),
        0x0ec3: (0x001f, None, None, 4, None, None, "SendDayLow"),
        0x0ec4: (0x001f, None, None, 4, None, None, "SendQuarterHigh"),
        0x0ec5: (0x001f, None, None, 4, None, None, "SendQuarterLow"),
        0x0ec6: (0x001f, None, None, 4, None, None, "RcvdYearHigh"),
        0x0ec7: (0x001f, None, None, 4, None, None, "RcvdYearLow"),
        0x0ec8: (0x001f, None, None, 4, None, None, "RcvdMonth"),
        0x0ec9: (0x001f, None, None, 4, None, None, "RcvdDayHigh"),
        0x0eca: (0x001f, None, None, 4, None, None, "RcvdDayLow"),
        0x0ecb: (0x001f, None, None, 4, None, None, "RcvdQuarterHigh"),
        0x0ecc: (0x001f, None, None, 4, None, None, "RcvdQuarterLow"),
        0x0ecd: (0x000b, None, None, 4, None, None, "IsIrmMessage"),
        0x3a01: (0x0102, None, None, 4, None, None, "AlternateRecipient"),
        0x3a03: (0x000b, None, None, 4, None, None, "ConversionProhibited"),
        0x3a04: (0x000b, None, None, 4, None, None, "DiscloseRecipients"),
        0x3a0e: (0x000b, None, None, 4, None, None, "MailPermission"),
        0x3a13: (0x001f, None, None, 4, None, None, "OriginalDisplayName"),
        0x3a14: (0x0102, None, None, 4, None, None, "OriginalSearchKey"),
        0x3a52: (0x0048, None, None, 4, None, None, "ContactVersion"),
        0x3a53: (0x1102, None, None, 4, None, None, "ContactEntryIds"),
        0x3a54: (0x101f, None, None, 4, None, None, "ContactAddrTypes"),
        0x3a55: (0x0003, None, None, 4, None, None, "ContactDefaultAddressIndex"),
        0x3a56: (0x101f, None, None, 4, None, None, "ContactEmailAddresses"),
        0x3d00: (0x0102, None, None, 4, None, None, "StoreProviders"),
        0x3d01: (0x0102, None, None, 4, None, None, "AbProviders"),
        0x3d02: (0x0102, None, None, 4, None, None, "TransportProviders"),
        0x3d04: (0x000b, None, None, 4, None, None, "DefaultProfile"),
        0x3d05: (0x1102, None, None, 4, None, None, "AbSearchPath"),
        0x3d06: (0x0102, None, None, 4, None, None, "AbDefaultDir"),
        0x3d07: (0x0102, None, None, 4, None, None, "AbDefaultPab"),
        0x3d08: (0x0102, None, None, 4, None, None, "FilteringHooks"),
        0x3d09: (0x001f, None, None, 4, None, None, "ServiceName"),
        0x3d0a: (0x001f, None, None, 4, None, None, "ServiceDllName"),
        0x3d0c: (0x0102, None, None, 4, None, None, "ServiceUid"),
        0x3d0d: (0x0102, None, None, 4, None, None, "ServiceExtraUids"),
        0x3d0e: (0x0102, None, None, 4, None, None, "Services"),
        0x3d0f: (0x101f, None, None, 4, None, None, "ServiceSupportFiles"),
        0x3d10: (0x101f, None, None, 4, None, None, "ServiceDeleteFiles"),
        0x3d11: (0x0102, None, None, 4, None, None, "AbSearchPathUpdate"),
        0x3d12: (0x001f, None, None, 4, None, None, "ProfileName"),
        0x3fe9: (0x0003, None, None, 4, None, None, "EformsLocaleId"),
        0x6620: (0x0102, None, None, 4, None, None, "NonIpmSubtreeEntryId"),
        0x6621: (0x0102, None, None, 4, None, None, "EFormsRegistryEntryId"),
        0x6623: (0x0102, None, None, 4, None, None, "OfflineAddressBookEntryId"),
        0x6624: (0x0102, None, None, 4, None, None, "LocaleEFormsRegistryEntryId"),
        0x6625: (0x0102, None, None, 4, None, None, "LocalSiteFreeBusyEntryId"),
        0x6626: (0x0102, None, None, 4, None, None, "LocalSiteOfflineAddressBookEntryId"),
        0x6810: (0x0003, None, None, 4, None, None, "OofStateEx"),
        0x6813: (0x0040, None, None, 4, None, None, "OofStateUserChangeTime"),
        0x6814: (0x0102, None, None, 4, None, None, "UserOofSettingsItemId"),
        0x35e3: (0x0102, None, None, 4, None, None, "IpmWasteBasketEntryId"),
        0x3011: (0x0102, None, None, 4, None, None, "ForceUserClientBackoff"),
        0x6618: (0x0003, None, None, 4, None, None, "InTransitStatus"),
        0x662a: (0x000b, None, None, 4, None, None, "TransferEnabled"),
        0x65f6: (0x101f, None, None, 4, None, None, "ImapSubscribeList"),
        0x676c: (0x0102, None, None, 4, None, None, "MapiEntryIdGuid"),
        0x662f: (0x000d, None, None, 4, None, None, "FastTransfer"),
        0x681a: (0x000b, None, None, 4, None, None, "MailboxQuarantined"),
        0x6761: (0x0102, None, None, 4, None, None, "NextLocalId"),
        0x6670: (0x0102, None, None, 4, None, None, "LongTermEntryIdFromTable"),
        0x0e27: (0x0102, None, None, 4, None, None, "NTSD"),
        0x3d21: (0x0102, None, None, 4, None, None, "AdminNTSD"),
        0x0f00: (0x0102, None, None, 4, None, None, "FreeBusyNTSD"),
        0x0e3f: (0x0102, None, None, 4, None, None, "AclTableAndNTSD"),
        0x6707: (0x001f, None, None, 4, None, None, "UrlName"),
        0x6698: (0x0102, None, None, 4, None, None, "ReplicaList"),
        0x663f: (0x000b, None, None, 4, None, None, "HasModerator"),
        0x3fe6: (0x000b, None, None, 4, None, None, "PublishInAddressBook"),
        0x6699: (0x0003, None, None, 4, None, None, "OverallAgeLimit"),
        0x66c4: (0x0003, None, None, 4, None, None, "RetentionAgeLimit"),
        0x6779: (0x0003, None, None, 4, None, None, "PfQuotaStyle"),
        0x677b: (0x0003, None, None, 4, None, None, "PfStorageQuota"),
        0x6721: (0x0003, None, None, 4, None, None, "PfOverHardQuotaLimit"),
        0x6722: (0x0003, None, None, 4, None, None, "PfMsgSizeLimit"),
        0x6690: (0x0003, None, None, 4, None, None, "ReplicationStyle"),
        0x6691: (0x0102, None, None, 4, None, None, "ReplicationSchedule"),
        0x66c5: (0x000b, None, None, 4, None, None, "DisablePeruserRead"),
        0x6701: (0x0003, None, None, 4, None, None, "PfMsgAgeLimit"),
        0x671d: (0x0102, None, None, 4, None, None, "PfProxy"),
        0x3d2f: (0x0003, None, None, 4, None, None, "SystemFolderFlags"),
        0x3fd9: (0x001f, None, None, 4, None, None, "Preview"),
        0x6828: (0x0102, None, None, 4, None, None, "LocalDirectory"),
        0x0e63: (0x0003, None, None, 4, None, None, "SendFlags"),
        0x3fe1: (0x000d, None, None, 4, None, None, "RulesTable"),
        0x4080: (0x0003, None, None, 4, None, None, "OofReplyType"),
        0x4081: (0x001f, None, None, 4, None, None, "ElcAutoCopyLabel"),
        0x6716: (0x0102, None, None, 4, None, None, "ElcAutoCopyTag"),
        0x6717: (0x0102, None, None, 4, None, None, "ElcMoveDate"),
        0x6829: (0x001f, None, None, 4, None, None, "MemberEmail"),
        0x682a: (0x001f, None, None, 4, None, None, "MemberExternalId"),
        0x682b: (0x0102, None, None, 4, None, None, "MemberSID"),
        0x662c: (0x000d, None, None, 4, None, None, "HierarchySynchronizer"),
        0x662d: (0x000d, None, None, 4, None, None, "ContentsSynchronizer"),
        0x662e: (0x000d, None, None, 4, None, None, "Collector"),
        0x6609: (0x000b, None, None, 4, None, None, "SendRichInfoOnly"),
        0x6637: (0x000b, None, None, 4, None, None, "SendNativeBody"),
        0x6608: (0x0003, None, None, 4, None, None, "InternetTransmitInfo"),
        0x6610: (0x0003, None, None, 4, None, None, "InternetMessageFormat"),
        0x6611: (0x0003, None, None, 4, None, None, "InternetMessageTextFormat"),
        0x6615: (0x0003, None, None, 4, None, None, "InternetRequestLines"),
        0x6616: (0x0003, None, None, 4, None, None, "InternetHeaderLength"),
        0x6617: (0x0003, None, None, 4, None, None, "InternetAddressingOptions"),
        0x661a: (0x001f, None, None, 4, None, None, "InternetTemporaryFilename"),
        0x6570: (0x0003, None, None, 4, None, None, "InternetExternalNewsItem"),
        0x661e: (0x0003, None, None, 4, None, None, "InternetRequestHeaders"),
        0x6631: (0x001f, None, None, 4, None, None, "InternetClientHostIPName"),
        0x66c0: (0x0040, None, None, 4, None, None, "ConnectTime"),
        0x66c1: (0x0003, None, None, 4, None, None, "ConnectFlags"),
        0x66c2: (0x0003, None, None, 4, None, None, "LogonCount"),
        0x669f: (0x001f, None, None, 4, None, None, "HostAddress"),
        0x66a0: (0x001f, None, None, 4, None, None, "NTUserName"),
        0x66a2: (0x0040, None, None, 4, None, None, "LastLogonTime"),
        0x66a3: (0x0040, None, None, 4, None, None, "LastLogoffTime"),
        0x66a4: (0x0003, None, None, 4, None, None, "StorageLimitInformation"),
        0x66a5: (0x000b, None, None, 4, None, None, "InternetMdns"),
        0x669b: (0x0014, None, None, 4, None, None, "DeletedMessageSizeExtended"),
        0x669c: (0x0014, None, None, 4, None, None, "DeletedNormalMessageSizeExtended"),
        0x669d: (0x0014, None, None, 4, None, None, "DeleteAssocMessageSizeExtended"),
        0x6640: (0x0003, None, None, 4, None, None, "DeletedMsgCount"),
        0x6708: (0x0040, None, None, 4, None, None, "DateDiscoveredAbsentInDS"),
        0x6778: (0x001f, None, None, 4, None, None, "AdminNickName"),
        0x6723: (0x0003, None, None, 4, None, None, "QuotaReceiveThreshold"),
        0x66df: (0x0040, None, None, 4, None, None, "LastOpTime"),
        0x66ff: (0x0003, None, None, 4, None, None, "PacketRate"),
        0x66cc: (0x0040, None, None, 4, None, None, "LogonTime"),
        0x66cd: (0x0003, None, None, 4, None, None, "LogonFlags"),
        0x6764: (0x0014, None, None, 4, None, None, "MsgHeaderFid"),
        0x66c9: (0x001f, None, None, 4, None, None, "MailboxDisplayName"),
        0x66c8: (0x001f, None, None, 4, None, None, "MailboxDN"),
        0x66cb: (0x001f, None, None, 4, None, None, "UserDisplayName"),
        0x66ca: (0x001f, None, None, 4, None, None, "UserDN"),
        0x6710: (0x0014, None, None, 4, None, None, "SessionId"),
        0x66cf: (0x0003, None, None, 4, None, None, "OpenMessageCount"),
        0x66d0: (0x0003, None, None, 4, None, None, "OpenFolderCount"),
        0x66d1: (0x0003, None, None, 4, None, None, "OpenAttachCount"),
        0x66d2: (0x0003, None, None, 4, None, None, "OpenContentCount"),
        0x66d3: (0x0003, None, None, 4, None, None, "OpenHierarchyCount"),
        0x66d4: (0x0003, None, None, 4, None, None, "OpenNotifyCount"),
        0x66d5: (0x0003, None, None, 4, None, None, "OpenAttachTableCount"),
        0x66d6: (0x0003, None, None, 4, None, None, "OpenACLTableCount"),
        0x66d7: (0x0003, None, None, 4, None, None, "OpenRulesTableCount"),
        0x66d8: (0x0003, None, None, 4, None, None, "OpenStreamsCount"),
        0x66d9: (0x0003, None, None, 4, None, None, "OpenFXSrcStreamCount"),
        0x66da: (0x0003, None, None, 4, None, None, "OpenFXDestStreamCount"),
        0x66db: (0x0003, None, None, 4, None, None, "OpenContentRegularCount"),
        0x66dc: (0x0003, None, None, 4, None, None, "OpenContentCategCount"),
        0x66dd: (0x0003, None, None, 4, None, None, "OpenContentRestrictedCount"),
        0x66de: (0x0003, None, None, 4, None, None, "OpenContentCategAndRestrictedCount"),
        0x66b8: (0x0003, None, None, 4, None, None, "MessagingOpRate"),
        0x66b9: (0x0003, None, None, 4, None, None, "FolderOpRate"),
        0x66ba: (0x0003, None, None, 4, None, None, "TableOpRate"),
        0x66bb: (0x0003, None, None, 4, None, None, "TransferOpRate"),
        0x66bc: (0x0003, None, None, 4, None, None, "StreamOpRate"),
        0x66bd: (0x0003, None, None, 4, None, None, "ProgressOpRate"),
        0x66be: (0x0003, None, None, 4, None, None, "OtherOpRate"),
        0x66bf: (0x0003, None, None, 4, None, None, "TotalOpRate"),
        0x671f: (0x000b, None, None, 4, None, None, "PfProxyRequired"),
        0x6724: (0x0102, None, None, 4, None, None, "ClientIP"),
        0x6725: (0x0102, None, None, 4, None, None, "ClientIPMask"),
        0x6726: (0x0102, None, None, 4, None, None, "ClientMacAddress"),
        0x6727: (0x001f, None, None, 4, None, None, "ClientMachineName"),
        0x6728: (0x0003, None, None, 4, None, None, "ClientAdapterSpeed"),
        0x6729: (0x0003, None, None, 4, None, None, "ClientRpcsAttempted"),
        0x672a: (0x0003, None, None, 4, None, None, "ClientRpcsSucceeded"),
        0x672b: (0x0102, None, None, 4, None, None, "ClientRpcErrors"),
        0x672c: (0x0003, None, None, 4, None, None, "ClientLatency"),
        0x666f: (0x000b, None, None, 4, None, None, "SubmittedByAdmin"),
        0x680d: (0x0003, None, None, 4, None, None, "ObjectClassFlags"),
        0x682d: (0x0003, None, None, 4, None, None, "MaxMessageSize"),
        0x672d: (0x0003, None, None, 4, None, None, "TimeInServer"),
        0x672e: (0x0003, None, None, 4, None, None, "TimeInCPU"),
        0x672f: (0x0003, None, None, 4, None, None, "ROPCount"),
        0x6730: (0x0003, None, None, 4, None, None, "PageRead"),
        0x6731: (0x0003, None, None, 4, None, None, "PagePreread"),
        0x6732: (0x0003, None, None, 4, None, None, "LogRecordCount"),
        0x6733: (0x0003, None, None, 4, None, None, "LogRecordBytes"),
        0x6734: (0x0003, None, None, 4, None, None, "LdapReads"),
        0x6735: (0x0003, None, None, 4, None, None, "LdapSearches"),
        0x6736: (0x001f, None, None, 4, None, None, "DigestCategory"),
        0x6737: (0x0003, None, None, 4, None, None, "SampleId"),
        0x6738: (0x0040, None, None, 4, None, None, "SampleTime"),
        0x661f: (0x0102, None, None, 4, None, None, "DeferredActionFolderEntryID"),
        0x3fff: (0x0003, None, None, 4, None, None, "RulesSize"),
        0x668d: (0x0002, None, None, 4, None, None, "RuleVersion"),
        0x65f2: (0x0002, None, None, 4, None, None, "RuleMsgVersion"),
        0x3642: (0x0102, None, None, 4, None, None, "DAMReferenceMessageEntryID"),
        0x65f5: (0x0040, None, None, 4, None, None, "ImapInternalDate"),
        0x6751: (0x0003, None, None, 4, None, None, "NextArticleId"),
        0x6752: (0x0003, None, None, 4, None, None, "ImapLastArticleId"),
        0x0e2f: (0x0003, None, None, 4, None, None, "ImapId"),
        0x0e32: (0x0002, None, None, 4, None, None, "OriginalSourceServerVersion"),
        0x5806: (0x000b, None, None, 4, None, None, "DeliverAsRead"),
        0x67fe: (0x0014, None, None, 4, None, None, "ReadCn"),
        0x6808: (0x0003, None, None, 4, None, None, "EventMask"),
        0x676a: (0x0102, None, None, 4, None, None, "EventMailboxGuid"),
        0x6815: (0x0003, None, None, 4, None, None, "DocumentId"),
        0x6702: (0x000b, None, None, 4, None, None, "BeingDeleted"),
        0x678d: (0x0102, None, None, 4, None, None, "FolderCDN"),
        0x67f6: (0x0003, None, None, 4, None, None, "ModifiedCount"),
        0x67f7: (0x0003, None, None, 4, None, None, "DeleteState"),
        0x66fe: (0x001f, None, None, 4, None, None, "AdminDisplayName"),
        0x66a9: (0x0040, None, None, 4, None, None, "LastAccessTime"),
        0x6830: (0x0040, None, None, 4, None, None, "LastUserAccessTime"),
        0x6831: (0x0040, None, None, 4, None, None, "LastUserModificationTime"),
        0x66b4: (0x0014, None, None, 4, None, None, "AssocMessageSizeExtended"),
        0x66b5: (0x001f, None, None, 4, None, None, "FolderPathName"),
        0x66b6: (0x0003, None, None, 4, None, None, "OwnerCount"),
        0x66b7: (0x0003, None, None, 4, None, None, "ContactCount"),
        0x7c01: (0x001f, None, None, 4, None, None, "MessageAudioNotes"),
        0x3ff5: (0x0003, None, None, 4, None, None, "StorageQuotaLimit"),
        0x3ff6: (0x0003, None, None, 4, None, None, "ExcessStorageUsed"),
        0x3ff7: (0x001f, None, None, 4, None, None, "SvrGeneratingQuotaMsg"),
        0x3fc2: (0x000b, None, None, 4, None, None, "PrimaryMbxOverQuota"),
        0x65c6: (0x0003, None, None, 4, None, None, "SecureSubmitFlags"),
        0x673e: (0x0102, None, None, 4, None, None, "PropertyGroupInformation"),
        0x6784: (0x0102, None, None, 4, None, None, "SearchRestriction"),
        0x67b0: (0x00fd, None, None, 4, None, None, "ViewRestriction"),
        0x6788: (0x0003, None, None, 4, None, None, "LCIDRestriction"),
        0x676e: (0x0003, None, None, 4, None, None, "LCID"),
        0x67f3: (0x0040, None, None, 4, None, None, "ViewAccessTime"),
        0x689e: (0x0003, None, None, 4, None, None, "CategCount"),
        0x6819: (0x000b, None, None, 4, None, None, "SoftDeletedFilter"),
        0x681b: (0x000b, None, None, 4, None, None, "ConversationsFilter"),
        0x689c: (0x0003, None, None, 4, None, None, "DVUIdLowest"),
        0x689d: (0x0003, None, None, 4, None, None, "DVUIdHighest"),
        0x6880: (0x101f, None, None, 4, None, None, "ConversationMvFrom"),
        0x6881: (0x101f, None, None, 4, None, None, "ConversationMvFromMailboxWide"),
        0x6882: (0x101f, None, None, 4, None, None, "ConversationMvTo"),
        0x6883: (0x101f, None, None, 4, None, None, "ConversationMvToMailboxWide"),
        0x6884: (0x0040, None, None, 4, None, None, "ConversationMsgDeliveryTime"),
        0x6885: (0x0040, None, None, 4, None, None, "ConversationMsgDeliveryTimeMailboxWide"),
        0x6886: (0x101f, None, None, 4, None, None, "ConversationCategories"),
        0x6887: (0x101f, None, None, 4, None, None, "ConversationCategoriesMailboxWide"),
        0x6888: (0x0003, None, None, 4, None, None, "ConversationFlagStatus"),
        0x6889: (0x0003, None, None, 4, None, None, "ConversationFlagStatusMailboxWide"),
        0x688a: (0x0040, None, None, 4, None, None, "ConversationFlagCompleteTime"),
        0x688b: (0x0040, None, None, 4, None, None, "ConversationFlagCompleteTimeMailboxWide"),
        0x688c: (0x000b, None, None, 4, None, None, "ConversationHasAttach"),
        0x688d: (0x000b, None, None, 4, None, None, "ConversationHasAttachMailboxWide"),
        0x688e: (0x0003, None, None, 4, None, None, "ConversationContentCount"),
        0x688f: (0x0003, None, None, 4, None, None, "ConversationContentCountMailboxWide"),
        0x6893: (0x0003, None, None, 4, None, None, "ConversationMessageSizeMailboxWide"),
        0x6894: (0x101f, None, None, 4, None, None, "ConversationMessageClasses"),
        0x6895: (0x101f, None, None, 4, None, None, "ConversationMessageClassesMailboxWide"),
        0x6896: (0x0003, None, None, 4, None, None, "ConversationReplyForwardState"),
        0x6897: (0x0003, None, None, 4, None, None, "ConversationReplyForwardStateMailboxWide"),
        0x6898: (0x0003, None, None, 4, None, None, "ConversationImportance"),
        0x6899: (0x0003, None, None, 4, None, None, "ConversationImportanceMailboxWide"),
        0x689a: (0x101f, None, None, 4, None, None, "ConversationMvFromUnread"),
        0x689b: (0x101f, None, None, 4, None, None, "ConversationMvFromUnreadMailboxWide"),
        0x68a0: (0x1102, None, None, 4, None, None, "ConversationMvItemIds"),
        0x68a1: (0x1102, None, None, 4, None, None, "ConversationMvItemIdsMailboxWide"),
        0x68a2: (0x000b, None, None, 4, None, None, "ConversationHasIrm"),
        0x68a3: (0x000b, None, None, 4, None, None, "ConversationHasIrmMailboxWide"),
        0x682c: (0x0040, None, None, 4, None, None, "TransportSyncSubscriptionListTimestamp"),
        0x3690: (0x0102, None, None, 4, None, None, "TransportRulesSnapshot"),
        0x3691: (0x0048, None, None, 4, None, None, "TransportRulesSnapshotId"),
        0x7c05: (0x0040, None, None, 4, None, None, "DeletedMessageSizeExtendedLastModificationTime"),
        0x0082: (0x001f, None, None, 4, None, None, "ReportOriginalSender"),
        0x0083: (0x001f, None, None, 4, None, None, "ReportDispositionToNames"),
        0x0084: (0x001f, None, None, 4, None, None, "ReportDispositionToEmailAddress"),
        0x0085: (0x001f, None, None, 4, None, None, "ReportDispositionOptions"),
        0x0086: (0x0002, None, None, 4, None, None, "RichContent"),
        0x0100: (0x101f, None, None, 4, None, None, "AdministratorEMail"),
        0x0c24: (0x0102, None, None, 4, None, None, "ParticipantSID"),
        0x0c25: (0x0102, None, None, 4, None, None, "ParticipantGuid"),
        0x0c26: (0x001f, None, None, 4, None, None, "ToGroupExpansionRecipients"),
        0x0c27: (0x001f, None, None, 4, None, None, "CcGroupExpansionRecipients"),
        0x0c28: (0x001f, None, None, 4, None, None, "BccGroupExpansionRecipients"),
        0x0e0b: (0x0102, None, None, 4, None, None, "ImmutableEntryId"),
        0x0e2e: (0x0003, None, None, 4, None, None, "MessageIsHidden"),
        0x0e33: (0x001f, None, None, 4, None, None, "OlcPopId"),
        0x0e38: (0x0003, None, None, 4, None, None, "ReplFlags"),
        0x0e40: (0x0102, None, None, 4, None, None, "SenderGuid"),
        0x0e41: (0x0102, None, None, 4, None, None, "SentRepresentingGuid"),
        0x0e42: (0x0102, None, None, 4, None, None, "OriginalSenderGuid"),
        0x0e43: (0x0102, None, None, 4, None, None, "OriginalSentRepresentingGuid"),
        0x0e44: (0x0102, None, None, 4, None, None, "ReadReceiptGuid"),
        0x0e45: (0x0102, None, None, 4, None, None, "ReportGuid"),
        0x0e46: (0x0102, None, None, 4, None, None, "OriginatorGuid"),
        0x0e47: (0x0102, None, None, 4, None, None, "ReportDestinationGuid"),
        0x0e48: (0x0102, None, None, 4, None, None, "OriginalAuthorGuid"),
        0x0e49: (0x0102, None, None, 4, None, None, "ReceivedByGuid"),
        0x0e4a: (0x0102, None, None, 4, None, None, "ReceivedRepresentingGuid"),
        0x0e4b: (0x0102, None, None, 4, None, None, "CreatorGuid"),
        0x0e4c: (0x0102, None, None, 4, None, None, "LastModifierGuid"),
        0x0e4d: (0x0102, None, None, 4, None, None, "SenderSID"),
        0x0e4e: (0x0102, None, None, 4, None, None, "SentRepresentingSID"),
        0x0e4f: (0x0102, None, None, 4, None, None, "OriginalSenderSid"),
        0x0e50: (0x0102, None, None, 4, None, None, "OriginalSentRepresentingSid"),
        0x0e51: (0x0102, None, None, 4, None, None, "ReadReceiptSid"),
        0x0e52: (0x0102, None, None, 4, None, None, "ReportSid"),
        0x0e53: (0x0102, None, None, 4, None, None, "OriginatorSid"),
        0x0e54: (0x0102, None, None, 4, None, None, "ReportDestinationSid"),
        0x0e55: (0x0102, None, None, 4, None, None, "OriginalAuthorSid"),
        0x0e56: (0x0102, None, None, 4, None, None, "ReceivedBySid"),
        0x0e57: (0x0102, None, None, 4, None, None, "ReceivedRepresentingSid"),
        0x0e58: (0x0102, None, None, 4, None, None, "CreatorSID"),
        0x0e59: (0x0102, None, None, 4, None, None, "LastModifierSid"),
        0x0e5a: (0x0102, None, None, 4, None, None, "RecipientCAI"),
        0x0e5b: (0x0102, None, None, 4, None, None, "ConversationCreatorSID"),
        0x0e5d: (0x000b, None, None, 4, None, None, "IsUserKeyDecryptPossible"),
        0x0e5e: (0x0003, None, None, 4, None, None, "MaxIndices"),
        0x0e5f: (0x0014, None, None, 4, None, None, "SourceFid"),
        0x0e60: (0x0102, None, None, 4, None, None, "PFContactsGuid"),
        0x0e61: (0x0003, None, None, 4, None, None, "UrlCompNamePostfix"),
        0x0e62: (0x000b, None, None, 4, None, None, "URLCompNameSet"),
        0x0e64: (0x0003, None, None, 4, None, None, "DeletedSubfolderCount"),
        0x0e68: (0x0003, None, None, 4, None, None, "MaxCachedViews"),
        0x0e6b: (0x001f, None, None, 4, None, None, "AdminNTSecurityDescriptorAsXML"),
        0x0e6c: (0x001f, None, None, 4, None, None, "CreatorSidAsXML"),
        0x0e6d: (0x001f, None, None, 4, None, None, "LastModifierSidAsXML"),
        0x0e6e: (0x001f, None, None, 4, None, None, "SenderSIDAsXML"),
        0x0e6f: (0x001f, None, None, 4, None, None, "SentRepresentingSidAsXML"),
        0x0e70: (0x001f, None, None, 4, None, None, "OriginalSenderSIDAsXML"),
        0x0e71: (0x001f, None, None, 4, None, None, "OriginalSentRepresentingSIDAsXML"),
        0x0e72: (0x001f, None, None, 4, None, None, "ReadReceiptSIDAsXML"),
        0x0e73: (0x001f, None, None, 4, None, None, "ReportSIDAsXML"),
        0x0e74: (0x001f, None, None, 4, None, None, "OriginatorSidAsXML"),
        0x0e75: (0x001f, None, None, 4, None, None, "ReportDestinationSIDAsXML"),
        0x0e76: (0x001f, None, None, 4, None, None, "OriginalAuthorSIDAsXML"),
        0x0e77: (0x001f, None, None, 4, None, None, "ReceivedBySIDAsXML"),
        0x0e78: (0x001f, None, None, 4, None, None, "ReceivedRepersentingSIDAsXML"),
        0x0e7a: (0x0102, None, None, 4, None, None, "MergeMidsetDeleted"),
        0x0e7b: (0x0102, None, None, 4, None, None, "ReserveRangeOfIDs"),
        0x0e97: (0x001f, None, None, 4, None, None, "AddrTo"),
        0x0e98: (0x001f, None, None, 4, None, None, "AddrCc"),
        0x0e9f: (0x101f, None, None, 4, None, None, "EntourageSentHistory"),
        0x0ea2: (0x0003, None, None, 4, None, None, "ProofInProgress"),
        0x0ea5: (0x001f, None, None, 4, None, None, "SearchAttachmentsOLK"),
        0x0ea6: (0x001f, None, None, 4, None, None, "SearchRecipEmailTo"),
        0x0ea7: (0x001f, None, None, 4, None, None, "SearchRecipEmailCc"),
        0x0ea8: (0x001f, None, None, 4, None, None, "SearchRecipEmailBcc"),
        0x0eaa: (0x0003, None, None, 4, None, None, "SFGAOFlags"),
        0x0ece: (0x000b, None, None, 4, None, None, "SearchIsPartiallyIndexed"),
        0x0ecf: (0x001f, None, None, 4, None, None, "SearchUniqueBody"),
        0x0ed0: (0x0003, None, None, 4, None, None, "SearchErrorCode"),
        0x0ed1: (0x0040, None, None, 4, None, None, "SearchReceivedTime"),
        0x0ed2: (0x0003, None, None, 4, None, None, "SearchNumberOfTopRankedResults"),
        0x0ed3: (0x0003, None, None, 4, None, None, "SearchControlFlags"),
        0x0ed4: (0x001f, None, None, 4, None, None, "SearchRankingModel"),
        0x0ed5: (0x0003, None, None, 4, None, None, "SearchMinimumNumberOfDateOrderedResults"),
        0x0ed6: (0x001f, None, None, 4, None, None, "SearchSharePointOnlineSearchableProps"),
        0x0ed7: (0x0002, None, None, 4, None, None, "SearchRelevanceRankedResults"),
        0x0edd: (0x0102, None, None, 4, None, None, "MailboxSyncState"),
        0x0f01: (0x0040, None, None, 4, None, None, "RenewTime"),
        0x0f02: (0x0040, None, None, 4, None, None, "DeliveryOrRenewTime"),
        0x0f03: (0x0102, None, None, 4, None, None, "ConversationThreadId"),
        0x0f04: (0x0003, None, None, 4, None, None, "LikeCount"),
        0x0f05: (0x0002, None, None, 4, None, None, "RichContentDeprecated"),
        0x0f06: (0x0003, None, None, 4, None, None, "PeopleCentricConversationId"),
        0x0f07: (0x0040, None, None, 4, None, None, "ReturnTime"),
        0x0f08: (0x0040, None, None, 4, None, None, "LastAttachmentsProcessedTime"),
        0x0f0a: (0x0040, None, None, 4, None, None, "LastActivityTime"),
        0x100a: (0x0102, None, None, 4, None, None, "AlternateBestBody"),
        0x100c: (0x0102, None, None, 4, None, None, "IsIntegJobCorruptions"),
        0x100e: (0x0002, None, None, 4, None, None, "IsIntegJobPriority"),
        0x100f: (0x0005, None, None, 4, None, None, "IsIntegJobTimeInServer"),
        0x1017: (0x0102, None, None, 4, None, None, "AnnotationToken"),
        0x1030: (0x001f, None, None, 4, None, None, "InternetApproved"),
        0x1033: (0x001f, None, None, 4, None, None, "InternetFollowupTo"),
        0x1036: (0x001f, None, None, 4, None, None, "InetNewsgroups"),
        0x103d: (0x0102, None, None, 4, None, None, "PostReplyFolderEntries"),
        0x1040: (0x001f, None, None, 4, None, None, "NNTPXRef"),
        0x1084: (0x0003, None, None, 4, None, None, "Relevance"),
        0x1092: (0x0003, None, None, 4, None, None, "FormatPT"),
        0x10c0: (0x0102, None, None, 4, None, None, "SMTPTempTblData"),
        0x10c1: (0x0003, None, None, 4, None, None, "SMTPTempTblData2"),
        0x10c2: (0x0102, None, None, 4, None, None, "SMTPTempTblData3"),
        0x10f0: (0x0102, None, None, 4, None, None, "IMAPCachedMsgSize"),
        0x10f2: (0x000b, None, None, 4, None, None, "DisableFullFidelity"),
        0x10f3: (0x001f, None, None, 4, None, None, "UrlCompName"),
        0x10f5: (0x000b, None, None, 4, None, None, "AttrSystem"),
        0x1204: (0x1002, None, None, 4, None, None, "PredictedActions"),
        0x1205: (0x1002, None, None, 4, None, None, "GroupingActions"),
        0x1206: (0x0003, None, None, 4, None, None, "PredictedActionsSummary"),
        0x1207: (0x000b, None, None, 4, None, None, "IsClutter"),
        0x120b: (0x0102, None, None, 4, None, None, "OriginalDeliveryFolderInfo"),
        0x120c: (0x0102, None, None, 4, None, None, "ClutterFolderEntryIdWellKnown"),
        0x120d: (0x0102, None, None, 4, None, None, "BirthdayCalendarFolderEntryIdWellKnown"),
        0x120e: (0x0102, None, None, 4, None, None, "InferencePredictedClutterReasons"),
        0x120f: (0x0102, None, None, 4, None, None, "InferencePredictedNotClutterReasons"),
        0x1210: (0x0102, None, None, 4, None, None, "BookingStaffFolderEntryId"),
        0x1211: (0x0102, None, None, 4, None, None, "BookingServicesFolderEntryId"),
        0x1212: (0x0003, None, None, 4, None, None, "InferenceClassificationInternal"),
        0x1213: (0x0003, None, None, 4, None, None, "InferenceClassification"),
        0x1214: (0x0102, None, None, 4, None, None, "SchedulesFolderEntryId"),
        0x1215: (0x0102, None, None, 4, None, None, "AllTaggedItemsFolderEntryId"),
        0x1236: (0x0048, None, None, 4, None, None, "WellKnownFolderGuid"),
        0x1237: (0x0003, None, None, 4, None, None, "RemoteFolderSyncStatus"),
        0x1238: (0x0102, None, None, 4, None, None, "BookingCustomQuestionsFolderEntryId"),
        0x300e: (0x000b, None, None, 4, None, None, "UserInformationAntispamBypassEnabled"),
        0x300f: (0x001f, None, None, 4, None, None, "UserInformationArchiveDomain"),
        0x3017: (0x0040, None, None, 4, None, None, "UserInformationBirthdate"),
        0x3020: (0x001f, None, None, 4, None, None, "UserInformationCountryOrRegion"),
        0x3021: (0x001f, None, None, 4, None, None, "UserInformationDefaultMailTip"),
        0x3022: (0x000b, None, None, 4, None, None, "UserInformationDeliverToMailboxAndForward"),
        0x3023: (0x101f, None, None, 4, None, None, "UserInformationDescription"),
        0x3024: (0x0048, None, None, 4, None, None, "UserInformationDisabledArchiveGuid"),
        0x3025: (0x000b, None, None, 4, None, None, "UserInformationDowngradeHighPriorityMessagesEnabled"),
        0x3026: (0x000b, None, None, 4, None, None, "UserInformationECPEnabled"),
        0x3027: (0x000b, None, None, 4, None, None, "UserInformationEmailAddressPolicyEnabled"),
        0x3028: (0x000b, None, None, 4, None, None, "UserInformationEwsAllowEntourage"),
        0x3029: (0x000b, None, None, 4, None, None, "UserInformationEwsAllowMacOutlook"),
        0x302a: (0x000b, None, None, 4, None, None, "UserInformationEwsAllowOutlook"),
        0x302b: (0x0003, None, None, 4, None, None, "UserInformationEwsApplicationAccessPolicy"),
        0x302c: (0x0003, None, None, 4, None, None, "UserInformationEwsEnabled"),
        0x302d: (0x101f, None, None, 4, None, None, "UserInformationEwsExceptions"),
        0x302e: (0x101f, None, None, 4, None, None, "UserInformationEwsWellKnownApplicationAccessPolicies"),
        0x302f: (0x0048, None, None, 4, None, None, "UserInformationExchangeGuid"),
        0x3030: (0x0003, None, None, 4, None, None, "UserInformationExternalOofOptions"),
        0x3031: (0x001f, None, None, 4, None, None, "UserInformationFirstName"),
        0x3032: (0x001f, None, None, 4, None, None, "UserInformationForwardingSmtpAddress"),
        0x3033: (0x001f, None, None, 4, None, None, "UserInformationGender"),
        0x3034: (0x001f, None, None, 4, None, None, "UserInformationGenericForwardingAddress"),
        0x3035: (0x001f, None, None, 4, None, None, "UserInformationGeoCoordinates"),
        0x3036: (0x0003, None, None, 4, None, None, "UserInformationHABSeniorityIndex"),
        0x3037: (0x000b, None, None, 4, None, None, "UserInformationHasActiveSyncDevicePartnership"),
        0x3038: (0x000b, None, None, 4, None, None, "UserInformationHiddenFromAddressListsEnabled"),
        0x3039: (0x000b, None, None, 4, None, None, "UserInformationHiddenFromAddressListsValue"),
        0x303a: (0x001f, None, None, 4, None, None, "UserInformationHomePhone"),
        0x303b: (0x000b, None, None, 4, None, None, "UserInformationImapEnabled"),
        0x303c: (0x000b, None, None, 4, None, None, "UserInformationImapEnableExactRFC822Size"),
        0x303d: (0x000b, None, None, 4, None, None, "UserInformationImapForceICalForCalendarRetrievalOption"),
        0x303e: (0x0003, None, None, 4, None, None, "UserInformationImapMessagesRetrievalMimeFormat"),
        0x303f: (0x0003, None, None, 4, None, None, "UserInformationImapProtocolLoggingEnabled"),
        0x3040: (0x000b, None, None, 4, None, None, "UserInformationImapSuppressReadReceipt"),
        0x3041: (0x000b, None, None, 4, None, None, "UserInformationImapUseProtocolDefaults"),
        0x3042: (0x000b, None, None, 4, None, None, "UserInformationIncludeInGarbageCollection"),
        0x3043: (0x001f, None, None, 4, None, None, "UserInformationInitials"),
        0x3044: (0x101f, None, None, 4, None, None, "UserInformationInPlaceHolds"),
        0x3045: (0x000b, None, None, 4, None, None, "UserInformationInternalOnly"),
        0x3046: (0x001f, None, None, 4, None, None, "UserInformationInternalUsageLocation"),
        0x3047: (0x0003, None, None, 4, None, None, "UserInformationInternetEncoding"),
        0x3048: (0x000b, None, None, 4, None, None, "UserInformationIsCalculatedTargetAddress"),
        0x3049: (0x000b, None, None, 4, None, None, "UserInformationIsExcludedFromServingHierarchy"),
        0x304a: (0x000b, None, None, 4, None, None, "UserInformationIsHierarchyReady"),
        0x304b: (0x000b, None, None, 4, None, None, "UserInformationIsInactiveMailbox"),
        0x304c: (0x000b, None, None, 4, None, None, "UserInformationIsSoftDeletedByDisable"),
        0x304d: (0x000b, None, None, 4, None, None, "UserInformationIsSoftDeletedByRemove"),
        0x304e: (0x001f, None, None, 4, None, None, "UserInformationIssueWarningQuota"),
        0x304f: (0x001f, None, None, 4, None, None, "UserInformationJournalArchiveAddress"),
        0x3051: (0x0040, None, None, 4, None, None, "UserInformationLastExchangeChangedTime"),
        0x3052: (0x001f, None, None, 4, None, None, "UserInformationLastName"),
        0x3053: (0x0040, None, None, 4, None, None, "UserInformationLastAliasSyncSubmittedTime"),
        0x3054: (0x000b, None, None, 4, None, None, "UserInformationLEOEnabled"),
        0x3055: (0x1003, None, None, 4, None, None, "UserInformationLocaleID"),
        0x3056: (0x0003, None, None, 4, None, None, "UserInformationLongitude"),
        0x3057: (0x0003, None, None, 4, None, None, "UserInformationMacAttachmentFormat"),
        0x3058: (0x0048, None, None, 4, None, None, "UserInformationMailboxContainerGuid"),
        0x3059: (0x001f, None, None, 4, None, None, "UserInformationMailboxMoveBatchName"),
        0x305a: (0x001f, None, None, 4, None, None, "UserInformationMailboxMoveRemoteHostName"),
        0x305b: (0x0003, None, None, 4, None, None, "UserInformationMailboxMoveStatus"),
        0x305c: (0x001f, None, None, 4, None, None, "UserInformationMailboxRelease"),
        0x305d: (0x101f, None, None, 4, None, None, "UserInformationMailTipTranslations"),
        0x305e: (0x000b, None, None, 4, None, None, "UserInformationMAPIBlockOutlookNonCachedMode"),
        0x305f: (0x000b, None, None, 4, None, None, "UserInformationMAPIBlockOutlookRpcHttp"),
        0x3060: (0x001f, None, None, 4, None, None, "UserInformationMAPIBlockOutlookVersions"),
        0x3061: (0x0003, None, None, 4, None, None, "UserInformationMailboxStatus"),
        0x3062: (0x000b, None, None, 4, None, None, "UserInformationMapiRecipient"),
        0x3063: (0x0003, None, None, 4, None, None, "UserInformationMaxBlockedSenders"),
        0x3064: (0x001f, None, None, 4, None, None, "UserInformationMaxReceiveSize"),
        0x3065: (0x0003, None, None, 4, None, None, "UserInformationMaxSafeSenders"),
        0x3066: (0x001f, None, None, 4, None, None, "UserInformationMaxSendSize"),
        0x3067: (0x001f, None, None, 4, None, None, "UserInformationMemberName"),
        0x3068: (0x0003, None, None, 4, None, None, "UserInformationMessageBodyFormat"),
        0x3069: (0x0003, None, None, 4, None, None, "UserInformationMessageFormat"),
        0x306a: (0x000b, None, None, 4, None, None, "UserInformationMessageTrackingReadStatusDisabled"),
        0x306b: (0x0003, None, None, 4, None, None, "UserInformationMobileFeaturesEnabled"),
        0x306c: (0x001f, None, None, 4, None, None, "UserInformationMobilePhone"),
        0x306d: (0x0003, None, None, 4, None, None, "UserInformationModerationFlags"),
        0x306e: (0x001f, None, None, 4, None, None, "UserInformationNotes"),
        0x306f: (0x001f, None, None, 4, None, None, "UserInformationOccupation"),
        0x3070: (0x000b, None, None, 4, None, None, "UserInformationOpenDomainRoutingDisabled"),
        0x3071: (0x101f, None, None, 4, None, None, "UserInformationOtherHomePhone"),
        0x3072: (0x101f, None, None, 4, None, None, "UserInformationOtherMobile"),
        0x3073: (0x101f, None, None, 4, None, None, "UserInformationOtherTelephone"),
        0x3074: (0x000b, None, None, 4, None, None, "UserInformationOWAEnabled"),
        0x3075: (0x000b, None, None, 4, None, None, "UserInformationOWAforDevicesEnabled"),
        0x3076: (0x001f, None, None, 4, None, None, "UserInformationPager"),
        0x3077: (0x1003, None, None, 4, None, None, "UserInformationPersistedCapabilities"),
        0x3078: (0x001f, None, None, 4, None, None, "UserInformationPhone"),
        0x3079: (0x001f, None, None, 4, None, None, "UserInformationPhoneProviderId"),
        0x307a: (0x000b, None, None, 4, None, None, "UserInformationPopEnabled"),
        0x307b: (0x000b, None, None, 4, None, None, "UserInformationPopEnableExactRFC822Size"),
        0x307c: (0x000b, None, None, 4, None, None, "UserInformationPopForceICalForCalendarRetrievalOption"),
        0x307d: (0x0003, None, None, 4, None, None, "UserInformationPopMessagesRetrievalMimeFormat"),
        0x307e: (0x0003, None, None, 4, None, None, "UserInformationPopProtocolLoggingEnabled"),
        0x307f: (0x000b, None, None, 4, None, None, "UserInformationPopSuppressReadReceipt"),
        0x3080: (0x000b, None, None, 4, None, None, "UserInformationPopUseProtocolDefaults"),
        0x3081: (0x001f, None, None, 4, None, None, "UserInformationPostalCode"),
        0x3082: (0x101f, None, None, 4, None, None, "UserInformationPostOfficeBox"),
        0x3083: (0x0048, None, None, 4, None, None, "UserInformationPreviousExchangeGuid"),
        0x3084: (0x0003, None, None, 4, None, None, "UserInformationPreviousRecipientTypeDetails"),
        0x3085: (0x001f, None, None, 4, None, None, "UserInformationProhibitSendQuota"),
        0x3086: (0x001f, None, None, 4, None, None, "UserInformationProhibitSendReceiveQuota"),
        0x3087: (0x000b, None, None, 4, None, None, "UserInformationQueryBaseDNRestrictionEnabled"),
        0x3088: (0x0003, None, None, 4, None, None, "UserInformationRecipientDisplayType"),
        0x3089: (0x001f, None, None, 4, None, None, "UserInformationRecipientLimits"),
        0x308a: (0x0003, None, None, 4, None, None, "UserInformationRecipientSoftDeletedStatus"),
        0x308b: (0x001f, None, None, 4, None, None, "UserInformationRecoverableItemsQuota"),
        0x308c: (0x001f, None, None, 4, None, None, "UserInformationRecoverableItemsWarningQuota"),
        0x308d: (0x001f, None, None, 4, None, None, "UserInformationRegion"),
        0x308e: (0x000b, None, None, 4, None, None, "UserInformationRemotePowerShellEnabled"),
        0x308f: (0x0003, None, None, 4, None, None, "UserInformationRemoteRecipientType"),
        0x3090: (0x000b, None, None, 4, None, None, "UserInformationRequireAllSendersAreAuthenticated"),
        0x3091: (0x000b, None, None, 4, None, None, "UserInformationResetPasswordOnNextLogon"),
        0x3092: (0x0014, None, None, 4, None, None, "UserInformationRetainDeletedItemsFor"),
        0x3093: (0x000b, None, None, 4, None, None, "UserInformationRetainDeletedItemsUntilBackup"),
        0x3094: (0x001f, None, None, 4, None, None, "UserInformationRulesQuota"),
        0x3095: (0x000b, None, None, 4, None, None, "UserInformationShouldUseDefaultRetentionPolicy"),
        0x3096: (0x001f, None, None, 4, None, None, "UserInformationSimpleDisplayName"),
        0x3097: (0x000b, None, None, 4, None, None, "UserInformationSingleItemRecoveryEnabled"),
        0x3098: (0x001f, None, None, 4, None, None, "UserInformationStateOrProvince"),
        0x3099: (0x001f, None, None, 4, None, None, "UserInformationStreetAddress"),
        0x309a: (0x000b, None, None, 4, None, None, "UserInformationSubscriberAccessEnabled"),
        0x309b: (0x001f, None, None, 4, None, None, "UserInformationTextEncodedORAddress"),
        0x309c: (0x101f, None, None, 4, None, None, "UserInformationTextMessagingState"),
        0x309d: (0x001f, None, None, 4, None, None, "UserInformationTimezone"),
        0x309e: (0x000b, None, None, 4, None, None, "UserInformationUCSImListMigrationCompleted"),
        0x309f: (0x001f, None, None, 4, None, None, "UserInformationUpgradeDetails"),
        0x30a0: (0x001f, None, None, 4, None, None, "UserInformationUpgradeMessage"),
        0x30a1: (0x0003, None, None, 4, None, None, "UserInformationUpgradeRequest"),
        0x30a2: (0x0003, None, None, 4, None, None, "UserInformationUpgradeStage"),
        0x30a3: (0x0040, None, None, 4, None, None, "UserInformationUpgradeStageTimeStamp"),
        0x30a4: (0x0003, None, None, 4, None, None, "UserInformationUpgradeStatus"),
        0x30a5: (0x001f, None, None, 4, None, None, "UserInformationUsageLocation"),
        0x30a6: (0x0003, None, None, 4, None, None, "UserInformationUseMapiRichTextFormat"),
        0x30a7: (0x000b, None, None, 4, None, None, "UserInformationUsePreferMessageFormat"),
        0x30a8: (0x000b, None, None, 4, None, None, "UserInformationUseUCCAuditConfig"),
        0x30a9: (0x001f, None, None, 4, None, None, "UserInformationWebPage"),
        0x30aa: (0x0040, None, None, 4, None, None, "UserInformationWhenMailboxCreated"),
        0x30ab: (0x0040, None, None, 4, None, None, "UserInformationWhenSoftDeleted"),
        0x30ac: (0x001f, None, None, 4, None, None, "UserInformationBirthdayPrecision"),
        0x30ad: (0x001f, None, None, 4, None, None, "UserInformationNameVersion"),
        0x30ae: (0x0040, None, None, 4, None, None, "UserInformationOptInTime"),
        0x30af: (0x000b, None, None, 4, None, None, "UserInformationIsMigratedConsumerMailbox"),
        0x30b0: (0x000b, None, None, 4, None, None, "UserInformationMigrationDryRun"),
        0x30b1: (0x000b, None, None, 4, None, None, "UserInformationIsPremiumConsumerMailbox"),
        0x30b2: (0x001f, None, None, 4, None, None, "UserInformationAlternateSupportEmailAddresses"),
        0x30b3: (0x101f, None, None, 4, None, None, "UserInformationEmailAddresses"),
        0x30b4: (0x000b, None, None, 4, None, None, "UserInformationHasSnackyAppData"),
        0x30b5: (0x001f, None, None, 4, None, None, "UserInformationMailboxMoveTargetMDB"),
        0x30b6: (0x001f, None, None, 4, None, None, "UserInformationMailboxMoveSourceMDB"),
        0x30b7: (0x0003, None, None, 4, None, None, "UserInformationMailboxMoveFlags"),
        0x30b8: (0x0040, None, None, 4, None, None, "UserInformationHydraLastSyncTimestamp"),
        0x30b9: (0x0048, None, None, 4, None, None, "UserInformationHydraSyncStartIdentity"),
        0x30ba: (0x0040, None, None, 4, None, None, "UserInformationHydraSyncStartTimestamp"),
        0x30bb: (0x0002, None, None, 4, None, None, "UserInformationStatus"),
        0x30bc: (0x0040, None, None, 4, None, None, "UserInformationDeletedOn"),
        0x30bd: (0x000b, None, None, 4, None, None, "UserInformationMigrationInterruptionTest"),
        0x30be: (0x0002, None, None, 4, None, None, "UserInformationLocatorSource"),
        0x30bf: (0x000b, None, None, 4, None, None, "UserInformationMAPIEnabled"),
        0x30c0: (0x0003, None, None, 4, None, None, "UserInformationOlcDatFlags"),
        0x30c1: (0x0003, None, None, 4, None, None, "UserInformationOlcDat2Flags"),
        0x30c2: (0x001f, None, None, 4, None, None, "UserInformationDefaultFromAddress"),
        0x30c3: (0x101f, None, None, 4, None, None, "UserInformationNotManagedEmailAddresses"),
        0x30c4: (0x0003, None, None, 4, None, None, "UserInformationLatitude"),
        0x30c5: (0x101f, None, None, 4, None, None, "UserInformationConnectedAccounts"),
        0x30c6: (0x0003, None, None, 4, None, None, "UserInformationAccountTrustLevel"),
        0x30c7: (0x001f, None, None, 4, None, None, "UserInformationBlockReason"),
        0x30c8: (0x000b, None, None, 4, None, None, "UserInformationHijackDetection"),
        0x30c9: (0x000b, None, None, 4, None, None, "UserInformationHipChallengeApplicable"),
        0x30ca: (0x000b, None, None, 4, None, None, "UserInformationIsBlocked"),
        0x30cb: (0x000b, None, None, 4, None, None, "UserInformationIsSwitchUser"),
        0x30cc: (0x000b, None, None, 4, None, None, "UserInformationIsToolsAccount"),
        0x30cd: (0x0040, None, None, 4, None, None, "UserInformationLastBlockTime"),
        0x30ce: (0x0003, None, None, 4, None, None, "UserInformationMaxDailyMessages"),
        0x30cf: (0x000b, None, None, 4, None, None, "UserInformationReportToExternalSender"),
        0x30d0: (0x0040, None, None, 4, None, None, "UserInformationWhenOlcMailboxCreated"),
        0x30d1: (0x001f, None, None, 4, None, None, "UserInformationMailboxProvisioningConstraint"),
        0x30d2: (0x101f, None, None, 4, None, None, "UserInformationMailboxProvisioningPreferences"),
        0x30d3: (0x0014, None, None, 4, None, None, "UserInformationCID"),
        0x30d4: (0x101f, None, None, 4, None, None, "UserInformationSharingAnonymousIdentities"),
        0x30d5: (0x0102, None, None, 4, None, None, "UserInformationExchangeSecurityDescriptor"),
        0x30d6: (0x000b, None, None, 4, None, None, "UserInformationMapiHttpEnabled"),
        0x30d7: (0x000b, None, None, 4, None, None, "UserInformationMAPIBlockOutlookExternalConnectivity"),
        0x30d8: (0x000b, None, None, 4, None, None, "UserInformationUniversalOutlookEnabled"),
        0x30d9: (0x000b, None, None, 4, None, None, "UserInformationPopMessageDeleteEnabled"),
        0x30da: (0x0003, None, None, 4, None, None, "UserInformationPrimaryMailboxSource"),
        0x30db: (0x0002, None, None, 4, None, None, "UserInformationLocatorCacheHint"),
        0x30dc: (0x0014, None, None, 4, None, None, "UserInformationNetID"),
        0x30dd: (0x000b, None, None, 4, None, None, "UserInformationIsProsumerConsumerMailbox"),
        0x30de: (0x101f, None, None, 4, None, None, "UserInformationProsumerEmailAddresses"),
        0x30df: (0x101f, None, None, 4, None, None, "UserInformationProsumerMSAVerifiedEmailAddresses"),
        0x30e0: (0x0048, None, None, 4, None, None, "UserInformationShardOwnerExchangeObjectId"),
        0x30e1: (0x0102, None, None, 4, None, None, "UserInformationShardOwnerTenantPartitionHint"),
        0x30e2: (0x001f, None, None, 4, None, None, "UserInformationShardProvisionedByProtocolType"),
        0x30e3: (0x000b, None, None, 4, None, None, "UserInformationIsShadowMailboxProvisioningComplete"),
        0x30e4: (0x001f, None, None, 4, None, None, "UserInformationShadowRemoteEmailAddress"),
        0x30e5: (0x0003, None, None, 4, None, None, "UserInformationShadowScope"),
        0x30e6: (0x001f, None, None, 4, None, None, "UserInformationShadowUserName"),
        0x30e7: (0x0003, None, None, 4, None, None, "UserInformationShadowProvider"),
        0x30e8: (0x000b, None, None, 4, None, None, "UserInformationIsShadowMailbox"),
        0x30e9: (0x0003, None, None, 4, None, None, "UserInformationPersistedMservNameVersion"),
        0x30ea: (0x0040, None, None, 4, None, None, "UserInformationLastPersistedMservNameVersionUpdateTime"),
        0x30eb: (0x0014, None, None, 4, None, None, "UserInformationPremiumAccountOffers"),
        0x30ec: (0x101f, None, None, 4, None, None, "UserInformationLegacyCustomDomainAddresses"),
        0x30ed: (0x000b, None, None, 4, None, None, "UserInformationActiveSyncSuppressReadReceipt"),
        0x30ee: (0x101f, None, None, 4, None, None, "UserInformationAcceptMessagesOnlyFrom"),
        0x30ef: (0x101f, None, None, 4, None, None, "UserInformationAcceptMessagesOnlyFromBL"),
        0x30f0: (0x101f, None, None, 4, None, None, "UserInformationAcceptMessagesOnlyFromDLMembers"),
        0x30f1: (0x101f, None, None, 4, None, None, "UserInformationAcceptMessagesOnlyFromDLMembersBL"),
        0x30f2: (0x001f, None, None, 4, None, None, "UserInformationActiveSyncMailboxPolicy"),
        0x30f3: (0x000b, None, None, 4, None, None, "UserInformationActiveSyncMailboxPolicyIsDefaulted"),
        0x30f4: (0x0003, None, None, 4, None, None, "UserInformationAddressBookFlags"),
        0x30f5: (0x001f, None, None, 4, None, None, "UserInformationAddressBookPolicy"),
        0x30f6: (0x101f, None, None, 4, None, None, "UserInformationAddressListMembership"),
        0x30f7: (0x101f, None, None, 4, None, None, "UserInformationAdministrativeUnits"),
        0x30f8: (0x101f, None, None, 4, None, None, "UserInformationAggregatedMailboxGuidsRaw"),
        0x30f9: (0x001f, None, None, 4, None, None, "UserInformationAlias"),
        0x30fa: (0x000b, None, None, 4, None, None, "UserInformationAllowAddGuests"),
        0x30fb: (0x101f, None, None, 4, None, None, "UserInformationAllowedAttributesEffective"),
        0x30fc: (0x0003, None, None, 4, None, None, "UserInformationAllowUMCallsFromNonUsers"),
        0x30fd: (0x101f, None, None, 4, None, None, "UserInformationAltSecurityIdentities"),
        0x30fe: (0x101f, None, None, 4, None, None, "UserInformationApprovalApplications"),
        0x30ff: (0x001f, None, None, 4, None, None, "UserInformationArbitrationMailbox"),
        0x3100: (0x001f, None, None, 4, None, None, "UserInformationArchiveDatabaseRaw"),
        0x3101: (0x1102, None, None, 4, None, None, "UserInformationAttributeMetadata"),
        0x3102: (0x0003, None, None, 4, None, None, "UserInformationAuditAdminFlags"),
        0x3103: (0x000b, None, None, 4, None, None, "UserInformationAuditBypassEnabled"),
        0x3104: (0x0003, None, None, 4, None, None, "UserInformationAuditDelegateAdminFlags"),
        0x3105: (0x0003, None, None, 4, None, None, "UserInformationAuditDelegateFlags"),
        0x3106: (0x000b, None, None, 4, None, None, "UserInformationAuditEnabled"),
        0x3107: (0x0040, None, None, 4, None, None, "UserInformationAuditLastAdminAccess"),
        0x3108: (0x0040, None, None, 4, None, None, "UserInformationAuditLastDelegateAccess"),
        0x3109: (0x0040, None, None, 4, None, None, "UserInformationAuditLastExternalAccess"),
        0x310a: (0x0014, None, None, 4, None, None, "UserInformationAuditLogAgeLimit"),
        0x310b: (0x0003, None, None, 4, None, None, "UserInformationAuditOwnerFlags"),
        0x310c: (0x0003, None, None, 4, None, None, "UserInformationAuditStorageState"),
        0x310d: (0x001f, None, None, 4, None, None, "UserInformationAuxMailboxParentObjectId"),
        0x310e: (0x001f, None, None, 4, None, None, "UserInformationAuxMailboxParentObjectIdBL"),
        0x310f: (0x001f, None, None, 4, None, None, "UserInformationAuthenticationPolicy"),
        0x3110: (0x0102, None, None, 4, None, None, "UserInformationBlockedSendersHash"),
        0x3111: (0x101f, None, None, 4, None, None, "UserInformationBypassModerationFrom"),
        0x3112: (0x101f, None, None, 4, None, None, "UserInformationBypassModerationFromBL"),
        0x3113: (0x101f, None, None, 4, None, None, "UserInformationBypassModerationFromDLMembers"),
        0x3114: (0x101f, None, None, 4, None, None, "UserInformationBypassModerationFromDLMembersBL"),
        0x3115: (0x0003, None, None, 4, None, None, "UserInformationCallAnsweringAudioCodecLegacy"),
        0x3116: (0x0003, None, None, 4, None, None, "UserInformationCallAnsweringAudioCodec2"),
        0x3117: (0x101f, None, None, 4, None, None, "UserInformationCatchAllRecipientBL"),
        0x3118: (0x1102, None, None, 4, None, None, "UserInformationCertificate"),
        0x3119: (0x001f, None, None, 4, None, None, "UserInformationClassification"),
        0x311a: (0x001f, None, None, 4, None, None, "UserInformationCo"),
        0x311b: (0x101f, None, None, 4, None, None, "UserInformationCoManagedBy"),
        0x311c: (0x101f, None, None, 4, None, None, "UserInformationCoManagedObjectsBL"),
        0x311d: (0x001f, None, None, 4, None, None, "UserInformationCompany"),
        0x311e: (0x001f, None, None, 4, None, None, "UserInformationConfigurationUnit"),
        0x311f: (0x001f, None, None, 4, None, None, "UserInformationConfigurationXMLRaw"),
        0x3120: (0x0048, None, None, 4, None, None, "UserInformationCorrelationIdRaw"),
        0x3121: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute1"),
        0x3122: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute10"),
        0x3123: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute11"),
        0x3124: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute12"),
        0x3125: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute13"),
        0x3126: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute14"),
        0x3127: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute15"),
        0x3128: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute2"),
        0x3129: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute3"),
        0x312a: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute4"),
        0x312b: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute5"),
        0x312c: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute6"),
        0x312d: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute7"),
        0x312e: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute8"),
        0x312f: (0x001f, None, None, 4, None, None, "UserInformationCustomAttribute9"),
        0x3130: (0x001f, None, None, 4, None, None, "UserInformationDatabase"),
        0x3131: (0x001f, None, None, 4, None, None, "UserInformationDataEncryptionPolicy"),
        0x3132: (0x001f, None, None, 4, None, None, "UserInformationDefaultPublicFolderMailbox"),
        0x3133: (0x001f, None, None, 4, None, None, "UserInformationDefaultPublicFolderMailboxSmtpAddress"),
        0x3134: (0x101f, None, None, 4, None, None, "UserInformationDelegateListBL"),
        0x3135: (0x101f, None, None, 4, None, None, "UserInformationDelegateListLink"),
        0x3136: (0x0003, None, None, 4, None, None, "UserInformationDeletedItemFlags"),
        0x3137: (0x0003, None, None, 4, None, None, "UserInformationDeliveryMechanism"),
        0x3138: (0x001f, None, None, 4, None, None, "UserInformationDepartment"),
        0x3139: (0x101f, None, None, 4, None, None, "UserInformationDirectReports"),
        0x313a: (0x101f, None, None, 4, None, None, "UserInformationDirSyncAuthorityMetadata"),
        0x313b: (0x001f, None, None, 4, None, None, "UserInformationDirSyncId"),
        0x313c: (0x001f, None, None, 4, None, None, "UserInformationDisabledArchiveDatabase"),
        0x313d: (0x1102, None, None, 4, None, None, "UserInformationDLSupervisionList"),
        0x313e: (0x0040, None, None, 4, None, None, "UserInformationElcExpirationSuspensionEndDate"),
        0x313f: (0x0040, None, None, 4, None, None, "UserInformationElcExpirationSuspensionStartDate"),
        0x3140: (0x0003, None, None, 4, None, None, "UserInformationElcMailboxFlags"),
        0x3141: (0x001f, None, None, 4, None, None, "UserInformationElcPolicyTemplate"),
        0x3142: (0x001f, None, None, 4, None, None, "UserInformationEntryId"),
        0x3143: (0x0048, None, None, 4, None, None, "UserInformationExchangeObjectIdRaw"),
        0x3144: (0x0102, None, None, 4, None, None, "UserInformationExchangeSecurityDescriptorRaw"),
        0x3145: (0x001f, None, None, 4, None, None, "UserInformationExchangeVersion"),
        0x3146: (0x0003, None, None, 4, None, None, "UserInformationExchangeUserAccountControl"),
        0x3147: (0x001f, None, None, 4, None, None, "UserInformationExpansionServer"),
        0x3148: (0x101f, None, None, 4, None, None, "UserInformationExtensionCustomAttribute1"),
        0x3149: (0x101f, None, None, 4, None, None, "UserInformationExtensionCustomAttribute2"),
        0x314a: (0x101f, None, None, 4, None, None, "UserInformationExtensionCustomAttribute3"),
        0x314b: (0x101f, None, None, 4, None, None, "UserInformationExtensionCustomAttribute4"),
        0x314c: (0x101f, None, None, 4, None, None, "UserInformationExtensionCustomAttribute5"),
        0x314d: (0x001f, None, None, 4, None, None, "UserInformationExternalDirectoryObjectId"),
        0x314e: (0x0102, None, None, 4, None, None, "UserInformationExternalSyncState"),
        0x314f: (0x001f, None, None, 4, None, None, "UserInformationFax"),
        0x3150: (0x000b, None, None, 4, None, None, "UserInformationFblEnabled"),
        0x3151: (0x001f, None, None, 4, None, None, "UserInformationForwardingAddress"),
        0x3152: (0x101f, None, None, 4, None, None, "UserInformationForwardingAddressBL"),
        0x3153: (0x001f, None, None, 4, None, None, "UserInformationForeignGroupSid"),
        0x3154: (0x101f, None, None, 4, None, None, "UserInformationGeneratedOfflineAddressBooks"),
        0x3155: (0x001f, None, None, 4, None, None, "UserInformationGroupPersonification"),
        0x3156: (0x101f, None, None, 4, None, None, "UserInformationGrantSendOnBehalfTo"),
        0x3157: (0x101f, None, None, 4, None, None, "UserInformationGrantSendOnBehalfToBL"),
        0x3158: (0x001f, None, None, 4, None, None, "UserInformationGroupSubtypeName"),
        0x3159: (0x0003, None, None, 4, None, None, "UserInformationGroupType"),
        0x315a: (0x0003, None, None, 4, None, None, "UserInformationGroupExternalMemberCount"),
        0x315b: (0x0003, None, None, 4, None, None, "UserInformationGroupMemberCount"),
        0x315c: (0x001f, None, None, 4, None, None, "UserInformationGuestHint"),
        0x315d: (0x101f, None, None, 4, None, None, "UserInformationHABShowInDepartments"),
        0x315e: (0x0003, None, None, 4, None, None, "UserInformationHeuristics"),
        0x315f: (0x000b, None, None, 4, None, None, "UserInformationHiddenGroupMembershipEnabled"),
        0x3160: (0x001f, None, None, 4, None, None, "UserInformationHomeMTA"),
        0x3161: (0x001f, None, None, 4, None, None, "UserInformationId"),
        0x3162: (0x001f, None, None, 4, None, None, "UserInformationImmutableId"),
        0x3163: (0x101f, None, None, 4, None, None, "UserInformationInPlaceHoldsRaw"),
        0x3164: (0x001f, None, None, 4, None, None, "UserInformationIntendedMailboxPlan"),
        0x3165: (0x1102, None, None, 4, None, None, "UserInformationInternalRecipientSupervisionList"),
        0x3166: (0x000b, None, None, 4, None, None, "UserInformationIsDirSynced"),
        0x3167: (0x000b, None, None, 4, None, None, "UserInformationIsInactive"),
        0x3168: (0x000b, None, None, 4, None, None, "UserInformationIsOrganizationalGroup"),
        0x3169: (0x001f, None, None, 4, None, None, "UserInformationLdapRecipientFilter"),
        0x316a: (0x001f, None, None, 4, None, None, "UserInformationLanguagesRaw"),
        0x316b: (0x001f, None, None, 4, None, None, "UserInformationLegacyExchangeDN"),
        0x316c: (0x001f, None, None, 4, None, None, "UserInformationLinkedPartnerGroupAndOrganizationId"),
        0x316d: (0x1102, None, None, 4, None, None, "UserInformationLinkMetadata"),
        0x316e: (0x0040, None, None, 4, None, None, "UserInformationLitigationHoldDate"),
        0x316f: (0x001f, None, None, 4, None, None, "UserInformationLitigationHoldOwner"),
        0x3170: (0x0003, None, None, 4, None, None, "UserInformationLocalizationFlags"),
        0x3171: (0x1102, None, None, 4, None, None, "UserInformationMailboxDatabasesRaw"),
        0x3172: (0x1048, None, None, 4, None, None, "UserInformationMailboxGuidsRaw"),
        0x3173: (0x1102, None, None, 4, None, None, "UserInformationMailboxLocationsRaw"),
        0x3174: (0x001f, None, None, 4, None, None, "UserInformationMailboxPlan"),
        0x3175: (0x001f, None, None, 4, None, None, "UserInformationMailboxPlanIndex"),
        0x3176: (0x001f, None, None, 4, None, None, "UserInformationMailboxRegion"),
        0x3177: (0x001f, None, None, 4, None, None, "UserInformationMailboxMoveSourceArchiveMDB"),
        0x3178: (0x001f, None, None, 4, None, None, "UserInformationMailboxMoveTargetArchiveMDB"),
        0x3179: (0x000b, None, None, 4, None, None, "UserInformationMbxGuidEnabled"),
        0x317a: (0x001f, None, None, 4, None, None, "UserInformationManager"),
        0x317b: (0x001f, None, None, 4, None, None, "UserInformationMasterAccountSid"),
        0x317c: (0x001f, None, None, 4, None, None, "UserInformationMasterDirectoryObjectIdRaw"),
        0x317d: (0x0003, None, None, 4, None, None, "UserInformationMemberDepartRestriction"),
        0x317e: (0x0003, None, None, 4, None, None, "UserInformationMemberJoinRestriction"),
        0x317f: (0x101f, None, None, 4, None, None, "UserInformationMemberOfGroup"),
        0x3180: (0x101f, None, None, 4, None, None, "UserInformationMembers"),
        0x3181: (0x0003, None, None, 4, None, None, "UserInformationMessageHygieneFlags"),
        0x3182: (0x000b, None, None, 4, None, None, "UserInformationMigrationToUnifiedGroupInProgress"),
        0x3183: (0x101f, None, None, 4, None, None, "UserInformationMobileAdminExtendedSettings"),
        0x3184: (0x0003, None, None, 4, None, None, "UserInformationMobileMailboxFlags"),
        0x3185: (0x101f, None, None, 4, None, None, "UserInformationModeratedBy"),
        0x3186: (0x000b, None, None, 4, None, None, "UserInformationModerationEnabled"),
        0x3187: (0x101f, None, None, 4, None, None, "UserInformationModeratedObjectsBL"),
        0x3188: (0x0003, None, None, 4, None, None, "UserInformationMservNameVersion"),
        0x3189: (0x0014, None, None, 4, None, None, "UserInformationMservNetID"),
        0x318a: (0x0102, None, None, 4, None, None, "UserInformationNTSecurityDescriptor"),
        0x318b: (0x001f, None, None, 4, None, None, "UserInformationObjectCategory"),
        0x318c: (0x101f, None, None, 4, None, None, "UserInformationObjectClass"),
        0x318d: (0x001f, None, None, 4, None, None, "UserInformationOffice"),
        0x318e: (0x001f, None, None, 4, None, None, "UserInformationOfflineAddressBook"),
        0x318f: (0x1102, None, None, 4, None, None, "UserInformationOneOffSupervisionList"),
        0x3190: (0x001f, None, None, 4, None, None, "UserInformationOrganizationalUnitRoot"),
        0x3191: (0x101f, None, None, 4, None, None, "UserInformationOrgLeaders"),
        0x3192: (0x001f, None, None, 4, None, None, "UserInformationOriginatingServer"),
        0x3193: (0x101f, None, None, 4, None, None, "UserInformationOtherDisplayNames"),
        0x3194: (0x101f, None, None, 4, None, None, "UserInformationOtherFax"),
        0x3195: (0x001f, None, None, 4, None, None, "UserInformationOwaMailboxPolicy"),
        0x3196: (0x101f, None, None, 4, None, None, "UserInformationOwners"),
        0x3197: (0x001f, None, None, 4, None, None, "UserInformationPreviousDatabase"),
        0x3198: (0x101f, None, None, 4, None, None, "UserInformationPublicFolderContacts"),
        0x3199: (0x000b, None, None, 4, None, None, "UserInformationPuidEmailAddressEnabled"),
        0x319a: (0x101f, None, None, 4, None, None, "UserInformationPurportedSearchUI"),
        0x319b: (0x0014, None, None, 4, None, None, "UserInformationPasswordLastSetRaw"),
        0x319c: (0x001f, None, None, 4, None, None, "UserInformationPhoneticCompany"),
        0x319d: (0x001f, None, None, 4, None, None, "UserInformationPhoneticDisplayName"),
        0x319e: (0x001f, None, None, 4, None, None, "UserInformationPhoneticDepartment"),
        0x319f: (0x001f, None, None, 4, None, None, "UserInformationPhoneticFirstName"),
        0x31a0: (0x001f, None, None, 4, None, None, "UserInformationPhoneticLastName"),
        0x31a1: (0x101f, None, None, 4, None, None, "UserInformationPoliciesExcluded"),
        0x31a2: (0x101f, None, None, 4, None, None, "UserInformationPoliciesIncluded"),
        0x31a3: (0x0003, None, None, 4, None, None, "UserInformationPrimaryGroupId"),
        0x31a4: (0x101f, None, None, 4, None, None, "UserInformationProtocolSettings"),
        0x31a5: (0x0003, None, None, 4, None, None, "UserInformationProvisioningFlags"),
        0x31a6: (0x001f, None, None, 4, None, None, "UserInformationQueryBaseDN"),
        0x31a7: (0x101f, None, None, 4, None, None, "UserInformationRawCanonicalName"),
        0x31a8: (0x1003, None, None, 4, None, None, "UserInformationRawCapabilities"),
        0x31a9: (0x001f, None, None, 4, None, None, "UserInformationRawExternalEmailAddress"),
        0x31aa: (0x001f, None, None, 4, None, None, "UserInformationRawManagedBy"),
        0x31ab: (0x001f, None, None, 4, None, None, "UserInformationRawName"),
        0x31ac: (0x001f, None, None, 4, None, None, "UserInformationRawDisplayName"),
        0x31ad: (0x0102, None, None, 4, None, None, "UserInformationRawOnPremisesObjectId"),
        0x31ae: (0x001f, None, None, 4, None, None, "UserInformationRecipientContainer"),
        0x31af: (0x001f, None, None, 4, None, None, "UserInformationRecipientFilter"),
        0x31b0: (0x101f, None, None, 4, None, None, "UserInformationRecipientFilterMetadata"),
        0x31b1: (0x0003, None, None, 4, None, None, "UserInformationRecipientTypeDetailsValue"),
        0x31b2: (0x0102, None, None, 4, None, None, "UserInformationReplicationSignature"),
        0x31b3: (0x101f, None, None, 4, None, None, "UserInformationRejectMessagesFrom"),
        0x31b4: (0x101f, None, None, 4, None, None, "UserInformationRejectMessagesFromBL"),
        0x31b5: (0x101f, None, None, 4, None, None, "UserInformationRejectMessagesFromDLMembers"),
        0x31b6: (0x101f, None, None, 4, None, None, "UserInformationRejectMessagesFromDLMembersBL"),
        0x31b7: (0x0003, None, None, 4, None, None, "UserInformationReleaseTrack"),
        0x31b8: (0x001f, None, None, 4, None, None, "UserInformationRemoteAccountPolicy"),
        0x31b9: (0x000b, None, None, 4, None, None, "UserInformationReportToManagerEnabled"),
        0x31ba: (0x000b, None, None, 4, None, None, "UserInformationReportToOriginatorEnabled"),
        0x31bb: (0x0003, None, None, 4, None, None, "UserInformationResourceCapacity"),
        0x31bc: (0x101f, None, None, 4, None, None, "UserInformationResourceMetaData"),
        0x31bd: (0x001f, None, None, 4, None, None, "UserInformationResourcePropertiesDisplay"),
        0x31be: (0x101f, None, None, 4, None, None, "UserInformationResourceSearchProperties"),
        0x31bf: (0x001f, None, None, 4, None, None, "UserInformationRetentionComment"),
        0x31c0: (0x001f, None, None, 4, None, None, "UserInformationRetentionUrl"),
        0x31c1: (0x101f, None, None, 4, None, None, "UserInformationRMSComputerAccounts"),
        0x31c2: (0x001f, None, None, 4, None, None, "UserInformationRoleAssignmentPolicy"),
        0x31c3: (0x0003, None, None, 4, None, None, "UserInformationRoleGroupTypeId"),
        0x31c4: (0x001f, None, None, 4, None, None, "UserInformationRTCSIPPrimaryUserAddress"),
        0x31c5: (0x001f, None, None, 4, None, None, "UserInformationRtcSipLine"),
        0x31c6: (0x0102, None, None, 4, None, None, "UserInformationSafeRecipientsHash"),
        0x31c7: (0x0102, None, None, 4, None, None, "UserInformationSafeSendersHash"),
        0x31c8: (0x001f, None, None, 4, None, None, "UserInformationSamAccountName"),
        0x31c9: (0x001f, None, None, 4, None, None, "UserInformationSatchmoClusterIp"),
        0x31ca: (0x0003, None, None, 4, None, None, "UserInformationSatchmoDGroup"),
        0x31cb: (0x0003, None, None, 4, None, None, "UserInformationSCLDeleteThresholdInt"),
        0x31cc: (0x0003, None, None, 4, None, None, "UserInformationSCLJunkThresholdInt"),
        0x31cd: (0x0003, None, None, 4, None, None, "UserInformationSCLQuarantineThresholdInt"),
        0x31ce: (0x0003, None, None, 4, None, None, "UserInformationSCLRejectThresholdInt"),
        0x31cf: (0x1102, None, None, 4, None, None, "UserInformationSecurityProtocol"),
        0x31d0: (0x000b, None, None, 4, None, None, "UserInformationSendOofMessageToOriginatorEnabled"),
        0x31d1: (0x001f, None, None, 4, None, None, "UserInformationServerLegacyDN"),
        0x31d2: (0x001f, None, None, 4, None, None, "UserInformationSharePointLinkedBy"),
        0x31d3: (0x101f, None, None, 4, None, None, "UserInformationSharePointResources"),
        0x31d4: (0x001f, None, None, 4, None, None, "UserInformationSharePointSiteInfo"),
        0x31d5: (0x001f, None, None, 4, None, None, "UserInformationSharePointUrl"),
        0x31d6: (0x101f, None, None, 4, None, None, "UserInformationSharingPartnerIdentitiesRaw"),
        0x31d7: (0x001f, None, None, 4, None, None, "UserInformationSharingPolicy"),
        0x31d8: (0x001f, None, None, 4, None, None, "UserInformationSid"),
        0x31d9: (0x101f, None, None, 4, None, None, "UserInformationSidHistory"),
        0x31da: (0x001f, None, None, 4, None, None, "UserInformationSiloName"),
        0x31db: (0x001f, None, None, 4, None, None, "UserInformationSkypeId"),
        0x31dc: (0x1102, None, None, 4, None, None, "UserInformationSMimeCertificate"),
        0x31dd: (0x001f, None, None, 4, None, None, "UserInformationSourceAnchor"),
        0x31de: (0x0040, None, None, 4, None, None, "UserInformationStsRefreshTokensValidFrom"),
        0x31df: (0x0014, None, None, 4, None, None, "UserInformationSystemMailboxRetainDeletedItemsFor"),
        0x31e0: (0x001f, None, None, 4, None, None, "UserInformationSystemMailboxRulesQuota"),
        0x31e1: (0x0040, None, None, 4, None, None, "UserInformationTeamMailboxExpiration"),
        0x31e2: (0x101f, None, None, 4, None, None, "UserInformationTeamMailboxShowInClientList"),
        0x31e3: (0x001f, None, None, 4, None, None, "UserInformationTelephoneAssistant"),
        0x31e4: (0x001f, None, None, 4, None, None, "UserInformationThrottlingPolicy"),
        0x31e5: (0x0102, None, None, 4, None, None, "UserInformationThumbnailPhoto"),
        0x31e6: (0x001f, None, None, 4, None, None, "UserInformationTitle"),
        0x31e7: (0x101f, None, None, 4, None, None, "UserInformationTokenGroupsGlobalAndUniversal"),
        0x31e8: (0x0003, None, None, 4, None, None, "UserInformationTransportSettingFlags"),
        0x31e9: (0x101f, None, None, 4, None, None, "UserInformationUMAddresses"),
        0x31ea: (0x101f, None, None, 4, None, None, "UserInformationUMCallingLineIds"),
        0x31eb: (0x101f, None, None, 4, None, None, "UserInformationUMDtmfMap"),
        0x31ec: (0x0003, None, None, 4, None, None, "UserInformationUMEnabledFlags"),
        0x31ed: (0x0003, None, None, 4, None, None, "UserInformationUMEnabledFlags2"),
        0x31ee: (0x001f, None, None, 4, None, None, "UserInformationUMMailboxPolicy"),
        0x31ef: (0x0102, None, None, 4, None, None, "UserInformationUMPinChecksum"),
        0x31f0: (0x001f, None, None, 4, None, None, "UserInformationUMRecipientDialPlanId"),
        0x31f1: (0x0003, None, None, 4, None, None, "UserInformationUMServerWritableFlags"),
        0x31f2: (0x0102, None, None, 4, None, None, "UserInformationUMSpokenName"),
        0x31f3: (0x101f, None, None, 4, None, None, "UserInformationUnifiedGroupEventSubscriptionBL"),
        0x31f4: (0x101f, None, None, 4, None, None, "UserInformationUnifiedGroupEventSubscriptionLink"),
        0x31f5: (0x001f, None, None, 4, None, None, "UserInformationUnifiedGroupFileNotificationsSettings"),
        0x31f6: (0x101f, None, None, 4, None, None, "UserInformationUnifiedGroupMembersBL"),
        0x31f7: (0x101f, None, None, 4, None, None, "UserInformationUnifiedGroupMembersLink"),
        0x31f8: (0x001f, None, None, 4, None, None, "UserInformationUnifiedGroupProvisioningOption"),
        0x31f9: (0x0003, None, None, 4, None, None, "UserInformationUnifiedGroupSecurityFlags"),
        0x31fa: (0x0003, None, None, 4, None, None, "UserInformationUnifiedGroupSKU"),
        0x31fb: (0x0102, None, None, 4, None, None, "UserInformationUnifiedMailboxAccount"),
        0x31fc: (0x0003, None, None, 4, None, None, "UserInformationUserAccountControl"),
        0x31fd: (0x001f, None, None, 4, None, None, "UserInformationUserPrincipalNameRaw"),
        0x31fe: (0x000b, None, None, 4, None, None, "UserInformationUseDatabaseQuotaDefaults"),
        0x31ff: (0x0014, None, None, 4, None, None, "UserInformationUsnChanged"),
        0x3200: (0x0014, None, None, 4, None, None, "UserInformationUsnCreated"),
        0x3201: (0x0002, None, None, 4, None, None, "UserInformationUserState"),
        0x3202: (0x101f, None, None, 4, None, None, "UserInformationVoiceMailSettings"),
        0x3203: (0x001f, None, None, 4, None, None, "UserInformationWhenChangedRaw"),
        0x3204: (0x001f, None, None, 4, None, None, "UserInformationWhenCreatedRaw"),
        0x3205: (0x001f, None, None, 4, None, None, "UserInformationWindowsEmailAddress"),
        0x3206: (0x001f, None, None, 4, None, None, "UserInformationWindowsLiveID"),
        0x3207: (0x001f, None, None, 4, None, None, "UserInformationYammerGroupAddress"),
        0x3208: (0x001f, None, None, 4, None, None, "UserInformationOperatorNumber"),
        0x3209: (0x0040, None, None, 4, None, None, "UserInformationWhenReadUTC"),
        0x320a: (0x0003, None, None, 4, None, None, "UserInformationPreviousRecipientTypeDetailsHigh"),
        0x320b: (0x0003, None, None, 4, None, None, "UserInformationRemoteRecipientTypeHigh"),
        0x320c: (0x0003, None, None, 4, None, None, "UserInformationRecipientTypeDetailsValueHigh"),
        0x320d: (0x0040, None, None, 4, None, None, "UserInformationFamilyMembersUpdateInProgressStartTime"),
        0x320e: (0x000b, None, None, 4, None, None, "UserInformationIsFamilyMailbox"),
        0x320f: (0x0040, None, None, 4, None, None, "UserInformationMailboxRegionLastUpdateTime"),
        0x3210: (0x0003, None, None, 4, None, None, "UserInformationSubscribeExistingGroupMembersStatus"),
        0x3211: (0x101f, None, None, 4, None, None, "UserInformationGroupMembers"),
        0x3212: (0x0003, None, None, 4, None, None, "UserInformationRecipientDisplayTypeRaw"),
        0x3213: (0x001f, None, None, 4, None, None, "UserInformationUITEntryVersion"),
        0x3214: (0x001f, None, None, 4, None, None, "UserInformationLastRefreshedFrom"),
        0x3215: (0x000b, None, None, 4, None, None, "UserInformationIsGroupMailBox"),
        0x3216: (0x0003, None, None, 4, None, None, "UserInformationMailboxFolderSet"),
        0x3217: (0x000b, None, None, 4, None, None, "UserInformationWasInactiveMailbox"),
        0x3218: (0x0040, None, None, 4, None, None, "UserInformationInactiveMailboxRetireTime"),
        0x3219: (0x0040, None, None, 4, None, None, "UserInformationOrphanSoftDeleteTrackingTime"),
        0x321a: (0x0014, None, None, 4, None, None, "UserInformationSubscriptions"),
        0x321b: (0x001f, None, None, 4, None, None, "UserInformationOtherMail"),
        0x321c: (0x000b, None, None, 4, None, None, "UserInformationIsCIDAddedToMserv"),
        0x321d: (0x0003, None, None, 4, None, None, "UserInformationMailboxWorkloads"),
        0x321e: (0x0040, None, None, 4, None, None, "UserInformationCacheLastAccessTime"),
        0x3233: (0x000b, None, None, 4, None, None, "UserInformationPublicFolderClientAccess"),
        0x330b: (0x0003, None, None, 4, None, None, "BigFunnelLargePOITableTotalPages"),
        0x330c: (0x0003, None, None, 4, None, None, "BigFunnelLargePOITableAvailablePages"),
        0x330d: (0x0014, None, None, 4, None, None, "BigFunnelPOISize"),
        0x330e: (0x0003, None, None, 4, None, None, "BigFunnelMessageCount"),
        0x330f: (0x000b, None, None, 4, None, None, "FastIsEnabled"),
        0x3310: (0x0003, None, None, 4, None, None, "NeedsToMove"),
        0x3311: (0x0003, None, None, 4, None, None, "MCDBMessageTableTotalPages"),
        0x3312: (0x0003, None, None, 4, None, None, "MCDBMessageTableAvailablePages"),
        0x3313: (0x0003, None, None, 4, None, None, "MCDBOtherTablesTotalPages"),
        0x3314: (0x0003, None, None, 4, None, None, "MCDBOtherTablesAvailablePages"),
        0x3315: (0x0003, None, None, 4, None, None, "MCDBBigFunnelFilterTableTotalPages"),
        0x3316: (0x0003, None, None, 4, None, None, "MCDBBigFunnelFilterTableAvailablePages"),
        0x3317: (0x0003, None, None, 4, None, None, "MCDBBigFunnelLargePOITableTotalPages"),
        0x3318: (0x0003, None, None, 4, None, None, "MCDBBigFunnelLargePOITableAvailablePages"),
        0x3319: (0x0014, None, None, 4, None, None, "MCDBSize"),
        0x3320: (0x0014, None, None, 4, None, None, "MCDBAvailableSpace"),
        0x3321: (0x0003, None, None, 4, None, None, "MCDBBigFunnelPostingListTableTotalPages"),
        0x3322: (0x0003, None, None, 4, None, None, "MCDBBigFunnelPostingListTableAvailablePages"),
        0x3323: (0x0005, None, None, 4, None, None, "MCDBMessageTablePercentReplicated"),
        0x3324: (0x0005, None, None, 4, None, None, "MCDBBigFunnelFilterTablePercentReplicated"),
        0x3325: (0x0005, None, None, 4, None, None, "MCDBBigFunnelLargePOITablePercentReplicated"),
        0x3326: (0x0005, None, None, 4, None, None, "MCDBBigFunnelPostingListTablePercentReplicated"),
        0x3327: (0x0003, None, None, 4, None, None, "BigFunnelMailboxCreationVersion"),
        0x3328: (0x0003, None, None, 4, None, None, "BigFunnelAttributeVectorCommonVersion"),
        0x3329: (0x0003, None, None, 4, None, None, "BigFunnelAttributeVectorSharePointVersion"),
        0x3330: (0x0014, None, None, 4, None, None, "BigFunnelIndexedSize"),
        0x3331: (0x0014, None, None, 4, None, None, "BigFunnelPartiallyIndexedSize"),
        0x3332: (0x0014, None, None, 4, None, None, "BigFunnelNotIndexedSize"),
        0x3333: (0x0014, None, None, 4, None, None, "BigFunnelCorruptedSize"),
        0x3334: (0x0014, None, None, 4, None, None, "BigFunnelStaleSize"),
        0x3335: (0x0014, None, None, 4, None, None, "BigFunnelShouldNotBeIndexedSize"),
        0x3336: (0x0003, None, None, 4, None, None, "BigFunnelIndexedCount"),
        0x3337: (0x0003, None, None, 4, None, None, "BigFunnelPartiallyIndexedCount"),
        0x3338: (0x0003, None, None, 4, None, None, "BigFunnelNotIndexedCount"),
        0x3339: (0x0003, None, None, 4, None, None, "BigFunnelCorruptedCount"),
        0x333a: (0x0003, None, None, 4, None, None, "BigFunnelStaleCount"),
        0x333b: (0x0003, None, None, 4, None, None, "BigFunnelShouldNotBeIndexedCount"),
        0x333c: (0x0005, None, None, 4, None, None, "BigFunnelL1Rank"),
        0x333d: (0x0002, None, None, 4, None, None, "BigFunnelResultSets"),
        0x333e: (0x000b, None, None, 4, None, None, "BigFunnelMaintainRefiners"),
        0x333f: (0x0003, None, None, 4, None, None, "BigFunnelPostingListTableBuckets"),
        0x3340: (0x0003, None, None, 4, None, None, "BigFunnelPostingListTargetTableBuckets"),
        0x3341: (0x101f, None, None, 4, None, None, "BigFunnelL1FeatureNames"),
        0x3342: (0x1003, None, None, 4, None, None, "BigFunnelL1FeatureValues"),
        0x3343: (0x0003, None, None, 4, None, None, "MCDBLogonScenarioTotalPages"),
        0x3344: (0x0003, None, None, 4, None, None, "MCDBLogonScenarioAvailablePages"),
        0x3345: (0x0003, None, None, 4, None, None, "BigFunnelMasterIndexVersion"),
        0x33f0: (0x0102, None, None, 4, None, None, "ControlDataForRecordReviewNotificationTBA"),
        0x33fe: (0x0102, None, None, 4, None, None, "ControlDataForBigFunnelQueryParityAssistant"),
        0x33ff: (0x0003, None, None, 4, None, None, "BigFunnelQueryParityAssistantVersion"),
        0x3401: (0x0003, None, None, 4, None, None, "MessageTableTotalPages"),
        0x3402: (0x0003, None, None, 4, None, None, "MessageTableAvailablePages"),
        0x3403: (0x0003, None, None, 4, None, None, "OtherTablesTotalPages"),
        0x3404: (0x0003, None, None, 4, None, None, "OtherTablesAvailablePages"),
        0x3405: (0x0003, None, None, 4, None, None, "AttachmentTableTotalPages"),
        0x3406: (0x0003, None, None, 4, None, None, "AttachmentTableAvailablePages"),
        0x3407: (0x0003, None, None, 4, None, None, "MailboxTypeVersion"),
        0x3408: (0x1048, None, None, 4, None, None, "MailboxPartitionMailboxGuids"),
        0x3409: (0x0003, None, None, 4, None, None, "BigFunnelFilterTableTotalPages"),
        0x340a: (0x0003, None, None, 4, None, None, "BigFunnelFilterTableAvailablePages"),
        0x340b: (0x0003, None, None, 4, None, None, "BigFunnelPostingListTableTotalPages"),
        0x340c: (0x0003, None, None, 4, None, None, "BigFunnelPostingListTableAvailablePages"),
        0x3417: (0x001f, None, None, 4, None, None, "ProviderDisplayIcon"),
        0x3418: (0x001f, None, None, 4, None, None, "ProviderDisplayName"),
        0x3432: (0x0102, None, None, 4, None, None, "ControlDataForDirectoryProcessorAssistant"),
        0x3433: (0x000b, None, None, 4, None, None, "NeedsDirectoryProcessor"),
        0x3434: (0x101f, None, None, 4, None, None, "RetentionQueryIds"),
        0x3435: (0x0014, None, None, 4, None, None, "RetentionQueryInfo"),
        0x3436: (0x0040, None, None, 4, None, None, "MailboxLastProcessedTimestamp"),
        0x3437: (0x0102, None, None, 4, None, None, "ControlDataForPublicFolderAssistant"),
        0x3438: (0x0102, None, None, 4, None, None, "ControlDataForInferenceTrainingAssistant"),
        0x3439: (0x000b, None, None, 4, None, None, "InferenceEnabled"),
        0x343b: (0x0003, None, None, 4, None, None, "ContactLinking"),
        0x343c: (0x0102, None, None, 4, None, None, "ControlDataForOABGeneratorAssistant"),
        0x343d: (0x0003, None, None, 4, None, None, "ContactSaveVersion"),
        0x3440: (0x0102, None, None, 4, None, None, "PushNotificationSubscriptionType"),
        0x3442: (0x0102, None, None, 4, None, None, "ControlDataForInferenceDataCollectionAssistant"),
        0x3443: (0x0102, None, None, 4, None, None, "InferenceDataCollectionProcessingState"),
        0x3444: (0x0102, None, None, 4, None, None, "ControlDataForPeopleRelevanceAssistant"),
        0x3445: (0x0003, None, None, 4, None, None, "SiteMailboxInternalState"),
        0x3446: (0x0102, None, None, 4, None, None, "ControlDataForSiteMailboxAssistant"),
        0x3447: (0x0003, None, None, 4, None, None, "InferenceTrainingLastContentCount"),
        0x3448: (0x0040, None, None, 4, None, None, "InferenceTrainingLastAttemptTimestamp"),
        0x3449: (0x0040, None, None, 4, None, None, "InferenceTrainingLastSuccessTimestamp"),
        0x344a: (0x0003, None, None, 4, None, None, "InferenceUserCapabilityFlags"),
        0x344b: (0x0102, None, None, 4, None, None, "ControlDataForMailboxAssociationReplicationAssistant"),
        0x344c: (0x0040, None, None, 4, None, None, "MailboxAssociationNextReplicationTime"),
        0x344d: (0x0003, None, None, 4, None, None, "MailboxAssociationProcessingFlags"),
        0x344e: (0x0102, None, None, 4, None, None, "ControlDataForSharePointSignalStoreAssistant"),
        0x344f: (0x0102, None, None, 4, None, None, "ControlDataForPeopleCentricTriageAssistant"),
        0x3450: (0x0003, None, None, 4, None, None, "NotificationBrokerSubscriptions"),
        0x3452: (0x0014, None, None, 4, None, None, "ElcLastRunTotalProcessingTime"),
        0x3453: (0x0014, None, None, 4, None, None, "ElcLastRunSubAssistantProcessingTime"),
        0x3454: (0x0014, None, None, 4, None, None, "ElcLastRunUpdatedFolderCount"),
        0x3455: (0x0014, None, None, 4, None, None, "ElcLastRunTaggedFolderCount"),
        0x3456: (0x0014, None, None, 4, None, None, "ElcLastRunUpdatedItemCount"),
        0x3457: (0x0014, None, None, 4, None, None, "ElcLastRunTaggedWithArchiveItemCount"),
        0x3458: (0x0014, None, None, 4, None, None, "ElcLastRunTaggedWithExpiryItemCount"),
        0x3459: (0x0014, None, None, 4, None, None, "ElcLastRunDeletedFromRootItemCount"),
        0x345a: (0x0014, None, None, 4, None, None, "ElcLastRunDeletedFromDumpsterItemCount"),
        0x345b: (0x0014, None, None, 4, None, None, "ElcLastRunArchivedFromRootItemCount"),
        0x345c: (0x0014, None, None, 4, None, None, "ElcLastRunArchivedFromDumpsterItemCount"),
        0x345d: (0x0040, None, None, 4, None, None, "ScheduledISIntegLastFinished"),
        0x345f: (0x0040, None, None, 4, None, None, "ELCLastSuccessTimestamp"),
        0x3460: (0x0040, None, None, 4, None, None, "EventEmailReminderTimer"),
        0x3463: (0x0102, None, None, 4, None, None, "ControlDataForGroupMailboxAssistant"),
        0x3464: (0x0003, None, None, 4, None, None, "ItemsPendingUpgrade"),
        0x3465: (0x0003, None, None, 4, None, None, "ConsumerSharingCalendarSubscriptionCount"),
        0x3466: (0x0003, None, None, 4, None, None, "GroupMailboxGeneratedPhotoVersion"),
        0x3467: (0x0102, None, None, 4, None, None, "GroupMailboxGeneratedPhotoSignature"),
        0x3468: (0x0003, None, None, 4, None, None, "AadGroupPublishedVersion"),
        0x3469: (0x001f, None, None, 4, None, None, "ElcFaiSaveStatus"),
        0x346a: (0x001f, None, None, 4, None, None, "ElcFaiDeleteStatus"),
        0x346b: (0x0102, None, None, 4, None, None, "ControlDataForCleanupActionsAssistant"),
        0x346c: (0x1003, None, None, 4, None, None, "HolidayCalendarVersionInfo"),
        0x346d: (0x0003, None, None, 4, None, None, "HolidayCalendarSubscriptionCount"),
        0x346e: (0x0003, None, None, 4, None, None, "HolidayCalendarHierarchyVersion"),
        0x346f: (0x0003, None, None, 4, None, None, "CalendarVersion"),
        0x3470: (0x0102, None, None, 4, None, None, "ControlDataForDefaultViewIndexAssistant"),
        0x3471: (0x0040, None, None, 4, None, None, "DefaultViewAssistantLastIndexTime"),
        0x3472: (0x0102, None, None, 4, None, None, "ControlDataForAuditTimeBasedAssitantAssistant"),
        0x3473: (0x0040, None, None, 4, None, None, "SystemCategoriesViewLastIndexTime"),
        0x3474: (0x0102, None, None, 4, None, None, "ControlDataForComplianceJobAssistant"),
        0x3475: (0x0102, None, None, 4, None, None, "ControlDataForGoLocalAssistant"),
        0x3476: (0x0102, None, None, 4, None, None, "ControlDataForUserGroupsRelevanceAssistant"),
        0x3477: (0x0040, None, None, 4, None, None, "GroupMailboxLastUsageCollectionTime"),
        0x3478: (0x0040, None, None, 4, None, None, "EventPushReminderTimer"),
        0x3479: (0x000b, None, None, 4, None, None, "PushReminderSubscriptionType"),
        0x347a: (0x0102, None, None, 4, None, None, "ControlDataForHashtagsRelevanceAssistant"),
        0x347b: (0x0102, None, None, 4, None, None, "ControlDataForSearchFeatureExtractionAssistant"),
        0x347c: (0x0040, None, None, 4, None, None, "EventMeetingConflictTimer"),
        0x347d: (0x0040, None, None, 4, None, None, "MentionsViewLastIndexTime"),
        0x347e: (0x0102, None, None, 4, None, None, "O365SuiteNotificationType"),
        0x347f: (0x0102, None, None, 4, None, None, "ControlDataForResourceUsageLoggingTimeBasedAssistant"),
        0x3480: (0x0102, None, None, 4, None, None, "ControlDataForConferenceRoomUsageAssistant"),
        0x3481: (0x0102, None, None, 4, None, None, "ControlDataForRetrospectiveFeaturizationAssistant"),
        0x3482: (0x000b, None, None, 4, None, None, "ConferenceRoomUsageUpdateNeeded"),
        0x3483: (0x0102, None, None, 4, None, None, "ControlDataForGriffinTimeBasedAssistant"),
        0x3484: (0x0102, None, None, 4, None, None, "FeaturizerExperimentId"),
        0x3485: (0x0102, None, None, 4, None, None, "ControlDataForPeopleInsightsTimeBasedAssistant"),
        0x3486: (0x001f, None, None, 4, None, None, "MailboxPreferredLocation"),
        0x3487: (0x0102, None, None, 4, None, None, "ControlDataForContentSubmissionAssistant"),
        0x3489: (0x0102, None, None, 4, None, None, "ControlDataForPublicFolderHierarchySyncAssistant"),
        0x348a: (0x0102, None, None, 4, None, None, "LastActiveParentEntryId"),
        0x348b: (0x000b, None, None, 4, None, None, "WasParentDeletedItems"),
        0x348c: (0x0003, None, None, 4, None, None, "PushSyncProcessingFlags"),
        0x348d: (0x0102, None, None, 4, None, None, "ControlDataForSuggestedUserGroupAssociationsAssistant"),
        0x348e: (0x0102, None, None, 4, None, None, "ControlDataForReminderSettingsAssistant"),
        0x348f: (0x0102, None, None, 4, None, None, "ControlDataForFileExtractionTimeBasedAssistant"),
        0x3490: (0x0102, None, None, 4, None, None, "ControlDataForMailboxDataExportAssistant"),
        0x3491: (0x0102, None, None, 4, None, None, "ControlDataForXrmActivityStreamMaintenanceAssistant"),
        0x3492: (0x0003, None, None, 4, None, None, "GroupMailboxSegmentationVersion"),
        0x3493: (0x0102, None, None, 4, None, None, "ControlDataForTimeProfileTimeBasedAssistant"),
        0x3494: (0x000b, None, None, 4, None, None, "ReactorSubscriptionCreationNeeded"),
        0x3495: (0x0102, None, None, 4, None, None, "ControlDataForMailboxUsageAnalysisAssistant"),
        0x3496: (0x0102, None, None, 4, None, None, "ControlDataForContactCleanUpAssistant"),
        0x3497: (0x0102, None, None, 4, None, None, "ControlDataForGroupCalendarSubscriptionAssistant"),
        0x3498: (0x0102, None, None, 4, None, None, "ControlDataForGriffinLightweightTimeBasedAssistant"),
        0x3499: (0x0102, None, None, 4, None, None, "ControlDataForXrmAutoTaggingMaintenanceAssistant"),
        0x349a: (0x0102, None, None, 4, None, None, "ControlDataForCalculatedValueTimeBasedAssistant"),
        0x349b: (0x0102, None, None, 4, None, None, "ControlDataForSkypeContactCleanUpAssistant"),
        0x349c: (0x0102, None, None, 4, None, None, "ControlDataForShardRelevancyAssistant"),
        0x349d: (0x0003, None, None, 4, None, None, "MeetingLocationCacheVersion"),
        0x349e: (0x0040, None, None, 4, None, None, "SuperFocusedViewLastIndexTime"),
        0x34a0: (0x0102, None, None, 4, None, None, "ControlDataForCalendarFeaturizationAssistant"),
        0x34a1: (0x0102, None, None, 4, None, None, "ControlDataForBigFunnelRetryFeederTimeBasedAssistant"),
        0x34a2: (0x0003, None, None, 4, None, None, "MeetingLocationCacheVersionV3"),
        0x34a3: (0x0102, None, None, 4, None, None, "ControlDataForSharingMigrationTimeBasedAssistant"),
        0x34a4: (0x0102, None, None, 4, None, None, "ControlDataForDynamicAttachmentTimeBasedAssistant"),
        0x34a5: (0x0102, None, None, 4, None, None, "ControlDataForSharingSyncAssistant"),
        0x34a6: (0x0040, None, None, 4, None, None, "TailoredPropertiesViewLastIndexTime"),
        0x34a7: (0x000b, None, None, 4, None, None, "AtpDynamicAttachmentEnabled"),
        0x34a8: (0x0003, None, None, 4, None, None, "MailboxAssociationVersion"),
        0x34af: (0x0102, None, None, 4, None, None, "ResourceUsageAggregate"),
        0x34b0: (0x000b, None, None, 4, None, None, "ResourceUsageDataReady"),
        0x34b1: (0x0040, None, None, 4, None, None, "ResourceUsageMinDateTime"),
        0x34b2: (0x0040, None, None, 4, None, None, "ResourceUsageMaxDateTime"),
        0x34b3: (0x0003, None, None, 4, None, None, "ResourceUsageNumberOfActivities"),
        0x34b4: (0x0003, None, None, 4, None, None, "ResourceUsageNumberOfCallsSlow"),
        0x34b5: (0x0003, None, None, 4, None, None, "ResourceUsageTotalCalls"),
        0x34b6: (0x0003, None, None, 4, None, None, "ResourceUsageTotalChunks"),
        0x34b7: (0x0003, None, None, 4, None, None, "ResourceUsageTotalCpuTimeKernel"),
        0x34b8: (0x0003, None, None, 4, None, None, "ResourceUsageTotalCpuTimeUser"),
        0x34b9: (0x0003, None, None, 4, None, None, "ResourceUsageTotalDatabaseReadWaitTime"),
        0x34ba: (0x0003, None, None, 4, None, None, "ResourceUsageTotalDatabaseTime"),
        0x34bb: (0x0014, None, None, 4, None, None, "ResourceUsageTotalLogBytes"),
        0x34bc: (0x0003, None, None, 4, None, None, "ResourceUsageTotalPagesDirtied"),
        0x34bd: (0x0003, None, None, 4, None, None, "ResourceUsageTotalPagesPreread"),
        0x34be: (0x0003, None, None, 4, None, None, "ResourceUsageTotalPagesRead"),
        0x34bf: (0x0003, None, None, 4, None, None, "ResourceUsageTotalPagesRedirtied"),
        0x34c0: (0x0003, None, None, 4, None, None, "ResourceUsageTotalTime"),
        0x34c1: (0x0102, None, None, 4, None, None, "ControlDataForPicwAssistant"),
        0x34c2: (0x0014, None, None, 4, None, None, "ResourceUsageClientTypeBitmap"),
        0x34c3: (0x0003, None, None, 4, None, None, "ConnectorConfigurationCount"),
        0x34c4: (0x0102, None, None, 4, None, None, "ResourceUsageRollingAvgRopAggregate"),
        0x34c5: (0x0003, None, None, 4, None, None, "ResourceUsageRollingAvgRop"),
        0x34c6: (0x0014, None, None, 4, None, None, "ResourceUsageRollingClientTypes"),
        0x34c7: (0x0102, None, None, 4, None, None, "ControlDataForComposeGroupSuggestionTimeBasedAssistant"),
        0x34c8: (0x000b, None, None, 4, None, None, "HasSubstrateData"),
        0x34c9: (0x0102, None, None, 4, None, None, "ControlDataForAddressListIndexAssistant"),
        0x34ca: (0x0102, None, None, 4, None, None, "ControlDataForActivitySharingTimeBasedAssistant"),
        0x34cb: (0x0102, None, None, 4, None, None, "ControlDataForShardRelevancyMultiStepAssistant"),
        0x34cc: (0x0102, None, None, 4, None, None, "ControlDataForXrmProvisioningTimeBasedAssistant"),
        0x34cd: (0x0102, None, None, 4, None, None, "ControlDataForSmbTenantProvisioningAssistant"),
        0x34ce: (0x0102, None, None, 4, None, None, "ControlDataForSupervisoryReviewTimeBasedAssistant"),
        0x34cf: (0x0102, None, None, 4, None, None, "ControlDataForMailboxQuotaAssistant"),
        0x34d0: (0x000b, None, None, 4, None, None, "IsQuotaSetByMailboxQuotaAssistant"),
        0x34d1: (0x0040, None, None, 4, None, None, "SuperReactClientViewLastIndexTime"),
        0x34d2: (0x0003, None, None, 4, None, None, "FileFolderFlags"),
        0x3500: (0x0102, None, None, 4, None, None, "ControlDataForBookingsTimeBasedAssistant"),
        0x35d8: (0x0102, None, None, 4, None, None, "RootEntryId"),
        0x35e1: (0x0102, None, None, 4, None, None, "IpmInboxEntryId"),
        0x35e8: (0x0102, None, None, 4, None, None, "SpoolerQueueEntryId"),
        0x35e9: (0x0102, None, None, 4, None, None, "ProtectedMailboxKey"),
        0x35ea: (0x0102, None, None, 4, None, None, "SyncRootFolderEntryId"),
        0x35eb: (0x0102, None, None, 4, None, None, "UMVoicemailFolderEntryId"),
        0x35ed: (0x0102, None, None, 4, None, None, "EHAMigrationFolderEntryId"),
        0x35f6: (0x0102, None, None, 4, None, None, "DeletionsFolderEntryId"),
        0x35f7: (0x0102, None, None, 4, None, None, "PurgesFolderEntryId"),
        0x35f8: (0x0102, None, None, 4, None, None, "DiscoveryHoldsFolderEntryId"),
        0x35f9: (0x0102, None, None, 4, None, None, "VersionsFolderEntryId"),
        0x35fa: (0x0102, None, None, 4, None, None, "ControlDataForBigFunnelMetricsCollectionAssistant"),
        0x35fb: (0x0003, None, None, 4, None, None, "BigFunnelMetricsCollectionAssistantVersion"),
        0x35fc: (0x0102, None, None, 4, None, None, "PublicFolderDiscoveryHoldsEntryId"),
        0x35fd: (0x0102, None, None, 4, None, None, "SystemFolderEntryId"),
        0x35ff: (0x0102, None, None, 4, None, None, "ArchiveFolderEntryId"),
        0x361c: (0x0102, None, None, 4, None, None, "PackedNamedProps"),
        0x3645: (0x000b, None, None, 4, None, None, "PartOfContentIndexing"),
        0x3647: (0x0003, None, None, 4, None, None, "SearchFolderAgeOutTimeout"),
        0x3648: (0x0003, None, None, 4, None, None, "SearchFolderPopulationResult"),
        0x3649: (0x0102, None, None, 4, None, None, "SearchFolderPopulationDiagnostics"),
        0x364a: (0x0003, None, None, 4, None, None, "FolderDatabaseVersion"),
        0x364b: (0x0014, None, None, 4, None, None, "SystemMessageCount"),
        0x364c: (0x0014, None, None, 4, None, None, "SystemMessageSize"),
        0x364d: (0x0014, None, None, 4, None, None, "SystemMessageSizeWarningQuota"),
        0x364e: (0x0014, None, None, 4, None, None, "SystemMessageSizeShutoffQuota"),
        0x364f: (0x0003, None, None, 4, None, None, "TotalPages"),
        0x3651: (0x000b, None, None, 4, None, None, "ClusterMessages"),
        0x3652: (0x0102, None, None, 4, None, None, "MessageTenantPartitionHintForValidation"),
        0x3653: (0x0102, None, None, 4, None, None, "BigFunnelPOIUncompressed"),
        0x3655: (0x000b, None, None, 4, None, None, "BigFunnelPOIIsUpToDate"),
        0x3656: (0x000b, None, None, 4, None, None, "AggressiveOportunisticPromotionForMessages"),
        0x3657: (0x0102, None, None, 4, None, None, "BigFunnelPartialPOIUncompressed"),
        0x3658: (0x0048, None, None, 4, None, None, "MessageMailboxGuidForValidation"),
        0x3659: (0x0003, None, None, 4, None, None, "DatabaseSchemaVersion"),
        0x365a: (0x0003, None, None, 4, None, None, "BigFunnelPoiNotNeededReason"),
        0x365b: (0x000b, None, None, 4, None, None, "PerUserTrackingBasedOnImmutableId"),
        0x365c: (0x0003, None, None, 4, None, None, "SetSearchCriteriaFlags"),
        0x365d: (0x000b, None, None, 4, None, None, "LargeOnPageThreshold"),
        0x3661: (0x000b, None, None, 4, None, None, "SecondaryKeyConstraintEnabled"),
        0x3662: (0x001f, None, None, 4, None, None, "SecondaryKey"),
        0x3663: (0x0003, None, None, 4, None, None, "BigFunnelPOIContentFlags"),
        0x3664: (0x0102, None, None, 4, None, None, "ControlDataForInferenceClutterCleanUpAssistant"),
        0x3665: (0x0003, None, None, 4, None, None, "BigFunnelMailboxPOIVersion"),
        0x3666: (0x0003, None, None, 4, None, None, "BigFunnelMessageUncompressedPOIVersion"),
        0x3669: (0x0102, None, None, 4, None, None, "ControlDataForInferenceTimeModelAssistant"),
        0x366a: (0x0003, None, None, 4, None, None, "MailCategorizerProcessedVersion"),
        0x368e: (0x0102, None, None, 4, None, None, "BigFunnelPOI"),
        0x368f: (0x0003, None, None, 4, None, None, "ContentAggregationFlags"),
        0x36bf: (0x0102, None, None, 4, None, None, "UMFaxFolderEntryId"),
        0x36cc: (0x001f, None, None, 4, None, None, "RecoveredPublicFolderOriginalPath"),
        0x36cd: (0x0102, None, None, 4, None, None, "PerMailboxRecoveryContainerEntryId"),
        0x36ce: (0x0102, None, None, 4, None, None, "LostAndFoundFolderEntryId"),
        0x36cf: (0x0102, None, None, 4, None, None, "CurrentIPMWasteBasketContainerEntryId"),
        0x36d6: (0x0102, None, None, 4, None, None, "RemindersSearchOfflineFolderEntryId"),
        0x36db: (0x0040, None, None, 4, None, None, "ContainerTimestamp"),
        0x36dc: (0x0102, None, None, 4, None, None, "AppointmentColorName"),
        0x36dd: (0x0003, None, None, 4, None, None, "INetUnread"),
        0x36de: (0x0003, None, None, 4, None, None, "NetFolderFlags"),
        0x36df: (0x0102, None, None, 4, None, None, "FolderWebViewInfo"),
        0x36e0: (0x0102, None, None, 4, None, None, "FolderWebViewInfoExtended"),
        0x36e1: (0x0003, None, None, 4, None, None, "FolderViewFlags"),
        0x36e6: (0x001f, None, None, 4, None, None, "DefaultPostDisplayName"),
        0x36eb: (0x0102, None, None, 4, None, None, "FolderViewList"),
        0x36ec: (0x0003, None, None, 4, None, None, "AgingPeriod"),
        0x36ee: (0x0003, None, None, 4, None, None, "AgingGranularity"),
        0x36f0: (0x0003, None, None, 4, None, None, "DefaultFoldersLocaleId"),
        0x36f1: (0x000b, None, None, 4, None, None, "InternalAccess"),
        0x36f2: (0x0102, None, None, 4, None, None, "PublicFolderSplitStateBinary"),
        0x36f3: (0x0102, None, None, 4, None, None, "PublicFolderHierarchySyncNotificationsFolderEntryId"),
        0x36f4: (0x000b, None, None, 4, None, None, "IncludeInContentIndex"),
        0x36f5: (0x0102, None, None, 4, None, None, "PublicFolderProcessorsStateBinary"),
        0x36f6: (0x000b, None, None, 4, None, None, "SystemUse"),
        0x36f7: (0x0048, None, None, 4, None, None, "LowLatencyContainerId"),
        0x36f8: (0x0003, None, None, 4, None, None, "LowLatencyContainerQuota"),
        0x3710: (0x0003, None, None, 4, None, None, "AttachmentMimeSequence"),
        0x371c: (0x000b, None, None, 4, None, None, "FailedInboundICalAsAttachment"),
        0x3720: (0x001f, None, None, 4, None, None, "AttachmentOriginalUrl"),
        0x3880: (0x0102, None, None, 4, None, None, "SyncEventSuppressGuid"),
        0x39fd: (0x101f, None, None, 4, None, None, "ListOfContactPhoneNumbersAndEmails"),
        0x3a3f: (0x000b, None, None, 4, None, None, "SkipSynchronousDelivery"),
        0x3a76: (0x001f, None, None, 4, None, None, "PartnerNetworkId"),
        0x3a77: (0x001f, None, None, 4, None, None, "PartnerNetworkUserId"),
        0x3a78: (0x001f, None, None, 4, None, None, "PartnerNetworkThumbnailPhotoUrl"),
        0x3a79: (0x001f, None, None, 4, None, None, "PartnerNetworkProfilePhotoUrl"),
        0x3a7a: (0x001f, None, None, 4, None, None, "PartnerNetworkContactType"),
        0x3a7b: (0x0003, None, None, 4, None, None, "RelevanceScore"),
        0x3a7c: (0x000b, None, None, 4, None, None, "IsDistributionListContact"),
        0x3a7d: (0x000b, None, None, 4, None, None, "IsPromotedContact"),
        0x3bfa: (0x101f, None, None, 4, None, None, "UserConfiguredConnectedAccounts"),
        0x3bfe: (0x001f, None, None, 4, None, None, "OrgUnitName"),
        0x3bff: (0x001f, None, None, 4, None, None, "OrganizationName"),
        0x3d0b: (0x001f, None, None, 4, None, None, "ServiceEntryName"),
        0x3d22: (0x0102, None, None, 4, None, None, "Win32NTSecurityDescriptor"),
        0x3d23: (0x000b, None, None, 4, None, None, "NonWin32ACL"),
        0x3d24: (0x000b, None, None, 4, None, None, "ItemLevelACL"),
        0x3d2e: (0x0102, None, None, 4, None, None, "ICSGid"),
        0x3d87: (0x0003, None, None, 4, None, None, "BigFunnelPostingReplicationScavengedBucketsWatermark"),
        0x3d88: (0x0003, None, None, 4, None, None, "BigFunnelPostingListReplicationScavengedBucketsAllowed"),
        0x3d89: (0x0040, None, None, 4, None, None, "BigFunnelLastCleanupMaintenance"),
        0x3d8b: (0x0040, None, None, 4, None, None, "BigFunnelPostingListLastCompactionMerge"),
        0x3d8c: (0x0102, None, None, 4, None, None, "BigFunnelAttributeVectorSharePointDataV1"),
        0x3d8d: (0x0014, None, None, 4, None, None, "BigFunnelL1PropertyLengths2V1"),
        0x3d8e: (0x0014, None, None, 4, None, None, "BigFunnelL1PropertyLengths1V1Rebuild"),
        0x3d8f: (0x000b, None, None, 4, None, None, "MessageSubmittedByOutlook"),
        0x3d90: (0x0003, None, None, 4, None, None, "BigFunnelPostingListTargetTableVersion"),
        0x3d91: (0x0003, None, None, 4, None, None, "BigFunnelPostingListTargetTableChunkSize"),
        0x3d92: (0x0014, None, None, 4, None, None, "BigFunnelL1PropertyLengths1V1"),
        0x3d93: (0x0003, None, None, 4, None, None, "ScopeKeyTokenType"),
        0x3d94: (0x0003, None, None, 4, None, None, "BigFunnelPostingListTableVersion"),
        0x3d95: (0x0003, None, None, 4, None, None, "BigFunnelPostingListTableChunkSize"),
        0x3d96: (0x0040, None, None, 4, None, None, "LastTableSizeStatisticsUpdate"),
        0x3d97: (0x000b, None, None, 4, None, None, "IcsRestrictionMatch"),
        0x3d98: (0x0102, None, None, 4, None, None, "BigFunnelPartialPOI"),
        0x3d99: (0x0102, None, None, 4, None, None, "LargeReservedDocIdRanges"),
        0x3d9a: (0x0048, None, None, 4, None, None, "DocIdAsImmutableIdGuid"),
        0x3d9b: (0x0040, None, None, 4, None, None, "MoveCompletionTime"),
        0x3d9c: (0x0102, None, None, 4, None, None, "MaterializedRestrictionSearchRoot"),
        0x3d9d: (0x0003, None, None, 4, None, None, "ScheduledISIntegCorruptionCount"),
        0x3d9e: (0x0003, None, None, 4, None, None, "ScheduledISIntegExecutionTime"),
        0x3da1: (0x0102, None, None, 4, None, None, "QueryCriteriaInternal"),
        0x3da2: (0x0040, None, None, 4, None, None, "LastQuotaNotificationTime"),
        0x3da3: (0x000b, None, None, 4, None, None, "PropertyPromotionInProgressHiddenItems"),
        0x3da4: (0x000b, None, None, 4, None, None, "PropertyPromotionInProgressNormalItems"),
        0x3da5: (0x001f, None, None, 4, None, None, "VirtualParentDisplay"),
        0x3da6: (0x0003, None, None, 4, None, None, "MailboxTypeDetail"),
        0x3da7: (0x0102, None, None, 4, None, None, "InternalTenantHint"),
        0x3da8: (0x000b, None, None, 4, None, None, "InternalConversationIndexTracking"),
        0x3da9: (0x0102, None, None, 4, None, None, "InternalConversationIndex"),
        0x3daa: (0x0102, None, None, 4, None, None, "ConversationItemConversationId"),
        0x3dab: (0x0014, None, None, 4, None, None, "VirtualUnreadMessageCount"),
        0x3dac: (0x000b, None, None, 4, None, None, "VirtualIsRead"),
        0x3dad: (0x000b, None, None, 4, None, None, "IsReadColumn"),
        0x3dae: (0x0102, None, None, 4, None, None, "PersistableTenantPartitionHint"),
        0x3daf: (0x0102, None, None, 4, None, None, "Internal9ByteChangeNumber"),
        0x3db0: (0x0102, None, None, 4, None, None, "Internal9ByteReadCnNew"),
        0x3db1: (0x000b, None, None, 4, None, None, "CategoryHeaderLevelStub1"),
        0x3db2: (0x000b, None, None, 4, None, None, "CategoryHeaderLevelStub2"),
        0x3db3: (0x000b, None, None, 4, None, None, "CategoryHeaderLevelStub3"),
        0x3db4: (0x0102, None, None, 4, None, None, "CategoryHeaderAggregateProp0"),
        0x3db5: (0x0102, None, None, 4, None, None, "CategoryHeaderAggregateProp1"),
        0x3db6: (0x0102, None, None, 4, None, None, "CategoryHeaderAggregateProp2"),
        0x3db7: (0x0102, None, None, 4, None, None, "CategoryHeaderAggregateProp3"),
        0x3db8: (0x0003, None, None, 4, None, None, "MailboxMoveExtendedFlags"),
        0x3dbb: (0x0048, None, None, 4, None, None, "MaintenanceId"),
        0x3dbc: (0x0003, None, None, 4, None, None, "MailboxType"),
        0x3dbd: (0x0003, None, None, 4, None, None, "MessageFlagsActual"),
        0x3dbe: (0x0102, None, None, 4, None, None, "InternalChangeKey"),
        0x3dbf: (0x0102, None, None, 4, None, None, "InternalSourceKey"),
        0x3dd1: (0x0048, None, None, 4, None, None, "CorrelationId"),
        0x3e00: (0x001f, None, None, 4, None, None, "IdentityDisplay"),
        0x3e01: (0x0102, None, None, 4, None, None, "IdentityEntryId"),
        0x3e02: (0x0003, None, None, 4, None, None, "ResourceMethods"),
        0x3e03: (0x0003, None, None, 4, None, None, "ResourceType"),
        0x3e04: (0x0003, None, None, 4, None, None, "StatusCode"),
        0x3e05: (0x0102, None, None, 4, None, None, "IdentitySearchKey"),
        0x3e06: (0x0102, None, None, 4, None, None, "OwnStoreEntryId"),
        0x3e07: (0x001f, None, None, 4, None, None, "ResourcePath"),
        0x3e08: (0x001f, None, None, 4, None, None, "StatusString"),
        0x3e0b: (0x0003, None, None, 4, None, None, "RemoteProgress"),
        0x3e0c: (0x001f, None, None, 4, None, None, "RemoteProgressText"),
        0x3e0d: (0x000b, None, None, 4, None, None, "RemoteValidateOK"),
        0x3f00: (0x0003, None, None, 4, None, None, "ControlFlags"),
        0x3f01: (0x0102, None, None, 4, None, None, "ControlStructure"),
        0x3f02: (0x0003, None, None, 4, None, None, "ControlType"),
        0x3f03: (0x0003, None, None, 4, None, None, "DeltaX"),
        0x3f04: (0x0003, None, None, 4, None, None, "DeltaY"),
        0x3f05: (0x0003, None, None, 4, None, None, "XPos"),
        0x3f06: (0x0003, None, None, 4, None, None, "YPos"),
        0x3f07: (0x0102, None, None, 4, None, None, "ControlId"),
        0x3f88: (0x0014, None, None, 4, None, None, "AttachmentId"),
        0x3f89: (0x0102, None, None, 4, None, None, "GVid"),
        0x3f8a: (0x0102, None, None, 4, None, None, "GDID"),
        0x3f95: (0x0102, None, None, 4, None, None, "XVid"),
        0x3f96: (0x0102, None, None, 4, None, None, "GDefVid"),
        0x3fc8: (0x0102, None, None, 4, None, None, "ReplicaChangeNumber"),
        0x3fc9: (0x0102, None, None, 4, None, None, "LastConflict"),
        0x3fd4: (0x0102, None, None, 4, None, None, "RMI"),
        0x3fd5: (0x0102, None, None, 4, None, None, "InternalPostReply"),
        0x3fd6: (0x0040, None, None, 4, None, None, "NTSDModificationTime"),
        0x3fd7: (0x0003, None, None, 4, None, None, "ACLDataChecksum"),
        0x3fd8: (0x001f, None, None, 4, None, None, "PreviewUnread"),
        0x3fe4: (0x000b, None, None, 4, None, None, "DesignInProgress"),
        0x3fe5: (0x000b, None, None, 4, None, None, "SecureOrigination"),
        0x3fe8: (0x001f, None, None, 4, None, None, "AddressBookDisplayName"),
        0x3ff2: (0x0102, None, None, 4, None, None, "RuleTriggerHistory"),
        0x3ff3: (0x0102, None, None, 4, None, None, "MoveToStoreEntryId"),
        0x3ff4: (0x0102, None, None, 4, None, None, "MoveToFolderEntryId"),
        0x3ffe: (0x0003, None, None, 4, None, None, "QuotaType"),
        0x4000: (0x0003, None, None, 4, None, None, "NewAttachment"),
        0x4001: (0x0003, None, None, 4, None, None, "StartEmbed"),
        0x4002: (0x0003, None, None, 4, None, None, "EndEmbed"),
        0x4003: (0x0003, None, None, 4, None, None, "StartRecip"),
        0x4004: (0x0003, None, None, 4, None, None, "EndRecip"),
        0x4005: (0x0003, None, None, 4, None, None, "EndCcRecip"),
        0x4006: (0x0003, None, None, 4, None, None, "EndBccRecip"),
        0x4007: (0x0003, None, None, 4, None, None, "EndP1Recip"),
        0x4008: (0x001f, None, None, 4, None, None, "DNPrefix"),
        0x4009: (0x0003, None, None, 4, None, None, "StartTopFolder"),
        0x400a: (0x0003, None, None, 4, None, None, "StartSubFolder"),
        0x400b: (0x0003, None, None, 4, None, None, "EndFolder"),
        0x400c: (0x0003, None, None, 4, None, None, "StartMessage"),
        0x400d: (0x0003, None, None, 4, None, None, "EndMessage"),
        0x400e: (0x0003, None, None, 4, None, None, "EndAttachment"),
        0x400f: (0x0003, None, None, 4, None, None, "EcWarning"),
        0x4010: (0x0003, None, None, 4, None, None, "StartFAIMessage"),
        0x4011: (0x0102, None, None, 4, None, None, "NewFXFolder"),
        0x4012: (0x0003, None, None, 4, None, None, "IncrSyncChange"),
        0x4013: (0x0003, None, None, 4, None, None, "IncrSyncDelete"),
        0x4014: (0x0003, None, None, 4, None, None, "IncrSyncEnd"),
        0x4015: (0x0003, None, None, 4, None, None, "IncrSyncMessage"),
        0x4016: (0x0003, None, None, 4, None, None, "FastTransferDelProp"),
        0x4017: (0x0102, None, None, 4, None, None, "IdsetGiven"),
        0x4018: (0x0003, None, None, 4, None, None, "FastTransferErrorInfo"),
        0x4019: (0x0003, None, None, 4, None, None, "SenderFlags"),
        0x401b: (0x0003, None, None, 4, None, None, "ReceivedByFlags"),
        0x401c: (0x0003, None, None, 4, None, None, "ReceivedRepresentingFlags"),
        0x401d: (0x0003, None, None, 4, None, None, "OriginalSenderFlags"),
        0x401e: (0x0003, None, None, 4, None, None, "OriginalSentRepresentingFlags"),
        0x401f: (0x0003, None, None, 4, None, None, "ReportFlags"),
        0x4020: (0x0003, None, None, 4, None, None, "ReadReceiptFlags"),
        0x4021: (0x0102, None, None, 4, None, None, "SoftDeletes"),
        0x4022: (0x001f, None, None, 4, None, None, "CreatorAddressType"),
        0x4023: (0x001f, None, None, 4, None, None, "CreatorEmailAddress"),
        0x4024: (0x001f, None, None, 4, None, None, "LastModifierAddressType"),
        0x4025: (0x001f, None, None, 4, None, None, "LastModifierEmailAddress"),
        0x4026: (0x001f, None, None, 4, None, None, "ReportAddressType"),
        0x4027: (0x001f, None, None, 4, None, None, "ReportEmailAddress"),
        0x4028: (0x001f, None, None, 4, None, None, "ReportDisplayName"),
        0x402d: (0x0102, None, None, 4, None, None, "IdsetRead"),
        0x402e: (0x0102, None, None, 4, None, None, "IdsetUnread"),
        0x402f: (0x0003, None, None, 4, None, None, "IncrSyncRead"),
        0x4037: (0x001f, None, None, 4, None, None, "ReportSimpleDisplayName"),
        0x4038: (0x001f, None, None, 4, None, None, "CreatorSimpleDisplayName"),
        0x4039: (0x001f, None, None, 4, None, None, "LastModifierSimpleDisplayName"),
        0x403a: (0x0003, None, None, 4, None, None, "IncrSyncStateBegin"),
        0x403b: (0x0003, None, None, 4, None, None, "IncrSyncStateEnd"),
        0x403c: (0x0003, None, None, 4, None, None, "IncrSyncImailStream"),
        0x403f: (0x001f, None, None, 4, None, None, "SenderOriginalAddressType"),
        0x4040: (0x001f, None, None, 4, None, None, "SenderOriginalEmailAddress"),
        0x4041: (0x001f, None, None, 4, None, None, "SentRepresentingOriginalAddressType"),
        0x4042: (0x001f, None, None, 4, None, None, "SentRepresentingOriginalEmailAddress"),
        0x4043: (0x001f, None, None, 4, None, None, "OriginalSenderOriginalAddressType"),
        0x4044: (0x001f, None, None, 4, None, None, "OriginalSenderOriginalEmailAddress"),
        0x4045: (0x001f, None, None, 4, None, None, "OriginalSentRepresentingOriginalAddressType"),
        0x4046: (0x001f, None, None, 4, None, None, "OriginalSentRepresentingOriginalEmailAddress"),
        0x4047: (0x001f, None, None, 4, None, None, "ReceivedByOriginalAddressType"),
        0x4048: (0x001f, None, None, 4, None, None, "ReceivedByOriginalEmailAddress"),
        0x4049: (0x001f, None, None, 4, None, None, "ReceivedRepresentingOriginalAddressType"),
        0x404a: (0x001f, None, None, 4, None, None, "ReceivedRepresentingOriginalEmailAddress"),
        0x404b: (0x001f, None, None, 4, None, None, "ReadReceiptOriginalAddressType"),
        0x404c: (0x001f, None, None, 4, None, None, "ReadReceiptOriginalEmailAddress"),
        0x404d: (0x001f, None, None, 4, None, None, "ReportOriginalAddressType"),
        0x404e: (0x001f, None, None, 4, None, None, "ReportOriginalEmailAddress"),
        0x404f: (0x001f, None, None, 4, None, None, "CreatorOriginalAddressType"),
        0x4050: (0x001f, None, None, 4, None, None, "CreatorOriginalEmailAddress"),
        0x4051: (0x001f, None, None, 4, None, None, "LastModifierOriginalAddressType"),
        0x4052: (0x001f, None, None, 4, None, None, "LastModifierOriginalEmailAddress"),
        0x4053: (0x001f, None, None, 4, None, None, "OriginatorOriginalAddressType"),
        0x4054: (0x001f, None, None, 4, None, None, "OriginatorOriginalEmailAddress"),
        0x4055: (0x001f, None, None, 4, None, None, "ReportDestinationOriginalAddressType"),
        0x4056: (0x001f, None, None, 4, None, None, "ReportDestinationOriginalEmailAddress"),
        0x4057: (0x001f, None, None, 4, None, None, "OriginalAuthorOriginalAddressType"),
        0x4058: (0x001f, None, None, 4, None, None, "OriginalAuthorOriginalEmailAddress"),
        0x4059: (0x0003, None, None, 4, None, None, "CreatorFlags"),
        0x405a: (0x0003, None, None, 4, None, None, "LastModifierFlags"),
        0x405b: (0x0003, None, None, 4, None, None, "OriginatorFlags"),
        0x405c: (0x0003, None, None, 4, None, None, "ReportDestinationFlags"),
        0x405d: (0x0003, None, None, 4, None, None, "OriginalAuthorFlags"),
        0x405e: (0x001f, None, None, 4, None, None, "OriginatorSimpleDisplayName"),
        0x405f: (0x001f, None, None, 4, None, None, "ReportDestinationSimpleDisplayName"),
        0x4061: (0x0102, None, None, 4, None, None, "OriginatorSearchKey"),
        0x4062: (0x001f, None, None, 4, None, None, "ReportDestinationAddressType"),
        0x4063: (0x001f, None, None, 4, None, None, "ReportDestinationEmailAddress"),
        0x4064: (0x0102, None, None, 4, None, None, "ReportDestinationSearchKey"),
        0x4066: (0x0003, None, None, 4, None, None, "IncrSyncImailStreamContinue"),
        0x4067: (0x0003, None, None, 4, None, None, "IncrSyncImailStreamCancel"),
        0x4071: (0x0003, None, None, 4, None, None, "IncrSyncImailStream2Continue"),
        0x4074: (0x000b, None, None, 4, None, None, "IncrSyncProgressMode"),
        0x4075: (0x000b, None, None, 4, None, None, "SyncProgressPerMsg"),
        0x407a: (0x0003, None, None, 4, None, None, "IncrSyncMsgPartial"),
        0x407b: (0x0003, None, None, 4, None, None, "IncrSyncGroupInfo"),
        0x407c: (0x0003, None, None, 4, None, None, "IncrSyncGroupId"),
        0x407d: (0x0003, None, None, 4, None, None, "IncrSyncChangePartial"),
        0x4084: (0x0003, None, None, 4, None, None, "ContentFilterPCL"),
        0x4085: (0x0040, None, None, 4, None, None, "PeopleInsightsLastAccessTime"),
        0x4086: (0x0040, None, None, 4, None, None, "EmailUsageLastActivityTime"),
        0x4087: (0x0003, None, None, 4, None, None, "PdpProfileDataMigrationFlags"),
        0x4088: (0x0102, None, None, 4, None, None, "ControlDataForPdpDataMigrationAssistant"),
        0x5500: (0x000b, None, None, 4, None, None, "IsInterestingForFileExtraction"),
        0x5d03: (0x001f, None, None, 4, None, None, "OriginalSenderSMTPAddress"),
        0x5d04: (0x001f, None, None, 4, None, None, "OriginalSentRepresentingSMTPAddress"),
        0x5d06: (0x001f, None, None, 4, None, None, "OriginalAuthorSMTPAddress"),
        0x5d09: (0x0102, None, None, 4, None, None, "MessageUsageData"),
        0x5d0a: (0x001f, None, None, 4, None, None, "CreatorSMTPAddress"),
        0x5d0b: (0x001f, None, None, 4, None, None, "LastModifierSMTPAddress"),
        0x5d0c: (0x001f, None, None, 4, None, None, "ReportSMTPAddress"),
        0x5d0d: (0x001f, None, None, 4, None, None, "OriginatorSMTPAddress"),
        0x5d0e: (0x001f, None, None, 4, None, None, "ReportDestinationSMTPAddress"),
        0x5fe5: (0x001f, None, None, 4, None, None, "RecipientSipUri"),
        0x6000: (0x0003, None, None, 4, None, None, "RssServerLockStartTime"),
        0x6001: (0x001f, None, None, 4, None, None, "DotStuffState"),
        0x6002: (0x001f, None, None, 4, None, None, "RssServerLockClientName"),
        0x61af: (0x0102, None, None, 4, None, None, "ScheduleData"),
        0x65ef: (0x0102, None, None, 4, None, None, "RuleMsgActions"),
        0x65f0: (0x0102, None, None, 4, None, None, "RuleMsgCondition"),
        0x65f1: (0x0003, None, None, 4, None, None, "RuleMsgConditionLCID"),
        0x65f4: (0x000b, None, None, 4, None, None, "PreventMsgCreate"),
        0x65f9: (0x0102, None, None, 4, None, None, "LISSD"),
        0x65fa: (0x101f, None, None, 4, None, None, "IMAPUnsubscribeList"),
        0x6607: (0x001f, None, None, 4, None, None, "ProfileUnresolvedName"),
        0x660d: (0x0003, None, None, 4, None, None, "ProfileMaxRestrict"),
        0x660e: (0x001f, None, None, 4, None, None, "ProfileABFilesPath"),
        0x660f: (0x001f, None, None, 4, None, None, "ProfileFavFolderDisplayName"),
        0x6613: (0x101f, None, None, 4, None, None, "ProfileHomeServerAddrs"),
        0x662b: (0x0102, None, None, 4, None, None, "TestLineSpeed"),
        0x6630: (0x0102, None, None, 4, None, None, "LegacyShortcutsFolderEntryId"),
        0x6635: (0x001f, None, None, 4, None, None, "FavoritesDefaultName"),
        0x6641: (0x0003, None, None, 4, None, None, "DeletedFolderCount"),
        0x6643: (0x0003, None, None, 4, None, None, "DeletedAssociatedMessageCount32"),
        0x6644: (0x001f, None, None, 4, None, None, "ReplicaServer"),
        0x664c: (0x0102, None, None, 4, None, None, "FidMid"),
        0x6652: (0x0102, None, None, 4, None, None, "ActiveUserEntryId"),
        0x6655: (0x0102, None, None, 4, None, None, "ICSChangeKey"),
        0x6657: (0x0102, None, None, 4, None, None, "SetPropsCondition"),
        0x6659: (0x0102, None, None, 4, None, None, "InternetContent"),
        0x665b: (0x001f, None, None, 4, None, None, "OriginatorName"),
        0x665c: (0x001f, None, None, 4, None, None, "OriginatorEmailAddress"),
        0x665d: (0x001f, None, None, 4, None, None, "OriginatorAddressType"),
        0x665e: (0x0102, None, None, 4, None, None, "OriginatorEntryId"),
        0x6662: (0x0003, None, None, 4, None, None, "RecipientNumber"),
        0x6664: (0x001f, None, None, 4, None, None, "ReportDestinationName"),
        0x6665: (0x0102, None, None, 4, None, None, "ReportDestinationEntryId"),
        0x6692: (0x0003, None, None, 4, None, None, "ReplicationMsgPriority"),
        0x6697: (0x0003, None, None, 4, None, None, "WorkerProcessId"),
        0x669a: (0x0003, None, None, 4, None, None, "CurrentDatabaseSchemaVersion"),
        0x669e: (0x000b, None, None, 4, None, None, "SecureInSite"),
        0x66a7: (0x0003, None, None, 4, None, None, "MailboxFlags"),
        0x66ab: (0x0003, None, None, 4, None, None, "MailboxMessagesPerFolderCountWarningQuota"),
        0x66ac: (0x0003, None, None, 4, None, None, "MailboxMessagesPerFolderCountReceiveQuota"),
        0x66ad: (0x0003, None, None, 4, None, None, "NormalMessagesWithAttachmentsCount32"),
        0x66ae: (0x0003, None, None, 4, None, None, "AssociatedMessagesWithAttachmentsCount32"),
        0x66af: (0x0003, None, None, 4, None, None, "FolderHierarchyChildrenCountWarningQuota"),
        0x66b0: (0x0003, None, None, 4, None, None, "FolderHierarchyChildrenCountReceiveQuota"),
        0x66b1: (0x0003, None, None, 4, None, None, "AttachmentsOnNormalMessagesCount32"),
        0x66b2: (0x0003, None, None, 4, None, None, "AttachmentsOnAssociatedMessagesCount32"),
        0x66b3: (0x0014, None, None, 4, None, None, "NormalMessageSize64"),
        0x66e0: (0x001f, None, None, 4, None, None, "ServerDN"),
        0x66e1: (0x0003, None, None, 4, None, None, "BackfillRanking"),
        0x66e2: (0x0003, None, None, 4, None, None, "LastTransmissionTime"),
        0x66e3: (0x0040, None, None, 4, None, None, "StatusSendTime"),
        0x66e4: (0x0003, None, None, 4, None, None, "BackfillEntryCount"),
        0x66e5: (0x0040, None, None, 4, None, None, "NextBroadcastTime"),
        0x66e6: (0x0040, None, None, 4, None, None, "NextBackfillTime"),
        0x66e7: (0x0102, None, None, 4, None, None, "LastCNBroadcast"),
        0x66eb: (0x0102, None, None, 4, None, None, "BackfillId"),
        0x66f4: (0x0102, None, None, 4, None, None, "LastShortCNBroadcast"),
        0x66fb: (0x0040, None, None, 4, None, None, "AverageTransmissionTime"),
        0x66fc: (0x0014, None, None, 4, None, None, "ReplicationStatus"),
        0x66fd: (0x0040, None, None, 4, None, None, "LastDataReceivalTime"),
        0x670c: (0x1048, None, None, 4, None, None, "AutoReset"),
        0x6712: (0x0102, None, None, 4, None, None, "ScopeFIDs"),
        0x671e: (0x000b, None, None, 4, None, None, "PFPlatinumHomeMdb"),
        0x673f: (0x0102, None, None, 4, None, None, "ReadCnNewExport"),
        0x6745: (0x0102, None, None, 4, None, None, "LocallyDelivered"),
        0x6746: (0x0014, None, None, 4, None, None, "MimeSize"),
        0x6747: (0x0014, None, None, 4, None, None, "FileSize"),
        0x674b: (0x0014, None, None, 4, None, None, "CategID"),
        0x674c: (0x0014, None, None, 4, None, None, "ParentCategID"),
        0x6750: (0x0002, None, None, 4, None, None, "ChangeType"),
        0x6753: (0x000b, None, None, 4, None, None, "Not822Renderable"),
        0x6758: (0x0102, None, None, 4, None, None, "LTID"),
        0x6759: (0x0102, None, None, 4, None, None, "CnExport"),
        0x675a: (0x0102, None, None, 4, None, None, "PclExport"),
        0x675b: (0x0102, None, None, 4, None, None, "CnMvExport"),
        0x675c: (0x0102, None, None, 4, None, None, "MidsetDeletedExport"),
        0x675d: (0x0003, None, None, 4, None, None, "ArticleNumMic"),
        0x675e: (0x0003, None, None, 4, None, None, "ArticleNumMost"),
        0x6760: (0x0003, None, None, 4, None, None, "RulesSync"),
        0x6762: (0x0102, None, None, 4, None, None, "ReplicaListRC"),
        0x6763: (0x0102, None, None, 4, None, None, "ReplicaListRBUG"),
        0x6766: (0x0102, None, None, 4, None, None, "FIDC"),
        0x676b: (0x001f, None, None, 4, None, None, "MailboxOwnerDN"),
        0x676d: (0x0102, None, None, 4, None, None, "IMAPCachedBodystructure"),
        0x676f: (0x001f, None, None, 4, None, None, "AltRecipientDN"),
        0x6770: (0x000b, None, None, 4, None, None, "NoLocalDelivery"),
        0x6771: (0x0003, None, None, 4, None, None, "DeliveryContentLength"),
        0x6772: (0x000b, None, None, 4, None, None, "AutoReply"),
        0x6773: (0x001f, None, None, 4, None, None, "MailboxOwnerDisplayName"),
        0x6774: (0x0040, None, None, 4, None, None, "MailboxLastUpdated"),
        0x6775: (0x001f, None, None, 4, None, None, "AdminSurName"),
        0x6776: (0x001f, None, None, 4, None, None, "AdminGivenName"),
        0x6777: (0x0003, None, None, 4, None, None, "ActiveSearchCount"),
        0x677a: (0x0003, None, None, 4, None, None, "OverQuotaLimit"),
        0x677c: (0x0003, None, None, 4, None, None, "SubmitContentLength"),
        0x677d: (0x0003, None, None, 4, None, None, "LogonRightsOnMailbox"),
        0x677e: (0x0014, None, None, 4, None, None, "ReservedIdCounterRangeUpperLimit"),
        0x677f: (0x0014, None, None, 4, None, None, "ReservedCnCounterRangeUpperLimit"),
        0x6780: (0x0003, None, None, 4, None, None, "SetReceiveCount"),
        0x6781: (0x000b, None, None, 4, None, None, "BigFunnelIsEnabled"),
        0x6782: (0x0003, None, None, 4, None, None, "SubmittedCount"),
        0x6783: (0x0102, None, None, 4, None, None, "CreatorToken"),
        0x6785: (0x0102, None, None, 4, None, None, "SearchFIDs"),
        0x6786: (0x0102, None, None, 4, None, None, "RecursiveSearchFIDs"),
        0x678a: (0x0102, None, None, 4, None, None, "CategFIDs"),
        0x6791: (0x0014, None, None, 4, None, None, "MidSegmentStart"),
        0x6792: (0x0102, None, None, 4, None, None, "MidsetDeleted"),
        0x6793: (0x0102, None, None, 4, None, None, "MidsetExpired"),
        0x6794: (0x0102, None, None, 4, None, None, "CnsetIn"),
        0x6796: (0x0102, None, None, 4, None, None, "CnsetBackfill"),
        0x6798: (0x0102, None, None, 4, None, None, "MidsetTombstones"),
        0x679a: (0x000b, None, None, 4, None, None, "GWFolder"),
        0x679b: (0x000b, None, None, 4, None, None, "IPMFolder"),
        0x679c: (0x001f, None, None, 4, None, None, "PublicFolderPath"),
        0x679f: (0x0002, None, None, 4, None, None, "MidSegmentIndex"),
        0x67a0: (0x0002, None, None, 4, None, None, "MidSegmentSize"),
        0x67a1: (0x0002, None, None, 4, None, None, "CnSegmentStart"),
        0x67a2: (0x0002, None, None, 4, None, None, "CnSegmentIndex"),
        0x67a3: (0x0002, None, None, 4, None, None, "CnSegmentSize"),
        0x67a5: (0x0102, None, None, 4, None, None, "PCL"),
        0x67a6: (0x1014, None, None, 4, None, None, "CnMv"),
        0x67a7: (0x0014, None, None, 4, None, None, "FolderTreeRootFID"),
        0x67a8: (0x0102, None, None, 4, None, None, "SourceEntryId"),
        0x67a9: (0x0002, None, None, 4, None, None, "MailFlags"),
        0x67ab: (0x0003, None, None, 4, None, None, "SubmitResponsibility"),
        0x67ad: (0x000b, None, None, 4, None, None, "SharedReceiptHandling"),
        0x67b3: (0x0102, None, None, 4, None, None, "MessageAttachmentList"),
        0x67b5: (0x0102, None, None, 4, None, None, "SenderCAI"),
        0x67b6: (0x0102, None, None, 4, None, None, "SentRepresentingCAI"),
        0x67b7: (0x0102, None, None, 4, None, None, "OriginalSenderCAI"),
        0x67b8: (0x0102, None, None, 4, None, None, "OriginalSentRepresentingCAI"),
        0x67b9: (0x0102, None, None, 4, None, None, "ReceivedByCAI"),
        0x67ba: (0x0102, None, None, 4, None, None, "ReceivedRepresentingCAI"),
        0x67bb: (0x0102, None, None, 4, None, None, "ReadReceiptCAI"),
        0x67bc: (0x0102, None, None, 4, None, None, "ReportCAI"),
        0x67bd: (0x0102, None, None, 4, None, None, "CreatorCAI"),
        0x67be: (0x0102, None, None, 4, None, None, "LastModifierCAI"),
        0x67c4: (0x0002, None, None, 4, None, None, "AnonymousRights"),
        0x67ce: (0x0102, None, None, 4, None, None, "SearchGUID"),
        0x67d2: (0x0102, None, None, 4, None, None, "CnsetRead"),
        0x67da: (0x0102, None, None, 4, None, None, "CnsetBackfillFAI"),
        0x67de: (0x0003, None, None, 4, None, None, "ReplMsgVersion"),
        0x67e5: (0x0102, None, None, 4, None, None, "IdSetDeleted"),
        0x67e6: (0x0102, None, None, 4, None, None, "FolderMessages"),
        0x67e7: (0x0102, None, None, 4, None, None, "SenderReplid"),
        0x67e8: (0x0014, None, None, 4, None, None, "CnMin"),
        0x67e9: (0x0014, None, None, 4, None, None, "CnMax"),
        0x67ea: (0x0003, None, None, 4, None, None, "ReplMsgType"),
        0x67eb: (0x0102, None, None, 4, None, None, "RgszDNResponders"),
        0x67f2: (0x1003, None, None, 4, None, None, "ViewCoveringPropertyTags"),
        0x67f4: (0x000b, None, None, 4, None, None, "ICSViewFilter"),
        0x67f8: (0x0102, None, None, 4, None, None, "OriginatorCAI"),
        0x67f9: (0x0102, None, None, 4, None, None, "ReportDestinationCAI"),
        0x67fa: (0x0102, None, None, 4, None, None, "OriginalAuthorCAI"),
        0x6807: (0x0014, None, None, 4, None, None, "EventCounter"),
        0x6809: (0x0102, None, None, 4, None, None, "EventFid"),
        0x680a: (0x0102, None, None, 4, None, None, "EventMid"),
        0x680b: (0x0102, None, None, 4, None, None, "EventFidParent"),
        0x680c: (0x0102, None, None, 4, None, None, "EventFidOld"),
        0x680e: (0x0102, None, None, 4, None, None, "EventFidOldParent"),
        0x680f: (0x0040, None, None, 4, None, None, "EventCreatedTime"),
        0x6811: (0x0003, None, None, 4, None, None, "EventItemCount"),
        0x6812: (0x0102, None, None, 4, None, None, "EventFidRoot"),
        0x6818: (0x0014, None, None, 4, None, None, "EventExtendedFlags"),
        0x681c: (0x0102, None, None, 4, None, None, "EventImmutableid"),
        0x681d: (0x0040, None, None, 4, None, None, "MailboxQuarantineEnd"),
        0x681e: (0x0003, None, None, 4, None, None, "EventOldParentDefaultFolderType"),
        0x681f: (0x0003, None, None, 4, None, None, "MailboxNumber"),
        0x6821: (0x0003, None, None, 4, None, None, "InferenceClientId"),
        0x6822: (0x0102, None, None, 4, None, None, "InferenceItemId"),
        0x6823: (0x0040, None, None, 4, None, None, "InferenceCreateTime"),
        0x6824: (0x0048, None, None, 4, None, None, "InferenceWindowId"),
        0x6825: (0x0048, None, None, 4, None, None, "InferenceSessionId"),
        0x6826: (0x0102, None, None, 4, None, None, "InferenceFolderId"),
        0x682e: (0x001f, None, None, 4, None, None, "InferenceTimeZone"),
        0x682f: (0x001f, None, None, 4, None, None, "InferenceCategory"),
        0x6832: (0x0003, None, None, 4, None, None, "InferenceModuleSelected"),
        0x6833: (0x001f, None, None, 4, None, None, "InferenceLayoutType"),
        0x6835: (0x0040, None, None, 4, None, None, "InferenceTimeStamp"),
        0x6836: (0x000b, None, None, 4, None, None, "InferenceOLKUserActivityLoggingEnabled"),
        0x6837: (0x001f, None, None, 4, None, None, "InferenceClientVersion"),
        0x6838: (0x000b, None, None, 4, None, None, "InferenceSSISource"),
        0x6839: (0x0003, None, None, 4, None, None, "ActivityWorkload"),
        0x683b: (0x0003, None, None, 4, None, None, "ActivityItemType"),
        0x683c: (0x0048, None, None, 4, None, None, "ActivityContainerMailbox"),
        0x683d: (0x001f, None, None, 4, None, None, "ActivityContainerId"),
        0x683e: (0x001f, None, None, 4, None, None, "ActivityNonExoItemId"),
        0x683f: (0x0048, None, None, 4, None, None, "ActivityClientInstanceId"),
        0x6840: (0x0102, None, None, 4, None, None, "ActivityImmutableItemId"),
        0x6857: (0x000b, None, None, 4, None, None, "AgingAgeFolder"),
        0x6858: (0x000b, None, None, 4, None, None, "AgingDontAgeMe"),
        0x6859: (0x001f, None, None, 4, None, None, "AgingFileNameAfter9"),
        0x685b: (0x000b, None, None, 4, None, None, "AgingWhenDeletedOnServer"),
        0x685c: (0x000b, None, None, 4, None, None, "AgingWaitUntilExpired"),
        0x685f: (0x0102, None, None, 4, None, None, "ActivityImmutableEntryId"),
        0x6870: (0x1102, None, None, 4, None, None, "DelegateEntryIds2"),
        0x6871: (0x1003, None, None, 4, None, None, "DelegateFlags2"),
        0x6873: (0x0102, None, None, 4, None, None, "InferenceTrainedModelVersionBreadCrumb"),
        0x6874: (0x001f, None, None, 4, None, None, "FolderPathFullName"),
        0x6875: (0x0102, None, None, 4, None, None, "ImmutableIdExport"),
        0x6876: (0x0102, None, None, 4, None, None, "ControlDataForTrendingAroundMeAssistant"),
        0x6877: (0x101f, None, None, 4, None, None, "RestrictionAnnotationWordBreakingTokens"),
        0x6878: (0x1003, None, None, 4, None, None, "RestrictionAnnotationWordBreakingTokenLengths"),
        0x6879: (0x0102, None, None, 4, None, None, "ControlDataForCalendarInsightsAssistant"),
        0x687a: (0x0102, None, None, 4, None, None, "ControlDataForFreeBusyPublishingTimeBasedAssistant"),
        0x687b: (0x0003, None, None, 4, None, None, "RestrictionAnnotationIndexPropertyTag"),
        0x687c: (0x000b, None, None, 4, None, None, "IsAbandonedMoveDestination"),
        0x687d: (0x0102, None, None, 4, None, None, "ImmutableId26Bytes"),
        0x687e: (0x0102, None, None, 4, None, None, "ImmutableIdSetIn"),
        0x687f: (0x0102, None, None, 4, None, None, "SearchFolderLargeRestriction"),
        0x689f: (0x0040, None, None, 4, None, None, "ConversationMsgSentTime"),
        0x68a4: (0x001f, None, None, 4, None, None, "PersonCompanyNameMailboxWide"),
        0x68a5: (0x001f, None, None, 4, None, None, "PersonDisplayNameMailboxWide"),
        0x68a6: (0x001f, None, None, 4, None, None, "PersonGivenNameMailboxWide"),
        0x68a7: (0x001f, None, None, 4, None, None, "PersonSurnameMailboxWide"),
        0x68a8: (0x0102, None, None, 4, None, None, "PersonPhotoContactEntryIdMailboxWide"),
        0x68b0: (0x001f, None, None, 4, None, None, "PersonFileAsMailboxWide"),
        0x68b1: (0x0003, None, None, 4, None, None, "PersonRelevanceScoreMailboxWide"),
        0x68b2: (0x000b, None, None, 4, None, None, "PersonIsDistributionListMailboxWide"),
        0x68b3: (0x001f, None, None, 4, None, None, "PersonHomeCityMailboxWide"),
        0x68b4: (0x0040, None, None, 4, None, None, "PersonCreationTimeMailboxWide"),
        0x68b7: (0x0048, None, None, 4, None, None, "PersonGALLinkIDMailboxWide"),
        0x68ba: (0x101f, None, None, 4, None, None, "PersonMvEmailAddressMailboxWide"),
        0x68bb: (0x101f, None, None, 4, None, None, "PersonMvEmailDisplayNameMailboxWide"),
        0x68bc: (0x101f, None, None, 4, None, None, "PersonMvEmailRoutingTypeMailboxWide"),
        0x68bd: (0x001f, None, None, 4, None, None, "PersonImAddressMailboxWide"),
        0x68be: (0x001f, None, None, 4, None, None, "PersonWorkCityMailboxWide"),
        0x68bf: (0x001f, None, None, 4, None, None, "PersonDisplayNameFirstLastMailboxWide"),
        0x68c0: (0x001f, None, None, 4, None, None, "PersonDisplayNameLastFirstMailboxWide"),
        0x68c2: (0x000b, None, None, 4, None, None, "ConversationHasClutter"),
        0x68c3: (0x000b, None, None, 4, None, None, "ConversationHasClutterMailboxWide"),
        0x68c4: (0x0102, None, None, 4, None, None, "ExchangeObjectId"),
        0x68c5: (0x0102, None, None, 4, None, None, "ViewLargeRestriction"),
        0x68c6: (0x0003, None, None, 4, None, None, "ClientDiagnosticLevel"),
        0x68c7: (0x0102, None, None, 4, None, None, "ClientDiagnosticData"),
        0x6900: (0x0003, None, None, 4, None, None, "ConversationLastMemberDocumentId"),
        0x6901: (0x001f, None, None, 4, None, None, "ConversationPreview"),
        0x6902: (0x0003, None, None, 4, None, None, "ConversationLastMemberDocumentIdMailboxWide"),
        0x6903: (0x0003, None, None, 4, None, None, "ConversationInitialMemberDocumentId"),
        0x6904: (0x1003, None, None, 4, None, None, "ConversationMemberDocumentIds"),
        0x6905: (0x0040, None, None, 4, None, None, "ConversationMessageDeliveryOrRenewTimeMailboxWide"),
        0x6907: (0x1002, None, None, 4, None, None, "ConversationMessageRichContentMailboxWide"),
        0x6908: (0x001f, None, None, 4, None, None, "ConversationPreviewMailboxWide"),
        0x6909: (0x0040, None, None, 4, None, None, "ConversationMessageDeliveryOrRenewTime"),
        0x690a: (0x001f, None, None, 4, None, None, "ConversationWorkingSetSourcePartition"),
        0x690b: (0x1003, None, None, 4, None, None, "ConversationSystemCategories"),
        0x690c: (0x1003, None, None, 4, None, None, "ConversationSystemCategoriesMailboxWide"),
        0x690d: (0x0003, None, None, 4, None, None, "ConversationExchangeApplicationFlagsMailboxWide"),
        0x690e: (0x101f, None, None, 4, None, None, "ConversationMvMentionsMailboxWide"),
        0x690f: (0x101f, None, None, 4, None, None, "ConversationMvMentions"),
        0x6910: (0x1102, None, None, 4, None, None, "ConversationMvThreadIds"),
        0x6911: (0x1102, None, None, 4, None, None, "ConversationMvThreadIdsMailboxWide"),
        0x6912: (0x0003, None, None, 4, None, None, "ConversationLikeCountMailboxWide"),
        0x6913: (0x0040, None, None, 4, None, None, "ConversationReturnTime"),
        0x6914: (0x0040, None, None, 4, None, None, "ConversationReturnTimeMailboxWide"),
        0x6915: (0x0003, None, None, 4, None, None, "UserActivityPayloadVersion"),
        0x6916: (0x000b, None, None, 4, None, None, "ConversationAtAllMention"),
        0x6917: (0x000b, None, None, 4, None, None, "ConversationAtAllMentionMailboxWide"),
        0x6918: (0x0003, None, None, 4, None, None, "ConversationInferenceClassification"),
        0x6919: (0x0003, None, None, 4, None, None, "ConversationCharm"),
        0x691a: (0x0003, None, None, 4, None, None, "ConversationCharmMailboxWide"),
        0x691b: (0x0003, None, None, 4, None, None, "SignalTypeId"),
        0x691c: (0x0048, None, None, 4, None, None, "SignalAppId"),
        0x691d: (0x001f, None, None, 4, None, None, "SignalActorId"),
        0x691e: (0x001f, None, None, 4, None, None, "SignalClientVersion"),
        0x691f: (0x0048, None, None, 4, None, None, "SignalAadTenantId"),
        0x6920: (0x0003, None, None, 4, None, None, "SignalActorIdType"),
        0x6921: (0x0003, None, None, 4, None, None, "SignalOS"),
        0x6922: (0x001f, None, None, 4, None, None, "SignalOSVersion"),
        0x6923: (0x0005, None, None, 4, None, None, "SignalLatitude"),
        0x6924: (0x001f, None, None, 4, None, None, "SignalCv"),
        0x6925: (0x001f, None, None, 4, None, None, "SignalClientIp"),
        0x6926: (0x001f, None, None, 4, None, None, "SignalUserAgent"),
        0x6927: (0x001f, None, None, 4, None, None, "SignalDeviceId"),
        0x6928: (0x001f, None, None, 4, None, None, "SignalSchemaVersion"),
        0x6929: (0x0003, None, None, 4, None, None, "SignalAppWorkload"),
        0x692a: (0x0003, None, None, 4, None, None, "SignalCompliance"),
        0x692b: (0x0003, None, None, 4, None, None, "SignalItemType"),
        0x692c: (0x001f, None, None, 4, None, None, "SignalContainerId"),
        0x692d: (0x001f, None, None, 4, None, None, "SignalContainerType"),
        0x692e: (0x0005, None, None, 4, None, None, "SignalLongitude"),
        0x6930: (0x0003, None, None, 4, None, None, "SignalLocationType"),
        0x6931: (0x0005, None, None, 4, None, None, "SignalPrecision"),
        0x6932: (0x0003, None, None, 4, None, None, "SignalLocaleId"),
        0x6933: (0x001f, None, None, 4, None, None, "SignalTargetItemId"),
        0x6935: (0x0040, None, None, 4, None, None, "SignalTimeStamp"),
        0x6938: (0x000b, None, None, 4, None, None, "SignalIsClient"),
        0x693a: (0x001f, None, None, 4, None, None, "ConversationSenderName"),
        0x693b: (0x001f, None, None, 4, None, None, "ConversationSenderNameMailboxWide"),
        0x693c: (0x001f, None, None, 4, None, None, "ConversationSenderSmtpAddress"),
        0x693d: (0x001f, None, None, 4, None, None, "ConversationSenderSmtpAddressMailboxWide"),
        0x693e: (0x0102, None, None, 4, None, None, "ConversationMemberCnSet"),
        0x693f: (0x0102, None, None, 4, None, None, "ConversationMemberCnSetMailboxWide"),
        0x6940: (0x0102, None, None, 4, None, None, "ConversationMemberImmutableIdSet"),
        0x6941: (0x0102, None, None, 4, None, None, "ConversationMemberImmutableIdSetMailboxWide"),
        0x6942: (0x0040, None, None, 4, None, None, "ConversationLastAttachmentsProcessedTime"),
        0x6943: (0x0040, None, None, 4, None, None, "ConversationLastAttachmentsProcessedTimeMailboxWide"),
        0x694a: (0x001f, None, None, 4, None, None, "SignalAppName"),
        0x694b: (0x0014, None, None, 4, None, None, "SignalTimeStampOffset"),
        0x6e01: (0x0003, None, None, 4, None, None, "SecurityFlags"),
        0x6e04: (0x000b, None, None, 4, None, None, "SecurityReceiptRequestProcessed"),
        0x7000: (0x0040, None, None, 4, None, None, "UserInformationInstanceCreationTime"),
        0x7018: (0x001f, None, None, 4, None, None, "RemoteFolderDisplayName"),
        0x7019: (0x0102, None, None, 4, None, None, "AssistantFilterResult"),
        0x701a: (0x0102, None, None, 4, None, None, "ControlDataForTimerBrokerAssistant"),
        0x701b: (0x000b, None, None, 4, None, None, "ContainsScheduledTimers"),
        0x701c: (0x0102, None, None, 4, None, None, "LastScheduledTimerChangeToken"),
        0x701d: (0x0102, None, None, 4, None, None, "ControlDataForBigFunnelStoreIndexAssistant"),
        0x701e: (0x0003, None, None, 4, None, None, "BigFunnelStoreIndexAssistantProcessedVersion"),
        0x701f: (0x0003, None, None, 4, None, None, "BigFunnelStoreIndexAssistantRequestedVersion"),
        0x7020: (0x0003, None, None, 4, None, None, "PeopleRelevanceMailEventCount"),
        0x7021: (0x0003, None, None, 4, None, None, "PeopleRelevanceCalendarEventCount"),
        0x7022: (0x0003, None, None, 4, None, None, "PeopleRelevanceContactEventCount"),
        0x7023: (0x0040, None, None, 4, None, None, "PeopleRelevanceLastSuccessfulRunTime"),
        0x702a: (0x0102, None, None, 4, None, None, "MailboxFeatureStorageProperty"),
        0x702b: (0x0040, None, None, 4, None, None, "LastUserActionTime"),
        0x7c00: (0x001f, None, None, 4, None, None, "FavoriteDisplayName"),
        0x7c02: (0x0102, None, None, 4, None, None, "FavPublicSourceKey"),
        0x7c03: (0x0102, None, None, 4, None, None, "SyncFolderSourceKey"),
        0x7c04: (0x0102, None, None, 4, None, None, "SyncFolderChangeKey"),
        0x7c09: (0x0102, None, None, 4, None, None, "UserConfigurationStream"),
        0x7c0a: (0x0102, None, None, 4, None, None, "SyncStateBlob"),
        0x7c0b: (0x001f, None, None, 4, None, None, "ReplyForwardStatus"),
        0x7c0c: (0x001f, None, None, 4, None, None, "PopImapPoisonMessageStamp"),
        0x7c14: (0x0102, None, None, 4, None, None, "ControlDataForSystemCleanupFolderAssistant"),
        0x7c1a: (0x0003, None, None, 4, None, None, "UserPhotoCacheId"),
        0x7c1b: (0x0003, None, None, 4, None, None, "UserPhotoPreviewCacheId"),
        0x7c1c: (0x0003, None, None, 4, None, None, "ProfileHeaderPhotoCacheId"),
        0x7c1d: (0x0003, None, None, 4, None, None, "ProfileHeaderPhotoPreviewCacheId"),
        0x7d03: (0x0003, None, None, 4, None, None, "FavLevelMask"),
        0x7d0e: (0x0014, None, None, 4, None, None, "ImmutableId"),
        0x7d0f: (0x0102, None, None, 4, None, None, "ControlDataForWeveMessageAssistant"),
        0x7d10: (0x0102, None, None, 4, None, None, "WeveMessageAssistantLastMessageSentTime"),
        0x7d11: (0x0102, None, None, 4, None, None, "WeveMessageAssistantLastNotificationMessageSentTime"),
        0x7d12: (0x0102, None, None, 4, None, None, "ControlDataForMailboxLifecycleAssistant"),
        0x7d13: (0x0102, None, None, 4, None, None, "WeveMessageAssistantLastLicenseCheckTime"),
        0x7d14: (0x000b, None, None, 4, None, None, "WeveMessageAssistantLicenseExists"),
        0x7d15: (0x0102, None, None, 4, None, None, "ReplacedImmutableIdBin"),
        0x7d16: (0x000b, None, None, 4, None, None, "IsCloudCacheCrawlingComplete"),
        0x7d17: (0x0003, None, None, 4, None, None, "CloudCacheItemSyncStatus"),
        0x7d18: (0x0102, None, None, 4, None, None, "ControlDataForRecordReviewAssistant"),
        0x7d19: (0x0040, None, None, 4, None, None, "LastRecordIdentifiedTime"),
        0x7d20: (0x0102, None, None, 4, None, None, "ControlDataForPeopleRelevanceMultiStepAssistant"),
        0x7d21: (0x0102, None, None, 4, None, None, "ControlDataForXrmSharingMaintenanceAssistant"),
        0x7d23: (0x0102, None, None, 4, None, None, "ItemAssistantCrawlVersionBlob"),
        0x7d24: (0x0102, None, None, 4, None, None, "ControlDataForDynamicTba0"),
        0x7d25: (0x0102, None, None, 4, None, None, "ControlDataForDynamicTba1"),
        0x7d26: (0x0102, None, None, 4, None, None, "ControlDataForDynamicTba2"),
        0x7d27: (0x0102, None, None, 4, None, None, "ControlDataForDynamicTba3"),
        0x7d28: (0x0102, None, None, 4, None, None, "ControlDataForDynamicTba4"),
        0x7d29: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba0"),
        0x7d2a: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba1"),
        0x7d2b: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba2"),
        0x7d2c: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba3"),
        0x7d2d: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba4"),
        0x7d2e: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba5"),
        0x7d2f: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba6"),
        0x7d30: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba7"),
        0x7d31: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba8"),
        0x7d32: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba9"),
        0x7d33: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba10"),
        0x7d34: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba11"),
        0x7d35: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba12"),
        0x7d36: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba13"),
        0x7d37: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba14"),
        0x7d38: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba15"),
        0x7d39: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba16"),
        0x7d3a: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba17"),
        0x7d3b: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba18"),
        0x7d3c: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba19"),
        0x7d3d: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba20"),
        0x7d3e: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba21"),
        0x7d3f: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba22"),
        0x7d40: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba23"),
        0x7d41: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba24"),
        0x7d42: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba25"),
        0x7d43: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba26"),
        0x7d44: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba27"),
        0x7d45: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba28"),
        0x7d46: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba29"),
        0x7d47: (0x0102, None, None, 4, None, None, "ControlDataForDynamicGriffinTba30"),
        0x7d48: (0x0102, None, None, 4, None, None, "ControlDataForContentClassificationAssistant"),
        0x7d49: (0x0102, None, None, 4, None, None, "ControlDataForOfficeGraphSecondaryCopyQuotaTimeBasedAssistant"),
        0x7d4a: (0x0102, None, None, 4, None, None, "ControlDataForOfficeGraphConvertFilesToWorkingSetAndSpoolsSubFoldersTimeBasedAssistant"),
        0x7d4b: (0x0102, None, None, 4, None, None, "ControlDataForOfficeGraphSpoolsScaleOutTimeBasedAssistant"),
        0x7d7f: (0x0102, None, None, 4, None, None, "ControlDataForDynamicTba6"),
        0x7d80: (0x0003, None, None, 4, None, None, "MailboxTenantSizeEstimate"),
        0x7d81: (0x0102, None, None, 4, None, None, "ControlDataForMailboxTenantDataAssistant"),
        0x7ff7: (0x000b, None, None, 4, None, None, "IsATPEncrypted"),
        0x7ff8: (0x001f, None, None, 4, None, None, "HasDlpDetectedAttachmentClassifications"),
        0x8d0d: (0x001f, None, None, 4, None, None, "ExternalDirectoryObjectId"),
}
