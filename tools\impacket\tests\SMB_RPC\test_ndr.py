# Impacket - Collection of Python classes for working with network protocols.
#
# Copyright Fortra, LLC and its affiliated companies 
#
# All rights reserved.
#
# This software is provided under a slightly modified version
# of the Apache Software License. See the accompanying LICENSE file
# for more information.
#
from __future__ import division
from __future__ import print_function

import unittest

from impacket.dcerpc.v5.samr import SamrLookupNamesInDomainResponse, SamrLookupIdsInDomain
from impacket.dcerpc.v5.d<PERSON><PERSON>pi import DRSCrackNamesResponse,DRSDomainControllerInfoResponse,DRSGetNCChangesResponse
from impacket.winregistry import hexdump
from impacket.dcerpc.v5.lsat import LsarGetUserNameResponse, LsarLookupSids2Response
from impacket.dcerpc.v5.rrp import BaseRegEnumValueResponse, BaseRegGetKeySecurityResponse, BaseRegQueryMultipleValues, RVALENT, REG_SZ, BaseRegQueryValueResponse
from impacket.dcerpc.v5.scmr import RCreateServiceWResponse
from impacket.dcerpc.v5.dcomrt import ComplexPing
from impacket.dcerpc.v5 import dtypes
from impacket.dcerpc.v5.dtypes import NULL
from impacket.dcerpc.v5 import srvs
from impacket.dcerpc.v5 import epm
from impacket.dcerpc.v5.epm import ept_lookupResponse
from impacket.uuid import string_to_bin, uuidtup_to_bin


class NDRTests(unittest.TestCase):
    NDR64Syntax = uuidtup_to_bin(('71710533-BEBA-4937-8319-B5DBEF9CCC36', '1.0'))

    def test_1(self):
        #<class 'impacket.dcerpc.v5.drsuapi.DRSCrackNamesResponse'>
        crackNamesResponse = b'\x01\x00\x00\x00\x01\x00\x00\x00\x00\x00\x02\x00\x05\x00\x00\x00\x04\x00\x02\x00\x05\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x0c\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x10\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x14\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x18\x00\x02\x00q\x00\x00\x00\x00\x00\x00\x00q\x00\x00\x00C\x00N\x00=\x00N\x00T\x00D\x00S\x00 \x00S\x00e\x00t\x00t\x00i\x00n\x00g\x00s\x00,\x00C\x00N\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00,\x00C\x00N\x00=\x00S\x00e\x00r\x00v\x00e\x00r\x00s\x00,\x00C\x00N\x00=\x00D\x00e\x00f\x00a\x00u\x00l\x00t\x00-\x00F\x00i\x00r\x00s\x00t\x00-\x00S\x00i\x00t\x00e\x00-\x00N\x00a\x00m\x00e\x00,\x00C\x00N\x00=\x00S\x00i\x00t\x00e\x00s\x00,\x00C\x00N\x00=\x00C\x00o\x00n\x00f\x00i\x00g\x00u\x00r\x00a\x00t\x00i\x00o\x00n\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\xab\xabq\x00\x00\x00\x00\x00\x00\x00q\x00\x00\x00C\x00N\x00=\x00N\x00T\x00D\x00S\x00 \x00S\x00e\x00t\x00t\x00i\x00n\x00g\x00s\x00,\x00C\x00N\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00,\x00C\x00N\x00=\x00S\x00e\x00r\x00v\x00e\x00r\x00s\x00,\x00C\x00N\x00=\x00D\x00e\x00f\x00a\x00u\x00l\x00t\x00-\x00F\x00i\x00r\x00s\x00t\x00-\x00S\x00i\x00t\x00e\x00-\x00N\x00a\x00m\x00e\x00,\x00C\x00N\x00=\x00S\x00i\x00t\x00e\x00s\x00,\x00C\x00N\x00=\x00C\x00o\x00n\x00f\x00i\x00g\x00u\x00r\x00a\x00t\x00i\x00o\x00n\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\xab\xabq\x00\x00\x00\x00\x00\x00\x00q\x00\x00\x00C\x00N\x00=\x00N\x00T\x00D\x00S\x00 \x00S\x00e\x00t\x00t\x00i\x00n\x00g\x00s\x00,\x00C\x00N\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00,\x00C\x00N\x00=\x00S\x00e\x00r\x00v\x00e\x00r\x00s\x00,\x00C\x00N\x00=\x00D\x00e\x00f\x00a\x00u\x00l\x00t\x00-\x00F\x00i\x00r\x00s\x00t\x00-\x00S\x00i\x00t\x00e\x00-\x00N\x00a\x00m\x00e\x00,\x00C\x00N\x00=\x00S\x00i\x00t\x00e\x00s\x00,\x00C\x00N\x00=\x00C\x00o\x00n\x00f\x00i\x00g\x00u\x00r\x00a\x00t\x00i\x00o\x00n\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\xab\xabq\x00\x00\x00\x00\x00\x00\x00q\x00\x00\x00C\x00N\x00=\x00N\x00T\x00D\x00S\x00 \x00S\x00e\x00t\x00t\x00i\x00n\x00g\x00s\x00,\x00C\x00N\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00,\x00C\x00N\x00=\x00S\x00e\x00r\x00v\x00e\x00r\x00s\x00,\x00C\x00N\x00=\x00D\x00e\x00f\x00a\x00u\x00l\x00t\x00-\x00F\x00i\x00r\x00s\x00t\x00-\x00S\x00i\x00t\x00e\x00-\x00N\x00a\x00m\x00e\x00,\x00C\x00N\x00=\x00S\x00i\x00t\x00e\x00s\x00,\x00C\x00N\x00=\x00C\x00o\x00n\x00f\x00i\x00g\x00u\x00r\x00a\x00t\x00i\x00o\x00n\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\xab\xabq\x00\x00\x00\x00\x00\x00\x00q\x00\x00\x00C\x00N\x00=\x00N\x00T\x00D\x00S\x00 \x00S\x00e\x00t\x00t\x00i\x00n\x00g\x00s\x00,\x00C\x00N\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00,\x00C\x00N\x00=\x00S\x00e\x00r\x00v\x00e\x00r\x00s\x00,\x00C\x00N\x00=\x00D\x00e\x00f\x00a\x00u\x00l\x00t\x00-\x00F\x00i\x00r\x00s\x00t\x00-\x00S\x00i\x00t\x00e\x00-\x00N\x00a\x00m\x00e\x00,\x00C\x00N\x00=\x00S\x00i\x00t\x00e\x00s\x00,\x00C\x00N\x00=\x00C\x00o\x00n\x00f\x00i\x00g\x00u\x00r\x00a\x00t\x00i\x00o\x00n\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\xbf\xbf\x00\x00\x00\x00'
        crackNames = DRSCrackNamesResponse()
        crackNames.fromString(crackNamesResponse)
        crackNames.dump()
        output = crackNames.getData()
        print("ORIG: %d, REPACKED: %d" % (len(crackNamesResponse), len(output)))
        print("="*80)
        print("ORIG")
        #hexdump(crackNamesResponse)
        #hexdump(output)
        self.assertEqual(crackNamesResponse, output)
        #print repr(output)

    def test_2(self):
        #<class 'impacket.dcerpc.v5.drsuapi.DRSDomainControllerInfoResponse'>
        domainControllerInfoResponse = b'\x02\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x00\x00\x02\x00\x01\x00\x00\x00\x04\x00\x02\x00\x08\x00\x02\x00\x0c\x00\x02\x00\x10\x00\x02\x00\x14\x00\x02\x00\x18\x00\x02\x00\x1c\x00\x02\x00\x01\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00\x00\xe6)\x8fL>\xfdXK\x88\xf26\xc1s\x08\xa2\xa1EG\x1c\xc0r\x17$H\xb6Y:\x1b\x10\x9dV\x0bz|z\x17\x08\xe4U@\xae\xb8\xcc\xc4<\xa4\xd3\x18\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x0b\x00\x00\x00\x00\x00\x00\x00\x0b\x00\x00\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00\x00\x00\xab\xab\x17\x00\x00\x00\x00\x00\x00\x00\x17\x00\x00\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00.\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00.\x00N\x00E\x00T\x00\x00\x00\xab\xab\x18\x00\x00\x00\x00\x00\x00\x00\x18\x00\x00\x00D\x00e\x00f\x00a\x00u\x00l\x00t\x00-\x00F\x00i\x00r\x00s\x00t\x00-\x00S\x00i\x00t\x00e\x00-\x00N\x00a\x00m\x00e\x00\x00\x00G\x00\x00\x00\x00\x00\x00\x00G\x00\x00\x00C\x00N\x00=\x00D\x00e\x00f\x00a\x00u\x00l\x00t\x00-\x00F\x00i\x00r\x00s\x00t\x00-\x00S\x00i\x00t\x00e\x00-\x00N\x00a\x00m\x00e\x00,\x00C\x00N\x00=\x00S\x00i\x00t\x00e\x00s\x00,\x00C\x00N\x00=\x00C\x00o\x00n\x00f\x00i\x00g\x00u\x00r\x00a\x00t\x00i\x00o\x00n\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\xab\xab6\x00\x00\x00\x00\x00\x00\x006\x00\x00\x00C\x00N\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00,\x00O\x00U\x00=\x00D\x00o\x00m\x00a\x00i\x00n\x00 \x00C\x00o\x00n\x00t\x00r\x00o\x00l\x00l\x00e\x00r\x00s\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00`\x00\x00\x00\x00\x00\x00\x00`\x00\x00\x00C\x00N\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00,\x00C\x00N\x00=\x00S\x00e\x00r\x00v\x00e\x00r\x00s\x00,\x00C\x00N\x00=\x00D\x00e\x00f\x00a\x00u\x00l\x00t\x00-\x00F\x00i\x00r\x00s\x00t\x00-\x00S\x00i\x00t\x00e\x00-\x00N\x00a\x00m\x00e\x00,\x00C\x00N\x00=\x00S\x00i\x00t\x00e\x00s\x00,\x00C\x00N\x00=\x00C\x00o\x00n\x00f\x00i\x00g\x00u\x00r\x00a\x00t\x00i\x00o\x00n\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00q\x00\x00\x00\x00\x00\x00\x00q\x00\x00\x00C\x00N\x00=\x00N\x00T\x00D\x00S\x00 \x00S\x00e\x00t\x00t\x00i\x00n\x00g\x00s\x00,\x00C\x00N\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00,\x00C\x00N\x00=\x00S\x00e\x00r\x00v\x00e\x00r\x00s\x00,\x00C\x00N\x00=\x00D\x00e\x00f\x00a\x00u\x00l\x00t\x00-\x00F\x00i\x00r\x00s\x00t\x00-\x00S\x00i\x00t\x00e\x00-\x00N\x00a\x00m\x00e\x00,\x00C\x00N\x00=\x00S\x00i\x00t\x00e\x00s\x00,\x00C\x00N\x00=\x00C\x00o\x00n\x00f\x00i\x00g\x00u\x00r\x00a\x00t\x00i\x00o\x00n\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\xbf\xbf\x00\x00\x00\x00'
        domainController = DRSDomainControllerInfoResponse()
        domainController.fromString(domainControllerInfoResponse)
        domainController.dump()
        output = domainController.getData()
        print("ORIG: %d, REPACKED: %d" % (len(domainControllerInfoResponse), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(domainControllerInfoResponse)
        hexdump(output)
        #print "ORIG: %d, REPACKED: %d" % (len(domainControllerInfoResponse), len(output))
        self.assertEqual(domainControllerInfoResponse, output)

    def test_3(self):
        #<class 'impacket.dcerpc.v5.drsuapi.DRSGetNCChangesResponse'>
        getNCChangesResponse = b'\x06\x00\x00\x00\x06\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x00\x00\x02\x00\xab\xab\xab\xab\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\'\x00\x00\x00\x04\x00\x02\x00\x01\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xa4\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x12\x00\x00\x00\\\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x11\x00\x00\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\'\x00\x00\x00\x00\x00\x00\x00\x02\x00\x00\x00\x08\x00\x02\x00\x01\x00\x00\x00\x02\x00\x00\x00\x0c\x00\x02\x00\x02\x00\x00\x00\x08\x00\x00\x00\x10\x00\x02\x00\x03\x00\x00\x00\x08\x00\x00\x00\x14\x00\x02\x00\x04\x00\x00\x00\x08\x00\x00\x00\x18\x00\x02\x00\x05\x00\x00\x00\x08\x00\x00\x00\x1c\x00\x02\x00\x06\x00\x00\x00\x08\x00\x00\x00 \x00\x02\x00\x07\x00\x00\x00\x08\x00\x00\x00$\x00\x02\x00\x08\x00\x00\x00\x02\x00\x00\x00(\x00\x02\x00\t\x00\x00\x00\x08\x00\x00\x00,\x00\x02\x00\n\x00\x00\x00\x08\x00\x00\x000\x00\x02\x00\x13\x00\x00\x00\x08\x00\x00\x004\x00\x02\x00\x14\x00\x00\x00\x08\x00\x00\x008\x00\x02\x00\x15\x00\x00\x00\t\x00\x00\x00<\x00\x02\x00\x16\x00\x00\x00\t\x00\x00\x00@\x00\x02\x00\x17\x00\x00\x00\n\x00\x00\x00D\x00\x02\x00\x18\x00\x00\x00\x02\x00\x00\x00H\x00\x02\x00\x19\x00\x00\x00\x02\x00\x00\x00L\x00\x02\x00\x1a\x00\x00\x00\x02\x00\x00\x00P\x00\x02\x00\x0b\x00\x00\x00\n\x00\x00\x00T\x00\x02\x00\x0c\x00\x00\x00\t\x00\x00\x00X\x00\x02\x00\r\x00\x00\x00\n\x00\x00\x00\\\x00\x02\x00\x0e\x00\x00\x00\t\x00\x00\x00`\x00\x02\x00\x0f\x00\x00\x00\n\x00\x00\x00d\x00\x02\x00\x10\x00\x00\x00\t\x00\x00\x00h\x00\x02\x00\x11\x00\x00\x00\t\x00\x00\x00l\x00\x02\x00\x12\x00\x00\x00\n\x00\x00\x00p\x00\x02\x00\x1b\x00\x00\x00\t\x00\x00\x00t\x00\x02\x00\x1c\x00\x00\x00\t\x00\x00\x00x\x00\x02\x00\x1d\x00\x00\x00\x08\x00\x00\x00|\x00\x02\x00\x1e\x00\x00\x00\x08\x00\x00\x00\x80\x00\x02\x00\x1f\x00\x00\x00\t\x00\x00\x00\x84\x00\x02\x00 \x00\x00\x00\t\x00\x00\x00\x88\x00\x02\x00!\x00\x00\x00\n\x00\x00\x00\x8c\x00\x02\x00"\x00\x00\x00\n\x00\x00\x00\x90\x00\x02\x00#\x00\x00\x00\n\x00\x00\x00\x94\x00\x02\x00$\x00\x00\x00\n\x00\x00\x00\x98\x00\x02\x00%\x00\x00\x00\x06\x00\x00\x00\x9c\x00\x02\x00&\x00\x00\x00\x06\x00\x00\x00\xa0\x00\x02\x00\x02\x00\x00\x00U\x04\xca\xca\x02\x00\x00\x00U\x06\xca\xca\x08\x00\x00\x00*\x86H\x86\xf7\x14\x01\x02\x08\x00\x00\x00*\x86H\x86\xf7\x14\x01\x03\x08\x00\x00\x00`\x86H\x01e\x02\x02\x01\x08\x00\x00\x00`\x86H\x01e\x02\x02\x03\x08\x00\x00\x00`\x86H\x01e\x02\x01\x05\x08\x00\x00\x00`\x86H\x01e\x02\x01\x04\x02\x00\x00\x00U\x05\xca\xca\x08\x00\x00\x00*\x86H\x86\xf7\x14\x01\x04\x08\x00\x00\x00*\x86H\x86\xf7\x14\x01\x05\x08\x00\x00\x00\t\x92&\x89\x93\xf2,d\x08\x00\x00\x00`\x86H\x01\x86\xf8B\x03\t\x00\x00\x00\t\x92&\x89\x93\xf2,d\x01\xca\xca\xca\t\x00\x00\x00`\x86H\x01\x86\xf8B\x03\x01\xca\xca\xca\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x05\xb6X\xca\xca\x02\x00\x00\x00U\x15\xca\xca\x02\x00\x00\x00U\x12\xca\xca\x02\x00\x00\x00U\x14\xca\xca\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x04\x82\x04\xca\xca\t\x00\x00\x00*\x86H\x86\xf7\x14\x01\x058\xca\xca\xca\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x04\x82\x06\xca\xca\t\x00\x00\x00*\x86H\x86\xf7\x14\x01\x059\xca\xca\xca\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x04\x82\x07\xca\xca\t\x00\x00\x00*\x86H\x86\xf7\x14\x01\x05:\xca\xca\xca\t\x00\x00\x00*\x86H\x86\xf7\x14\x01\x05I\xca\xca\xca\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x04\x821\xca\xca\t\x00\x00\x00+\x06\x01\x04\x01\x8b:ew\xca\xca\xca\t\x00\x00\x00`\x86H\x01\x86\xf8B\x03\x02\xca\xca\xca\x08\x00\x00\x00+\x06\x01\x04\x01\x81z\x01\x08\x00\x00\x00*\x86H\x86\xf7\r\x01\t\t\x00\x00\x00\t\x92&\x89\x93\xf2,d\x04\xca\xca\xca\t\x00\x00\x00*\x86H\x86\xf7\x14\x01\x06\x17\xca\xca\xca\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x06\x12\x01\xca\xca\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x06\x12\x02\xca\xca\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x06\r\x03\xca\xca\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x06\r\x04\xca\xca\x06\x00\x00\x00+\x06\x01\x01\x01\x01\xca\xca\x06\x00\x00\x00+\x06\x01\x01\x01\x02\xab\xab\x00\x00\x00\x00\xa8\x00\x02\x00\x01\x00\x00\x00\x02\x00\x00\x00\xac\x00\x02\x00\x01\x00\x00\x00\x00\x00\x00\x00\xc0\x00\x02\x00\x12\x00\x00\x00\\\x00\x00\x00\x18\x00\x00\x00\xbb\xd9xM\x8c\xa3lI\xb5\xd3a\xde\x92\xcb\xaa\x0c\x01\x04\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\x98\xb7\xba\xeb^\xc4g\x7fy2s\xab\x00\x00\x00\x00\x11\x00\x00\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x02\x00\x00\x00\x01\x00\x02\x00\x01\x00\x00\x00\xb0\x00\x02\x00\x92\x00\t\x00\x01\x00\x00\x00\xb8\x00\x02\x00\x01\x00\x00\x00\x04\x00\x00\x00\xb4\x00\x02\x00\x04\x00\x00\x00\x05\x00\x00\x00\x01\x00\x00\x00\x18\x00\x00\x00\xbc\x00\x02\x00\x18\x00\x00\x00\x01\x04\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\x98\xb7\xba\xeb^\xc4g\x7fy2s\xab\x02\x00\x00\x00\x02\x00\x00\x00\xab\xab\xab\xab\x01\x00\x00\x00\xbf\xbf\xbf\xbf\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\xbf\xbf\xbf\xbf\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x04\x10\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
        ncChanges = DRSGetNCChangesResponse()
        ncChanges.fromString(getNCChangesResponse)
        ncChanges.dump()
        output = ncChanges.getData()
        print(repr(output))
        print("ORIG: %d, REPACKED: %d" % (len(getNCChangesResponse), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(getNCChangesResponse)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(len(getNCChangesResponse), len(output))

    def test_4(self):
        #<class 'impacket.dcerpc.v5.drsuapi.DRSGetNCChangesResponse'>
        getNCChangesResponse = b'\x06\x00\x00\x00\x06\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x00\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\'\x00\x00\x00\x04\x00\x02\x00\x01\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xa4\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x12\x00\x00\x00\\\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x11\x00\x00\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\'\x00\x00\x00\x00\x00\x00\x00\x02\x00\x00\x00\x08\x00\x02\x00\x01\x00\x00\x00\x02\x00\x00\x00\x0c\x00\x02\x00\x02\x00\x00\x00\x08\x00\x00\x00\x10\x00\x02\x00\x03\x00\x00\x00\x08\x00\x00\x00\x14\x00\x02\x00\x04\x00\x00\x00\x08\x00\x00\x00\x18\x00\x02\x00\x05\x00\x00\x00\x08\x00\x00\x00\x1c\x00\x02\x00\x06\x00\x00\x00\x08\x00\x00\x00 \x00\x02\x00\x07\x00\x00\x00\x08\x00\x00\x00$\x00\x02\x00\x08\x00\x00\x00\x02\x00\x00\x00(\x00\x02\x00\t\x00\x00\x00\x08\x00\x00\x00,\x00\x02\x00\n\x00\x00\x00\x08\x00\x00\x000\x00\x02\x00\x13\x00\x00\x00\x08\x00\x00\x004\x00\x02\x00\x14\x00\x00\x00\x08\x00\x00\x008\x00\x02\x00\x15\x00\x00\x00\t\x00\x00\x00<\x00\x02\x00\x16\x00\x00\x00\t\x00\x00\x00@\x00\x02\x00\x17\x00\x00\x00\n\x00\x00\x00D\x00\x02\x00\x18\x00\x00\x00\x02\x00\x00\x00H\x00\x02\x00\x19\x00\x00\x00\x02\x00\x00\x00L\x00\x02\x00\x1a\x00\x00\x00\x02\x00\x00\x00P\x00\x02\x00\x0b\x00\x00\x00\n\x00\x00\x00T\x00\x02\x00\x0c\x00\x00\x00\t\x00\x00\x00X\x00\x02\x00\r\x00\x00\x00\n\x00\x00\x00\\\x00\x02\x00\x0e\x00\x00\x00\t\x00\x00\x00`\x00\x02\x00\x0f\x00\x00\x00\n\x00\x00\x00d\x00\x02\x00\x10\x00\x00\x00\t\x00\x00\x00h\x00\x02\x00\x11\x00\x00\x00\t\x00\x00\x00l\x00\x02\x00\x12\x00\x00\x00\n\x00\x00\x00p\x00\x02\x00\x1b\x00\x00\x00\t\x00\x00\x00t\x00\x02\x00\x1c\x00\x00\x00\t\x00\x00\x00x\x00\x02\x00\x1d\x00\x00\x00\x08\x00\x00\x00|\x00\x02\x00\x1e\x00\x00\x00\x08\x00\x00\x00\x80\x00\x02\x00\x1f\x00\x00\x00\t\x00\x00\x00\x84\x00\x02\x00 \x00\x00\x00\t\x00\x00\x00\x88\x00\x02\x00!\x00\x00\x00\n\x00\x00\x00\x8c\x00\x02\x00"\x00\x00\x00\n\x00\x00\x00\x90\x00\x02\x00#\x00\x00\x00\n\x00\x00\x00\x94\x00\x02\x00$\x00\x00\x00\n\x00\x00\x00\x98\x00\x02\x00%\x00\x00\x00\x06\x00\x00\x00\x9c\x00\x02\x00&\x00\x00\x00\x06\x00\x00\x00\xa0\x00\x02\x00\x02\x00\x00\x00U\x04\x00\x00\x02\x00\x00\x00U\x06\x00\x00\x08\x00\x00\x00*\x86H\x86\xf7\x14\x01\x02\x08\x00\x00\x00*\x86H\x86\xf7\x14\x01\x03\x08\x00\x00\x00`\x86H\x01e\x02\x02\x01\x08\x00\x00\x00`\x86H\x01e\x02\x02\x03\x08\x00\x00\x00`\x86H\x01e\x02\x01\x05\x08\x00\x00\x00`\x86H\x01e\x02\x01\x04\x02\x00\x00\x00U\x05\x00\x00\x08\x00\x00\x00*\x86H\x86\xf7\x14\x01\x04\x08\x00\x00\x00*\x86H\x86\xf7\x14\x01\x05\x08\x00\x00\x00\t\x92&\x89\x93\xf2,d\x08\x00\x00\x00`\x86H\x01\x86\xf8B\x03\t\x00\x00\x00\t\x92&\x89\x93\xf2,d\x01\x00\x00\x00\t\x00\x00\x00`\x86H\x01\x86\xf8B\x03\x01\x00\x00\x00\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x05\xb6X\x00\x00\x02\x00\x00\x00U\x15\x00\x00\x02\x00\x00\x00U\x12\x00\x00\x02\x00\x00\x00U\x14\x00\x00\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x04\x82\x04\x00\x00\t\x00\x00\x00*\x86H\x86\xf7\x14\x01\x058\x00\x00\x00\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x04\x82\x06\x00\x00\t\x00\x00\x00*\x86H\x86\xf7\x14\x01\x059\x00\x00\x00\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x04\x82\x07\x00\x00\t\x00\x00\x00*\x86H\x86\xf7\x14\x01\x05:\x00\x00\x00\t\x00\x00\x00*\x86H\x86\xf7\x14\x01\x05I\x00\x00\x00\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x04\x821\x00\x00\t\x00\x00\x00+\x06\x01\x04\x01\x8b:ew\x00\x00\x00\t\x00\x00\x00`\x86H\x01\x86\xf8B\x03\x02\x00\x00\x00\x08\x00\x00\x00+\x06\x01\x04\x01\x81z\x01\x08\x00\x00\x00*\x86H\x86\xf7\r\x01\t\t\x00\x00\x00\t\x92&\x89\x93\xf2,d\x04\x00\x00\x00\t\x00\x00\x00*\x86H\x86\xf7\x14\x01\x06\x17\x00\x00\x00\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x06\x12\x01\x00\x00\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x06\x12\x02\x00\x00\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x06\r\x03\x00\x00\n\x00\x00\x00*\x86H\x86\xf7\x14\x01\x06\r\x04\x00\x00\x06\x00\x00\x00+\x06\x01\x01\x01\x01\x00\x00\x06\x00\x00\x00+\x06\x01\x01\x01\x02\x00\x00\x00\x00\x00\x00\xa8\x00\x02\x00\x01\x00\x00\x00%\x00\x00\x00\xac\x00\x02\x00\x01\x00\x00\x00\x00\x00\x00\x00\xf8\x01\x02\x00\x12\x00\x00\x00\\\x00\x00\x00\x18\x00\x00\x00\xbb\xd9xM\x8c\xa3lI\xb5\xd3a\xde\x92\xcb\xaa\x0c\x01\x04\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\x98\xb7\xba\xeb^\xc4g\x7fy2s\xab\x00\x00\x00\x00\x11\x00\x00\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00%\x00\x00\x00\x00\x00\x00\x00\x03\x00\x00\x00\xb0\x00\x02\x00\x01\x00\x02\x00\x01\x00\x00\x00\xc0\x00\x02\x00\x02\x00\x02\x00\x01\x00\x00\x00\xc8\x00\x02\x00J\x00\x02\x00\x01\x00\x00\x00\xd0\x00\x02\x00\x19\x01\x02\x00\x01\x00\x00\x00\xd8\x00\x02\x00\x01\x00\t\x00\x01\x00\x00\x00\xe0\x00\x02\x00\x1a\x00\t\x00\x01\x00\x00\x00\xe8\x00\x02\x00\'\x00\t\x00\x01\x00\x00\x00\xf0\x00\x02\x00<\x00\t\x00\x01\x00\x00\x00\xf8\x00\x02\x00=\x00\t\x00\x01\x00\x00\x00\x00\x01\x02\x00I\x00\t\x00\x01\x00\x00\x00\x08\x01\x02\x00J\x00\t\x00\x01\x00\x00\x00\x10\x01\x02\x00N\x00\t\x00\x01\x00\x00\x00\x18\x01\x02\x00O\x00\t\x00\x01\x00\x00\x00 \x01\x02\x00Q\x00\t\x00\x01\x00\x00\x00(\x01\x02\x00X\x00\t\x00\x01\x00\x00\x000\x01\x02\x00]\x00\t\x00\x01\x00\x00\x008\x01\x02\x00_\x00\t\x00\x01\x00\x00\x00@\x01\x02\x00\x92\x00\t\x00\x01\x00\x00\x00H\x01\x02\x00\x97\x00\t\x00\x00\x00\x00\x00\x00\x00\x00\x00\x9b\x00\t\x00\x01\x00\x00\x00P\x01\x02\x00\x9e\x00\t\x00\x00\x00\x00\x00\x00\x00\x00\x00\xca\x00\t\x00\x01\x00\x00\x00X\x01\x02\x00e\x01\t\x00\x01\x00\x00\x00`\x01\x02\x00p\x01\t\x00\x01\x00\x00\x00h\x01\x02\x00q\x01\t\x00\x01\x00\x00\x00p\x01\x02\x00w\x01\t\x00\x01\x00\x00\x00x\x01\x02\x00j\x02\t\x00\x0b\x00\x00\x00\x80\x01\x02\x00\x0e\x03\t\x00\x01\x00\x00\x00\xb0\x01\x02\x00d\x03\t\x00\x01\x00\x00\x00\xb8\x01\x02\x00{\x03\t\x00\x01\x00\x00\x00\xc0\x01\x02\x00O\x05\t\x00\x01\x00\x00\x00\xc8\x01\x02\x00\x83\x05\t\x00\x01\x00\x00\x00\xd0\x01\x02\x00\xb3\x05\t\x00\x01\x00\x00\x00\xd8\x01\x02\x00\xfc\x06\t\x00\x01\x00\x00\x00\xe0\x01\x02\x00\xfd\x06\t\x00\x01\x00\x00\x00\xe8\x01\x02\x00\xfe\x06\t\x00\x01\x00\x00\x00\xf0\x01\x02\x00\x03\x00\x00\x00\x04\x00\x00\x00\xb4\x00\x02\x00\x04\x00\x00\x00\xb8\x00\x02\x00\x04\x00\x00\x00\xbc\x00\x02\x00\x04\x00\x00\x00C\x00\n\x00\x04\x00\x00\x00B\x00\n\x00\x04\x00\x00\x00\x00\x00\x01\x00\x01\x00\x00\x00\x04\x00\x00\x00\xc4\x00\x02\x00\x04\x00\x00\x00\x05\x00\x00\x00\x01\x00\x00\x00\x08\x00\x00\x00\xcc\x00\x02\x00\x08\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\x01\x00\x00\x00(\x00\x00\x00\xd4\x00\x02\x00(\x00\x00\x00\x01\x00\x00\x00(\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x01\x00\x00\x00\xf4\x08\x00\x00\xdc\x00\x02\x00\xf4\x08\x00\x00\x01\x00\x14\x80\xd4\x08\x00\x00\xe4\x08\x00\x00\x14\x00\x00\x00\xdc\x00\x00\x00\x04\x00\xc8\x00\x05\x00\x00\x00\x02@\x14\x00 \x00\x0c\x00\x01\x01\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x02@\x18\x00\x00\x01\x00\x00\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x02@$\x00\x00\x01\x00\x00\x01\x05\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\x98\xb7\xba\xeb^\xc4g\x7fy2s\xab\x01\x02\x00\x00\x07B8\x00 \x00\x00\x00\x03\x00\x00\x00\xbe;\x0e\xf3\xf0\x9f\xd1\x11\xb6\x03\x00\x00\xf8\x03g\xc1\xa5z\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x01\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x07B8\x00 \x00\x00\x00\x03\x00\x00\x00\xbf;\x0e\xf3\xf0\x9f\xd1\x11\xb6\x03\x00\x00\xf8\x03g\xc1\xa5z\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x01\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x04\x00\xf8\x07.\x00\x00\x00\x05\x008\x00\x00\x01\x00\x00\x01\x00\x00\x00\xaa\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x05\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\x98\xb7\xba\xeb^\xc4g\x7fy2s\xab\xf2\x01\x00\x00\x00\x00\x14\x00\x10\x00\x00\x00\x01\x01\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x05\x00(\x00\x00\x01\x00\x00\x01\x00\x00\x00\xaa\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x01\x00\x00\x00\x00\x00\x05\t\x00\x00\x00\x05\x00(\x00\x00\x01\x00\x00\x01\x00\x00\x00\xab\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x01\x00\x00\x00\x00\x00\x05\t\x00\x00\x00\x05\x00(\x00\x00\x01\x00\x00\x01\x00\x00\x00\xac\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x01\x00\x00\x00\x00\x00\x05\t\x00\x00\x00\x05\x00,\x00\x00\x01\x00\x00\x01\x00\x00\x00\xaa\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x05\x00,\x00\x00\x01\x00\x00\x01\x00\x00\x00\xab\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x05\x00,\x00\x00\x01\x00\x00\x01\x00\x00\x00\xac\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x00\x00\x14\x00\x94\x00\x02\x00\x01\x01\x00\x00\x00\x00\x00\x05\x0b\x00\x00\x00\x00\x00$\x00\xbd\x01\x0e\x00\x01\x05\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\x98\xb7\xba\xeb^\xc4g\x7fy2s\xab\x00\x02\x00\x00\x00\x02\x18\x00\xbd\x01\x0f\x00\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x00\x00\x14\x00\xff\x01\x0f\x00\x01\x01\x00\x00\x00\x00\x00\x05\x12\x00\x00\x00\x00\x02$\x00\xff\x01\x0f\x00\x01\x05\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\x98\xb7\xba\xeb^\xc4g\x7fy2s\xab\x07\x02\x00\x00\x00\x02\x18\x00\x04\x00\x00\x00\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n<\x00\x10\x00\x00\x00\x03\x00\x00\x00\xf8\x88p\x03\xe1\n\xd2\x11\xb4"\x00\xa0\xc9h\xf99\xbaz\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n<\x00\x10\x00\x00\x00\x03\x00\x00\x00B/\xbaY\xa2y\xd0\x11\x90 \x00\xc0O\xc2\xd3\xcf\xbaz\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n<\x00\x10\x00\x00\x00\x03\x00\x00\x00@\xc2\n\xbc\xa9y\xd0\x11\x90 \x00\xc0O\xc2\xd4\xcf\xbaz\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n<\x00\x10\x00\x00\x00\x03\x00\x00\x00\x00B\x16L\xc0 \xd0\x11\xa7h\x00\xaa\x00n\x05)\xbaz\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n<\x00\x10\x00\x00\x00\x03\x00\x00\x00\x10  _\xa5y\xd0\x11\x90 \x00\xc0O\xc2\xd4\xcf\xbaz\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\x00,\x00\x10\x00\x00\x00\x01\x00\x00\x00`s@\xc7\xbf \xd0\x11\xa7h\x00\xaa\x00n\x05)\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n,\x00\x94\x00\x02\x00\x02\x00\x00\x00\x9cz\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x00\x00\x18\x00\x10\x00\x02\x00\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n,\x00\x94\x00\x02\x00\x02\x00\x00\x00\xbaz\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x00\x00\x14\x00\x94\x00\x02\x00\x01\x01\x00\x00\x00\x00\x00\x05\t\x00\x00\x00\x05\n<\x00\x10\x00\x00\x00\x03\x00\x00\x00\xf8\x88p\x03\xe1\n\xd2\x11\xb4"\x00\xa0\xc9h\xf99\x14\xcc(H7\x14\xbcE\x9b\x07\xado\x01^_(\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n<\x00\x10\x00\x00\x00\x03\x00\x00\x00B/\xbaY\xa2y\xd0\x11\x90 \x00\xc0O\xc2\xd3\xcf\x14\xcc(H7\x14\xbcE\x9b\x07\xado\x01^_(\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n<\x00\x10\x00\x00\x00\x03\x00\x00\x00@\xc2\n\xbc\xa9y\xd0\x11\x90 \x00\xc0O\xc2\xd4\xcf\x14\xcc(H7\x14\xbcE\x9b\x07\xado\x01^_(\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n<\x00\x10\x00\x00\x00\x03\x00\x00\x00\x00B\x16L\xc0 \xd0\x11\xa7h\x00\xaa\x00n\x05)\x14\xcc(H7\x14\xbcE\x9b\x07\xado\x01^_(\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n<\x00\x10\x00\x00\x00\x03\x00\x00\x00\x10  _\xa5y\xd0\x11\x90 \x00\xc0O\xc2\xd4\xcf\x14\xcc(H7\x14\xbcE\x9b\x07\xado\x01^_(\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\n,\x00\x94\x00\x02\x00\x02\x00\x00\x00\x14\xcc(H7\x14\xbcE\x9b\x07\xado\x01^_(\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\x00,\x00\x10\x00\x00\x00\x01\x00\x00\x00\xd0\x9f\x11\xb8\xf6\x04bG\xabzI\x86\xc7k?\x9a\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00*\x02\x00\x00\x05\x00(\x00\x10\x00\x00\x00\x01\x00\x00\x00\xd0\x9f\x11\xb8\xf6\x04bG\xabzI\x86\xc7k?\x9a\x01\x01\x00\x00\x00\x00\x00\x05\x0b\x00\x00\x00\x05\n8\x00\x10\x00\x00\x00\x03\x00\x00\x00m\x9e\xc6\xb7\xc7,\xd2\x11\x85N\x00\xa0\xc9\x83\xf6\x08\xbaz\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x01\x00\x00\x00\x00\x00\x05\t\x00\x00\x00\x05\n8\x00\x10\x00\x00\x00\x03\x00\x00\x00m\x9e\xc6\xb7\xc7,\xd2\x11\x85N\x00\xa0\xc9\x83\xf6\x08\x9cz\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x01\x00\x00\x00\x00\x00\x05\t\x00\x00\x00\x05\n8\x00\x10\x00\x00\x00\x03\x00\x00\x00m\x9e\xc6\xb7\xc7,\xd2\x11\x85N\x00\xa0\xc9\x83\xf6\x08\x86z\x96\xbf\xe6\r\xd0\x11\xa2\x85\x00\xaa\x000I\xe2\x01\x01\x00\x00\x00\x00\x00\x05\t\x00\x00\x00\x05\x008\x00\x00\x01\x00\x00\x01\x00\x00\x00\xad\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x05\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\x98\xb7\xba\xeb^\xc4g\x7fy2s\xab\x04\x02\x00\x00\x05\x00(\x00\x00\x01\x00\x00\x01\x00\x00\x00v[\xe9\x89MDbL\x99\x1a\x0f\xac\xbe\xdad\x0c\x01\x01\x00\x00\x00\x00\x00\x05\t\x00\x00\x00\x05\x00,\x00\x00\x01\x00\x00\x01\x00\x00\x00\xad\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x05\x00,\x00\x00\x01\x00\x00\x01\x00\x00\x00v[\xe9\x89MDbL\x99\x1a\x0f\xac\xbe\xdad\x0c\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x05\x00,\x00\x00\x01\x00\x00\x01\x00\x00\x00\xc9m\xa3\xe2\x17\xae\xc3G\xb5\x8b\xbe4\xc5[\xa63\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00-\x02\x00\x00\x05\x00(\x00\x00\x01\x00\x00\x01\x00\x00\x00\x9c6\x0f(\xc7g\x8eC\xae\x98\x1dF\xf3\xc6\xf5A\x01\x01\x00\x00\x00\x00\x00\x05\x0b\x00\x00\x00\x05\x00(\x00\x00\x01\x00\x00\x01\x00\x00\x00}\xdc\xc2\xcc\xad\xa6zJ\x88F\xc0N<\xc55\x01\x01\x01\x00\x00\x00\x00\x00\x05\x0b\x00\x00\x00\x05\x00(\x00\x00\x01\x00\x00\x01\x00\x00\x00^L\xc7\x05\xebM\xb4C\xbd\x9f\x86fL*\x7f\xd5\x01\x01\x00\x00\x00\x00\x00\x05\x0b\x00\x00\x00\x05\x00(\x00\x00\x01\x00\x00\x01\x00\x00\x00\xae\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x01\x00\x00\x00\x00\x00\x05\t\x00\x00\x00\x05\x00,\x00\x00\x01\x00\x00\x01\x00\x00\x00\xae\xf61\x11\x07\x9c\xd1\x11\xf7\x9f\x00\xc0O\xc2\xdc\xd2\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x05\n(\x000\x01\x00\x00\x01\x00\x00\x00\xdeG\xe6\x91o\xd9pK\x95W\xd6?\xf4\xf3\xcc\xd8\x01\x01\x00\x00\x00\x00\x00\x05\n\x00\x00\x00\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x01\x00\x00\x00\x0e\x00\x00\x00\xe4\x00\x02\x00\x0e\x00\x00\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00\x00\x00\x01\x00\x00\x00\x08\x00\x00\x00\xec\x00\x02\x00\x08\x00\x00\x00\xe4\xb6\x96\\G\xd1\xd0\x01\x01\x00\x00\x00\x08\x00\x00\x00\xf4\x00\x02\x00\x08\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x80\x01\x00\x00\x00\x08\x00\x00\x00\xfc\x00\x02\x00\x08\x00\x00\x00\x00\xcc\x1d\xcf\xfb\xff\xff\xff\x01\x00\x00\x00\x08\x00\x00\x00\x04\x01\x02\x00\x08\x00\x00\x00\x00\xcc\x1d\xcf\xfb\xff\xff\xff\x01\x00\x00\x00\x04\x00\x00\x00\x0c\x01\x02\x00\x04\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x08\x00\x00\x00\x14\x01\x02\x00\x08\x00\x00\x00\x00\x80\xa6\n\xff\xde\xff\xff\x01\x00\x00\x00\x08\x00\x00\x00\x1c\x01\x02\x00\x08\x00\x00\x00\x00@\x96\xd56\xff\xff\xff\x01\x00\x00\x00\x04\x00\x00\x00$\x01\x02\x00\x04\x00\x00\x00\x07\x00\x00\x00\x01\x00\x00\x00\x08\x00\x00\x00,\x01\x02\x00\x08\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x04\x00\x00\x004\x01\x02\x00\x04\x00\x00\x00\xe8\x03\x00\x00\x01\x00\x00\x00\x04\x00\x00\x00<\x01\x02\x00\x04\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00\x00\x04\x00\x00\x00D\x01\x02\x00\x04\x00\x00\x00\x18\x00\x00\x00\x01\x00\x00\x00\x18\x00\x00\x00L\x01\x02\x00\x18\x00\x00\x00\x01\x04\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\x98\xb7\xba\xeb^\xc4g\x7fy2s\xab\x01\x00\x00\x00\x04\x00\x00\x00T\x01\x02\x00\x04\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00\x00\x02\x00\x00\x00\\\x01\x02\x00\x02\x00\x00\x00\x00\x01\x00\x00\x01\x00\x00\x00\x04\x00\x00\x00d\x01\x02\x00\x04\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x90\x00\x00\x00l\x01\x02\x00\x90\x00\x00\x00\x90\x00\x00\x00\x00\x00\x00\x00\x15C\x8a\xe3y\xe7FO\x96\xb1\xbb\x89\xe8(\xc2\xed\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00+\x00\x00\x00C\x00N\x00=\x00R\x00I\x00D\x00 \x00M\x00a\x00n\x00a\x00g\x00e\x00r\x00$\x00,\x00C\x00N\x00=\x00S\x00y\x00s\x00t\x00e\x00m\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x01\x00\x00\x00\x1a\x01\x00\x00t\x01\x02\x00\x1a\x01\x00\x00\x1a\x01\x00\x00\x00\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00p\x00\x00\x00C\x00N\x00=\x00N\x00T\x00D\x00S\x00 \x00S\x00e\x00t\x00t\x00i\x00n\x00g\x00s\x00,\x00C\x00N\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00-\x00D\x00C\x00,\x00C\x00N\x00=\x00S\x00e\x00r\x00v\x00e\x00r\x00s\x00,\x00C\x00N\x00=\x00D\x00e\x00f\x00a\x00u\x00l\x00t\x00-\x00F\x00i\x00r\x00s\x00t\x00-\x00S\x00i\x00t\x00e\x00-\x00N\x00a\x00m\x00e\x00,\x00C\x00N\x00=\x00S\x00i\x00t\x00e\x00s\x00,\x00C\x00N\x00=\x00C\x00o\x00n\x00f\x00i\x00g\x00u\x00r\x00a\x00t\x00i\x00o\x00n\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x00\x00\x01\x00\x00\x00\x04\x00\x00\x00|\x01\x02\x00\x04\x00\x00\x00\x00\x00\x00\x8c\x0b\x00\x00\x00\x84\x00\x00\x00\x84\x01\x02\x00\x8c\x00\x00\x00\x88\x01\x02\x00\x9c\x00\x00\x00\x8c\x01\x02\x00\x84\x00\x00\x00\x90\x01\x02\x00\x90\x00\x00\x00\x94\x01\x02\x00\x94\x00\x00\x00\x98\x01\x02\x00\x98\x00\x00\x00\x9c\x01\x02\x00\xac\x00\x00\x00\xa0\x01\x02\x00\x90\x00\x00\x00\xa4\x01\x02\x00\xac\x00\x00\x00\xa8\x01\x02\x00\x90\x00\x00\x00\xac\x01\x02\x00\x84\x00\x00\x00n\x00\x00\x00\x00\x00\x00\x00i=*\xed\x02\x83\xe1K\x92\x8aO\x0c<}D0\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1a\x00\x00\x00C\x00N\x00=\x00U\x00s\x00e\x00r\x00s\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x00\x00\x14\x00\x00\x00\xa9\xd1\xca\x15v\x88\x11\xd1\xad\xed\x00\xc0O\xd8\xd5\xcd\x8c\x00\x00\x00v\x00\x00\x00\x00\x00\x00\x00>\x9fS\x1c\xff\x18\xd9K\xad\xe2\xb9\xf9\xbcwJ\xb0\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00C\x00N\x00=\x00C\x00o\x00m\x00p\x00u\x00t\x00e\x00r\x00s\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x00\x00\x14\x00\x00\x00\xaa1(%v\x88\x11\xd1\xad\xed\x00\xc0O\xd8\xd5\xcd\x9c\x00\x00\x00\x88\x00\x00\x00\x00\x00\x00\x00\x95\xdf\xc9\xb8\x8f\x95\x17L\x9f\xa1\r\x95\xaf\xd5\xc1\xd0\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\'\x00\x00\x00O\x00U\x00=\x00D\x00o\x00m\x00a\x00i\x00n\x00 \x00C\x00o\x00n\x00t\x00r\x00o\x00l\x00l\x00e\x00r\x00s\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x14\x00\x00\x00\xa3a\xb2\xff\xff\xd2\x11\xd1\xaaK\x00\xc0O\xd7\xd8:\x84\x00\x00\x00p\x00\x00\x00\x00\x00\x00\x00\xc7N\xe4\xc2\xd96EG\x81|[\xf5\xbcx\x0e\xef\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1b\x00\x00\x00C\x00N\x00=\x00S\x00y\x00s\x00t\x00e\x00m\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x14\x00\x00\x00\xab\x1d0\xf3v\x88\x11\xd1\xad\xed\x00\xc0O\xd8\xd5\xcd\x90\x00\x00\x00|\x00\x00\x00\x00\x00\x00\x00\xfb3]!\xb3C\x19H\x84(\xf5rD\x80)\x1c\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00!\x00\x00\x00C\x00N\x00=\x00L\x00o\x00s\x00t\x00A\x00n\x00d\x00F\x00o\x00u\x00n\x00d\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x14\x00\x00\x00\xab\x81S\xb7v\x88\x11\xd1\xad\xed\x00\xc0O\xd8\xd5\xcd\x94\x00\x00\x00\x80\x00\x00\x00\x00\x00\x00\x00\xa9\x11n\xec\xab\xd4YO\xa9?\x14\xdb@(KE\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00#\x00\x00\x00C\x00N\x00=\x00I\x00n\x00f\x00r\x00a\x00s\x00t\x00r\x00u\x00c\x00t\x00u\x00r\x00e\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x14\x00\x00\x00/\xba\xc1\x87\n\xde\x11\xd2\x97\xc4\x00\xc0O\xd8\xd5\xcd\x98\x00\x00\x00\x82\x00\x00\x00\x00\x00\x00\x00y]\xf0\x0b\xf6\xd0\xcbO\xa6&*\xeb\xf5\xb3L\x1b\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00$\x00\x00\x00C\x00N\x00=\x00D\x00e\x00l\x00e\x00t\x00e\x00d\x00 \x00O\x00b\x00j\x00e\x00c\x00t\x00s\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x00\x00\x14\x00\x00\x00\x18\xe2\xea\x80hO\x11\xd2\xb9\xaa\x00\xc0Oy\xf8\x05\xac\x00\x00\x00\x96\x00\x00\x00\x00\x00\x00\x00\x91\xf3\'v\x92\xeeWF\xa7=\x00\x06\'\xcf\xd9D\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00.\x00\x00\x00C\x00N\x00=\x00F\x00o\x00r\x00e\x00i\x00g\x00n\x00S\x00e\x00c\x00u\x00r\x00i\x00t\x00y\x00P\x00r\x00i\x00n\x00c\x00i\x00p\x00a\x00l\x00s\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x00\x00\x14\x00\x00\x00"\xb7\x0cg\xd5nN\xfb\x91\xe90\x0f\xca=\xc1\xaa\x90\x00\x00\x00|\x00\x00\x00\x00\x00\x00\x00!\xbd\xd0\xe0=\x8d\xcfF\xb9\x95[_\x11+\xaf\xfd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00!\x00\x00\x00C\x00N\x00=\x00P\x00r\x00o\x00g\x00r\x00a\x00m\x00 \x00D\x00a\x00t\x00a\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x14\x00\x00\x00\tF\x0c\x08\xae\x1eJN\xa0\xf6J\xee}\xaa\x1eZ\xac\x00\x00\x00\x96\x00\x00\x00\x00\x00\x00\x00B\x99\x1cL\x1e\xddFD\x94\xe5\xe3\x95"K\x87{\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00.\x00\x00\x00C\x00N\x00=\x00M\x00i\x00c\x00r\x00o\x00s\x00o\x00f\x00t\x00,\x00C\x00N\x00=\x00P\x00r\x00o\x00g\x00r\x00a\x00m\x00 \x00D\x00a\x00t\x00a\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x00\x00\x14\x00\x00\x00\xf4\xbe\x92\xa4\xc7wH^\x87\x8e\x94!\xd50\x87\xdb\x90\x00\x00\x00z\x00\x00\x00\x00\x00\x00\x00\x92*v]O\xa8\xdbC\x92\x07\x84\x80\xb2D\x1bM\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00 \x00\x00\x00C\x00N\x00=\x00N\x00T\x00D\x00S\x00 \x00Q\x00u\x00o\x00t\x00a\x00s\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x00\x00\x14\x00\x00\x00b\'\xf0\xaf\x1f\xc2A\r\x8e;\xb1\x06\x15\xbb[\x0f\x01\x00\x00\x00\xae\x00\x00\x00\xb4\x01\x02\x00\xae\x00\x00\x00\xae\x00\x00\x00\x00\x00\x00\x00\xc9\xd9\xa8\x16!\xa7\xddM\x8b\x1f\xdew\xe0\xe3\xb4\x07\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00:\x00\x00\x00C\x00N\x00=\x00D\x00o\x00m\x00a\x00i\x00n\x00-\x00D\x00N\x00S\x00,\x00C\x00N\x00=\x00S\x00c\x00h\x00e\x00m\x00a\x00,\x00C\x00N\x00=\x00C\x00o\x00n\x00f\x00i\x00g\x00u\x00r\x00a\x00t\x00i\x00o\x00n\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x00\x00\x01\x00\x00\x00\x04\x00\x00\x00\xbc\x01\x02\x00\x04\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00\x00\xb8\x00\x00\x00\xc4\x01\x02\x00\xb8\x00\x00\x00[\x00L\x00D\x00A\x00P\x00:\x00/\x00/\x00C\x00N\x00=\x00{\x003\x001\x00B\x002\x00F\x003\x004\x000\x00-\x000\x001\x006\x00D\x00-\x001\x001\x00D\x002\x00-\x009\x004\x005\x00F\x00-\x000\x000\x00C\x000\x004\x00F\x00B\x009\x008\x004\x00F\x009\x00}\x00,\x00C\x00N\x00=\x00P\x00o\x00l\x00i\x00c\x00i\x00e\x00s\x00,\x00C\x00N\x00=\x00S\x00y\x00s\x00t\x00e\x00m\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00;\x000\x00]\x00\x01\x00\x00\x00\xa8\x00\x00\x00\xcc\x01\x02\x00\xa8\x00\x00\x00\x94\x00\x00\x00\x00\x00\x00\x001\xdf\xae\x8c\xc3\xe2HG\x95:\x13\xdfB\x03\xaf\xca\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00-\x00\x00\x00C\x00N\x00=\x00M\x00a\x00n\x00a\x00g\x00e\x00d\x00 \x00S\x00e\x00r\x00v\x00i\x00c\x00e\x00 \x00A\x00c\x00c\x00o\x00u\x00n\x00t\x00s\x00,\x00D\x00C\x00=\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00,\x00D\x00C\x00=\x00N\x00E\x00T\x00\x00\x00\x14\x00\x00\x00\x1e\xb98\x89\xe4\x0cE\xdf\x9f\x0cd\xd2;\xbbb7\x01\x00\x00\x00\x04\x00\x00\x00\xd4\x01\x02\x00\x04\x00\x00\x00\n\x00\x00\x00\x01\x00\x00\x00\x04\x00\x00\x00\xdc\x01\x02\x00\x04\x00\x00\x00\x04\x00\x00\x00\x01\x00\x00\x00\x04\x00\x00\x00\xe4\x01\x02\x00\x04\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00\x00\x04\x00\x00\x00\xec\x01\x02\x00\x04\x00\x00\x00\xe8\x03\x00\x00\x01\x00\x00\x00\x04\x00\x00\x00\xf4\x01\x02\x00\x04\x00\x00\x00\n\x00\x00\x00%\x00\x00\x00%\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00.!a\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\xdd@\x00\x00\x00\x00\x00\x00\x02\x00\x00\x00\x00\x00\x00\x00\xdc\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03 \x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x06\x00\x00\x00\x00\x00\x00\x00\x12\x98\xd5\x0b\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x08\x00\x01\x00\x00\x00\x00\x00\xd3\x00\x00\x00\x00\x00\x00\x00y\xfe\xe2\x0b\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x82-\x01\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xdc\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03 \x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xdc\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03 \x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xdc\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03 \x00\x00\x00\x00\x00\x00\xdf\x00\x00\x00\x00\x00\x00\x00\xb4]\xe3\x0b\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\xbc-\x01\x00\x00\x00\x00\x00\x02\x00\x00\x00\x00\x00\x00\x007\x1bV\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\xf30\x00\x00\x00\x00\x00\x00\x02\x00\x00\x00\x00\x00\x00\x007\x1bV\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\xf30\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xdc\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03 \x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xdc\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03 \x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xdc\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03 \x00\x00\x00\x00\x00\x00\x02\x00\x00\x00\x00\x00\x00\x007\x1bV\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\xf30\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x04\x10\x00\x00\x00\x00\x00\x00\xd3\x00\x00\x00\x00\x00\x00\x00y\xfe\xe2\x0b\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x84-\x01\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xdc\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03 \x00\x00\x00\x00\x00\x00\xd3\x00\x00\x00\x00\x00\x00\x00y\xfe\xe2\x0b\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x86-\x01\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x006\x1aV\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x9d0\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\xd2\x19V\x08\x03\x00\x00\x00\xd7\xba[\xe8#\t\xcbA\x91\x1e6\x91\xd2\x01H\x15\x03\x10\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
        ncChanges = DRSGetNCChangesResponse()
        ncChanges.fromString(getNCChangesResponse)
        output = ncChanges.getData()
        print("ORIG: %d, REPACKED: %d" % (len(getNCChangesResponse), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(getNCChangesResponse)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(len(getNCChangesResponse), len(output))

    def test_5(self):
        #<class 'impacket.dcerpc.v5.samr.SamrLookupNamesInDomainResponse'>
        samrLookupNamesInDomainResponse = b'\x01\x00\x00\x00\x00\x00\x02\x00\x01\x00\x00\x00\xf4\x01\x00\x00\x01\x00\x00\x00\x04\x00\x02\x00\x01\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00'
        hexdump(samrLookupNamesInDomainResponse)
        samrLookupNamesInDomain = SamrLookupNamesInDomainResponse()
        samrLookupNamesInDomain.fromString(samrLookupNamesInDomainResponse)
        samrLookupNamesInDomain.dump()
        output = samrLookupNamesInDomain.getData()
        print("ORIG: %d, REPACKED: %d" % (len(samrLookupNamesInDomainResponse), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(samrLookupNamesInDomainResponse)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(samrLookupNamesInDomainResponse, output)

    def test_6(self):
        lsarGetUserNameResponse = b'\x00\x00\x02\x00\n\x00\x0c\x00\x04\x00\x02\x00\x06\x00\x00\x00\x00\x00\x00\x00\x05\x00\x00\x00a\x00d\x00m\x00i\x00n\x00\xaa\xaa\x00\x00\x00\x00\x00\x00\x00\x00'
        hexdump(lsarGetUserNameResponse)
        lsarGetUserName = LsarGetUserNameResponse()
        lsarGetUserName.fromString(lsarGetUserNameResponse)
        lsarGetUserName.dump()
        output = lsarGetUserName.getData()
        print("ORIG: %d, REPACKED: %d" % (len(lsarGetUserNameResponse), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(lsarGetUserNameResponse)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(lsarGetUserNameResponse, output)

    def test_8(self):
        lsarLookupSids2Response = b'\x00\x00\x02\x00\x00\x00\x00\x00\x01\x00\x00\x00\xaa\xaa\xaa\xaa\x00\x00\x02\x00\x00\x00\x00\x00 \x00\x00\x00\xef\xef\xef\xef\x01\x00\x00\x00\x00\x00\x00\x00\x0e\x00\x10\x00\xaa\xaa\xaa\xaa\x00\x00\x02\x00\x00\x00\x00\x00\x00\x00\x02\x00\x00\x00\x00\x00\x08\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x07\x00\x00\x00\x00\x00\x00\x00F\x00R\x00E\x00E\x00F\x00L\x00Y\x00\xee\xee\x04\x00\x00\x00\x00\x00\x00\x00\x01\x04\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\x98\xb7\xba\xeb^\xc4g\x7fy2s\xab\x02\x00\x00\x00\xaa\xaa\xaa\xaa\x00\x00\x02\x00\x00\x00\x00\x00\x02\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\xab\xab\xab\xab\x1a\x00\x1a\x00\xaa\xaa\xaa\xaa\x00\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\xab\xab\xab\xab\n\x00\n\x00\xaa\xaa\xaa\xaa\x00\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\r\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\r\x00\x00\x00\x00\x00\x00\x00A\x00d\x00m\x00i\x00n\x00i\x00s\x00t\x00r\x00a\x00t\x00o\x00r\x00\xab\xab\xab\xab\xab\xab\x05\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x05\x00\x00\x00\x00\x00\x00\x00G\x00u\x00e\x00s\x00t\x00\xbf\xbf\x02\x00\x00\x00\x00\x00\x00\x00'
        hexdump(lsarLookupSids2Response)
        lsarLookupSids2 = LsarLookupSids2Response(isNDR64=True)
        lsarLookupSids2.fromString(lsarLookupSids2Response)
        lsarLookupSids2.dumpRaw()
        output = lsarLookupSids2.getData()
        print("ORIG: %d, REPACKED: %d" % (len(lsarLookupSids2Response), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(lsarLookupSids2Response)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(lsarLookupSids2Response, output)

    def test_88(self):
        baseRegEnumValueResponse = b' \x00\xc8\x00\x00\x00\x02\x00d\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00\x00R\x00e\x00g\x00i\x00s\x00t\x00e\x00r\x00e\x00d\x00O\x00w\x00n\x00e\x00r\x00\x00\x00\x04\x00\x02\x00\x01\x00\x00\x00\x08\x00\x02\x00\x14\x00\x00\x00\x00\x00\x00\x00\x14\x00\x00\x00M\x00i\x00c\x00r\x00o\x00s\x00o\x00f\x00t\x00\x00\x00\x0c\x00\x02\x00\x14\x00\x00\x00\x10\x00\x02\x00\x14\x00\x00\x00\x00\x00\x00\x00'
        hexdump(baseRegEnumValueResponse)
        baseRegEnumValue= BaseRegEnumValueResponse(isNDR64=False)
        baseRegEnumValue.fromString(baseRegEnumValueResponse)
        baseRegEnumValue.dumpRaw()
        output = baseRegEnumValue.getData()
        print("ORIG: %d, REPACKED: %d" % (len(baseRegEnumValueResponse), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(baseRegEnumValueResponse)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(baseRegEnumValueResponse, output)

    def test_9(self):
        rCreateServiceWResponse = b'\x00\x00\x00\x00\x00\x00\x00\x00ZU\x81\xedB>RL\xb9v\xb1\xe3\xc5?~\x15\x00\x00\x00\x00'
        hexdump(rCreateServiceWResponse)
        rCreateServiceW= RCreateServiceWResponse(isNDR64=False)
        rCreateServiceW.fromString(rCreateServiceWResponse)
        rCreateServiceW.dumpRaw()
        output = rCreateServiceW.getData()
        print("ORIG: %d, REPACKED: %d" % (len(rCreateServiceWResponse), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(rCreateServiceWResponse)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(rCreateServiceWResponse, output)

    def test_10(self):
        netrShareEnum = b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xbc\xbc\xbc\xbc\x00\x00\x00\x00\xbd\xbd\xbd\xbd\xfc\xb1\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xbc\xbc\xbc\xbc\x00\x00\x00\x00\x00\x00\x00\x00\xff\xff\xff\xff\xaa\xaa\xaa\xaa\x00\x00\x00\x00\x00\x00\x00\x00'

        request = srvs.NetrShareEnum(isNDR64=True)
        request['ServerName'] = NULL
        request['PreferedMaximumLength'] = 0xffffffff
        request['ResumeHandle'] = NULL
        request['InfoStruct']['ShareInfo']['tag'] = 0
        request['InfoStruct']['ShareInfo']['Level0']['Buffer'] = NULL
        request['InfoStruct']['Level'] = 0
        output = request.getData()
        hexdump(netrShareEnum)
        print("ORIG: %d, REPACKED: %d" % (len(netrShareEnum), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(netrShareEnum)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(len(netrShareEnum), len(output))

    def test_11(self):
        ept_lookup_resp = b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xa1\x00\x00\x00\xf3\x01\x00\x00\x00\x00\x00\x00\xa1\x00\x00\x00\xba\x94Rv\xbc`\xb8H\x92\xe9\x89\xfdwv\x9d\x91\x01\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\xba\x94Rv\xbc`\xb8H\x92\xe9\x89\xfdwv\x9d\x91\x02\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\xba\x94Rv\xbc`\xb8H\x92\xe9\x89\xfdwv\x9d\x91\x03\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\xba\x94Rv\xbc`\xb8H\x92\xe9\x89\xfdwv\x9d\x91\x04\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\xeei\x86\xb0\xb5\x8c\xa5C\xa0\x17\x84\xfe\x00\x00\x00\x00\x05\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\xeei\x86\xb0\xb5\x8c\xa5C\xa0\x17\x84\xfe\x00\x00\x00\x00\x06\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\xeei\x86\xb0\xb5\x8c\xa5C\xa0\x17\x84\xfe\x00\x00\x00\x00\x07\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xddtermsrv\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\x00\x00\x00\x00\x00\x00\x00\x13\x00\x00\x00Impl friendly name\x00\xdd\x0c\x13\xefR\xfd\x08\x88C\x86\xb3n\xdf\x00\x00\x00\x01\t\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00Secure Desktop LRPC interface\x00\xdd\xdd\xeei\x86\xb0\xb5\x8c\xa5C\xa0\x17\x84\xfe\x00\x00\x00\x01\n\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x0b\x00\x00\x00\x00\x00\x00\x00\x1a\x00\x00\x00DHCP Client LRPC Endpoint\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\x1a\x00\x00\x00DHCP Client LRPC Endpoint\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\r\x00\x00\x00\x00\x00\x00\x00\x1a\x00\x00\x00DHCP Client LRPC Endpoint\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x0e\x00\x00\x00\x00\x00\x00\x00\x1a\x00\x00\x00DHCP Client LRPC Endpoint\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x0f\x00\x00\x00\x00\x00\x00\x00\x1a\x00\x00\x00DHCP Client LRPC Endpoint\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00\x00\x00\x00\x00\x00\x1c\x00\x00\x00DHCPv6 Client LRPC Endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x11\x00\x00\x00\x00\x00\x00\x00\x1c\x00\x00\x00DHCPv6 Client LRPC Endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x12\x00\x00\x00\x00\x00\x00\x00\x1c\x00\x00\x00DHCPv6 Client LRPC Endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x13\x00\x00\x00\x00\x00\x00\x00\x1c\x00\x00\x00DHCPv6 Client LRPC Endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x14\x00\x00\x00\x00\x00\x00\x00\x14\x00\x00\x00NRP server endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x15\x00\x00\x00\x00\x00\x00\x00\x14\x00\x00\x00NRP server endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x16\x00\x00\x00\x00\x00\x00\x00\x14\x00\x00\x00NRP server endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00\x00Event log TCPIP\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x18\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00\x00Event log TCPIP\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x19\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00\x00Event log TCPIP\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1a\x00\x00\x00\x00\x00\x00\x00%\x00\x00\x00IP Transition Configuration endpoint\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1b\x00\x00\x00\x00\x00\x00\x00%\x00\x00\x00IP Transition Configuration endpoint\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1c\x00\x00\x00\x00\x00\x00\x00%\x00\x00\x00IP Transition Configuration endpoint\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1d\x00\x00\x00\x00\x00\x00\x00%\x00\x00\x00IP Transition Configuration endpoint\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00\x00\x00\x00\x00%\x00\x00\x00IP Transition Configuration endpoint\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1f\x00\x00\x00\x00\x00\x00\x00%\x00\x00\x00IP Transition Configuration endpoint\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00 \x00\x00\x00\x00\x00\x00\x00\x0f\x00\x00\x00IKE/Authip API\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00!\x00\x00\x00\x00\x00\x00\x00\x0f\x00\x00\x00IKE/Authip API\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"\x00\x00\x00\x00\x00\x00\x00\x0f\x00\x00\x00IKE/Authip API\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00#\x00\x00\x00\x00\x00\x00\x00\x0f\x00\x00\x00IKE/Authip API\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00$\x00\x00\x00\x00\x00\x00\x00\x0f\x00\x00\x00IKE/Authip API\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00%\x00\x00\x00\x00\x00\x00\x00\x0f\x00\x00\x00IKE/Authip API\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00&\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00\x00XactSrv service\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\'\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00\x00XactSrv service\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00(\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00\x00XactSrv service\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00)\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00\x00XactSrv service\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00*\x00\x00\x00\x00\x00\x00\x00\x10\x00\x00\x00XactSrv service\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00+\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00,\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00-\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00.\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00/\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x000\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x001\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x002\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x003\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x004\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x005\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x006\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x007\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x008\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x009\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00:\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xddsens\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00;\x00\x00\x00\x00\x00\x00\x00\x13\x00\x00\x00Impl friendly name\x00\xddsens\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00<\x00\x00\x00\x00\x00\x00\x00\x13\x00\x00\x00Impl friendly name\x00\xddsens\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00=\x00\x00\x00\x00\x00\x00\x00\x13\x00\x00\x00Impl friendly name\x00\xdd\xc7\xf7\xd1$\xafv(O\x9c\xcd\x7fl\xb6F\x86\x01>\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\xc7\xf7\xd1$\xafv(O\x9c\xcd\x7fl\xb6F\x86\x01?\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xddgpclient\x00\x00\x00\x00\x00\x00\x00\x00@\x00\x00\x00\x00\x00\x00\x00\x13\x00\x00\x00Impl friendly name\x00\xddgpclient\x00\x00\x00\x00\x00\x00\x00\x00A\x00\x00\x00\x00\x00\x00\x00\x13\x00\x00\x00Impl friendly name\x00\xddprofiles\x00\x00\x00\x00\x00\x00\x00\x00B\x00\x00\x00\x00\x00\x00\x00\x13\x00\x00\x00Impl friendly name\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00C\x00\x00\x00\x00\x00\x00\x00\x1b\x00\x00\x00WinHttp Auto-Proxy Service\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00D\x00\x00\x00\x00\x00\x00\x00\x1b\x00\x00\x00WinHttp Auto-Proxy Service\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00E\x00\x00\x00\x00\x00\x00\x00\x1b\x00\x00\x00WinHttp Auto-Proxy Service\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00F\x00\x00\x00\x00\x00\x00\x00\x1b\x00\x00\x00WinHttp Auto-Proxy Service\x00\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00G\x00\x00\x00\x00\x00\x00\x00\x14\x00\x00\x00NSI server endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00H\x00\x00\x00\x00\x00\x00\x00\x14\x00\x00\x00NSI server endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00I\x00\x00\x00\x00\x00\x00\x00\x08\x00\x00\x00Fw APIs\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00J\x00\x00\x00\x00\x00\x00\x00\x08\x00\x00\x00Fw APIs\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00K\x00\x00\x00\x00\x00\x00\x00\x19\x00\x00\x00Base Firewall Engine API\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00L\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00M\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00N\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00O\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00P\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00Q\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00R\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00S\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00T\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00U\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00V\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00W\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00X\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00Y\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00Z\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00[\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\\\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00]\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00^\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00`\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00a\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00b\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00c\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00d\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00e\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00f\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00g\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00h\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00i\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00j\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00k\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00l\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00m\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00n\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00o\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00p\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00q\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00r\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00s\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00t\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00u\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00v\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00w\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00x\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00y\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00z\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00{\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00|\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00}\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00~\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x7f\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x80\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x81\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x82\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x83\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x84\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x85\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x86\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x87\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x88\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x89\x00\x00\x00\x00\x00\x00\x00\x1e\x00\x00\x00MS NT Directory DRS Interface\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x8a\x00\x00\x00\x00\x00\x00\x00\x1a\x00\x00\x00Spooler function endpoint\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x8b\x00\x00\x00\x00\x00\x00\x00$\x00\x00\x00Spooler base remote object endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x8c\x00\x00\x00\x00\x00\x00\x00\x1a\x00\x00\x00Spooler function endpoint\x00\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x8d\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x8e\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x8f\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x90\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x91\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x92\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x93\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x94\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x95\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x96\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x97\x00\x00\x00\x00\x00\x00\x00\x1c\x00\x00\x00IPSec Policy agent endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x98\x00\x00\x00\x00\x00\x00\x00\x1c\x00\x00\x00IPSec Policy agent endpoint\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x99\x00\x00\x00\x00\x00\x00\x00\x0f\x00\x00\x00Remote Fw APIs\x00\xdd\x08n\xfb\xec\xaek\x1a@\x97}\x10x\xd7\xe2A\xd4\x9a\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x08n\xfb\xec\xaek\x1a@\x97}\x10x\xd7\xe2A\xd4\x9b\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd-\xa9\xf0%\x1d.\xe5N\xa4CG\xa6\xd0\xf3W!\x9c\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xddx\xaf\x13\x9aR\x87\xedL\xaa\xfa\xb2\x1e\xd8\x8a\xbf\\\x9d\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xddR\x19\x10\xdd\x0c\xfb\x8f@\xa3\xfa6\x1a\x07a\xd5U\x9e\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x94\xedS\xe7>mlB\xba><\x11\xf1\x07\xe2`\x9f\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\xdd\xdd\xdd\x07\xed\xc1[\xf5\xf5_H\x9d\xfdo\xd0\xac\xf9\xa2<\xa0\x00\x00\x00\x00\x00\x00\x00\r\x00\x00\x00Frs2 Service\x00\xdd\xdd\xdd\x07\xed\xc1[\xf5\xf5_H\x9d\xfdo\xd0\xac\xf9\xa2<\xa1\x00\x00\x00\x00\x00\x00\x00\r\x00\x00\x00Frs2 Service\x00\xcc\xcc\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rp\xfeZ\xd9\xd5\xa6YB\x82.,\x84\xda\x1d\xdb\r\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x00\x01\x00\t\x04\x00\x00\x00\x00\x00\xccP\x00\x00\x00P\x00\x00\x00\x04\x00\x13\x00\rp\xfeZ\xd9\xd5\xa6YB\x82.,\x84\xda\x1d\xdb\r\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x10\x00WindowsShutdown\x00e\x00\x00\x00e\x00\x00\x00\x05\x00\x13\x00\rp\xfeZ\xd9\xd5\xa6YB\x82.,\x84\xda\x1d\xdb\r\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x13\x00\\PIPE\\InitShutdown\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xcc\xccO\x00\x00\x00O\x00\x00\x00\x04\x00\x13\x00\rp\xfeZ\xd9\xd5\xa6YB\x82.,\x84\xda\x1d\xdb\r\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0f\x00WMsgKRpc052A70\x00\xccP\x00\x00\x00P\x00\x00\x00\x04\x00\x13\x00\r\xc3&\xf2v\x14\xec%C\x8a\x99jF4\x84\x18\xaf\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x10\x00WindowsShutdown\x00e\x00\x00\x00e\x00\x00\x00\x05\x00\x13\x00\r\xc3&\xf2v\x14\xec%C\x8a\x99jF4\x84\x18\xaf\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x13\x00\\PIPE\\InitShutdown\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xcc\xccO\x00\x00\x00O\x00\x00\x00\x04\x00\x13\x00\r\xc3&\xf2v\x14\xec%C\x8a\x99jF4\x84\x18\xaf\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0f\x00WMsgKRpc052A70\x00\xccX\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r\xb5m\xac\xc9\xb7\x82UN\xae\x8a\xe4d\xed{Bw\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-755a2d4ec93d5695ee\x00O\x00\x00\x00O\x00\x00\x00\x04\x00\x13\x00\r\xd8]\xe6\x12\x7f\x88\xefA\x91\xbf\x8d\x81lB\xc2\xe7\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0f\x00WMsgKRpc052C21\x00\xccO\x00\x00\x00O\x00\x00\x00\x04\x00\x13\x00\r\xc3&\xf2v\x14\xec%C\x8a\x99jF4\x84\x18\xaf\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0f\x00WMsgKRpc052C21\x00\xccI\x00\x00\x00I\x00\x00\x00\x04\x00\x13\x00\r\xc5(G<\xab\xf0\x8bD\xbd\xa1l\xe0\x1e\xb0\xa6\xd5\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\t\x00dhcpcsvc\x00\xcc\xcc\xccJ\x00\x00\x00J\x00\x00\x00\x04\x00\x13\x00\r\xc5(G<\xab\xf0\x8bD\xbd\xa1l\xe0\x1e\xb0\xa6\xd5\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\n\x00dhcpcsvc6\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r\xc5(G<\xab\xf0\x8bD\xbd\xa1l\xe0\x1e\xb0\xa6\xd5\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x01\x01\x00\t\x04\x00\x00\x00\x00\x00\xcca\x00\x00\x00a\x00\x00\x00\x05\x00\x13\x00\r\xc5(G<\xab\xf0\x8bD\xbd\xa1l\xe0\x1e\xb0\xa6\xd5\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0f\x00\\pipe\\eventlog\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xcc\xccI\x00\x00\x00I\x00\x00\x00\x04\x00\x13\x00\r\xc5(G<\xab\xf0\x8bD\xbd\xa1l\xe0\x1e\xb0\xa6\xd5\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\t\x00eventlog\x00\xcc\xcc\xccJ\x00\x00\x00J\x00\x00\x00\x04\x00\x13\x00\r\xc5(G<\xab\xf0\x8bD\xbd\xa1l\xe0\x1e\xb0\xa6\xd6\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\n\x00dhcpcsvc6\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r\xc5(G<\xab\xf0\x8bD\xbd\xa1l\xe0\x1e\xb0\xa6\xd6\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x01\x01\x00\t\x04\x00\x00\x00\x00\x00\xcca\x00\x00\x00a\x00\x00\x00\x05\x00\x13\x00\r\xc5(G<\xab\xf0\x8bD\xbd\xa1l\xe0\x1e\xb0\xa6\xd6\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0f\x00\\pipe\\eventlog\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xcc\xccI\x00\x00\x00I\x00\x00\x00\x04\x00\x13\x00\r\xc5(G<\xab\xf0\x8bD\xbd\xa1l\xe0\x1e\xb0\xa6\xd6\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\t\x00eventlog\x00\xcc\xcc\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r\x0c\xc5\xad0\xbc\\\xceF\x9a\x0e\x91\x91G\x89\xe2<\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x01\x01\x00\t\x04\x00\x00\x00\x00\x00\xcca\x00\x00\x00a\x00\x00\x00\x05\x00\x13\x00\r\x0c\xc5\xad0\xbc\\\xceF\x9a\x0e\x91\x91G\x89\xe2<\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0f\x00\\pipe\\eventlog\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xcc\xccI\x00\x00\x00I\x00\x00\x00\x04\x00\x13\x00\r\x0c\xc5\xad0\xbc\\\xceF\x9a\x0e\x91\x91G\x89\xe2<\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\t\x00eventlog\x00\xcc\xcc\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r\xf7\xaf\xbe\xf6\x19\x1e\xbbO\x9f\x8f\xb8\x9e \x183|\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x01\x01\x00\t\x04\x00\x00\x00\x00\x00\xcca\x00\x00\x00a\x00\x00\x00\x05\x00\x13\x00\r\xf7\xaf\xbe\xf6\x19\x1e\xbbO\x9f\x8f\xb8\x9e \x183|\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0f\x00\\pipe\\eventlog\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xcc\xccI\x00\x00\x00I\x00\x00\x00\x04\x00\x13\x00\r\xf7\xaf\xbe\xf6\x19\x1e\xbbO\x9f\x8f\xb8\x9e \x183|\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\t\x00eventlog\x00\xcc\xcc\xcc_\x00\x00\x00_\x00\x00\x00\x05\x00\x13\x00\rj\x07-U)\xcbDN\x8bj\xd1^Y\xe2\xc0\xaf\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\r\x00\\PIPE\\srvsvc\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rj\x07-U)\xcbDN\x8bj\xd1^Y\xe2\xc0\xaf\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x02\x01\x00\t\x04\x00\x00\x00\x00\x00\xcc^\x00\x00\x00^\x00\x00\x00\x05\x00\x13\x00\rj\x07-U)\xcbDN\x8bj\xd1^Y\xe2\xc0\xaf\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0c\x00\\PIPE\\atsvc\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccH\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\rj\x07-U)\xcbDN\x8bj\xd1^Y\xe2\xc0\xaf\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00senssvc\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\rj\x07-U)\xcbDN\x8bj\xd1^Y\xe2\xc0\xaf\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE7F700A20D38041EEBE253CC2C3D8\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\rj\x07-U)\xcbDN\x8bj\xd1^Y\xe2\xc0\xaf\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xcc_\x00\x00\x00_\x00\x00\x00\x05\x00\x13\x00\r \xe5\x98\xa3\x9a\xd5\xddK\xaaz<\x1e\x03\x03\xa5\x11\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\r\x00\\PIPE\\srvsvc\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r \xe5\x98\xa3\x9a\xd5\xddK\xaaz<\x1e\x03\x03\xa5\x11\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x02\x01\x00\t\x04\x00\x00\x00\x00\x00\xcc^\x00\x00\x00^\x00\x00\x00\x05\x00\x13\x00\r \xe5\x98\xa3\x9a\xd5\xddK\xaaz<\x1e\x03\x03\xa5\x11\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0c\x00\\PIPE\\atsvc\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccH\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\r \xe5\x98\xa3\x9a\xd5\xddK\xaaz<\x1e\x03\x03\xa5\x11\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00senssvc\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r \xe5\x98\xa3\x9a\xd5\xddK\xaaz<\x1e\x03\x03\xa5\x11\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE7F700A20D38041EEBE253CC2C3D8\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\r \xe5\x98\xa3\x9a\xd5\xddK\xaaz<\x1e\x03\x03\xa5\x11\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r\x03mq\x98\xac\x89\xc7D\xbb\x8c(X$\xe5\x1cJ\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x02\x01\x00\t\x04\x00\x00\x00\x00\x00\xcc^\x00\x00\x00^\x00\x00\x00\x05\x00\x13\x00\r\x03mq\x98\xac\x89\xc7D\xbb\x8c(X$\xe5\x1cJ\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0c\x00\\PIPE\\atsvc\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccH\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\r\x03mq\x98\xac\x89\xc7D\xbb\x8c(X$\xe5\x1cJ\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00senssvc\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r\x03mq\x98\xac\x89\xc7D\xbb\x8c(X$\xe5\x1cJ\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE7F700A20D38041EEBE253CC2C3D8\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\r\x03mq\x98\xac\x89\xc7D\xbb\x8c(X$\xe5\x1cJ\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rIY\xd3\x86\xc9\x83D@\xb4$\xdb621\xfd\x0c\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x02\x01\x00\t\x04\x00\x00\x00\x00\x00\xcc^\x00\x00\x00^\x00\x00\x00\x05\x00\x13\x00\rIY\xd3\x86\xc9\x83D@\xb4$\xdb621\xfd\x0c\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0c\x00\\PIPE\\atsvc\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccH\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\rIY\xd3\x86\xc9\x83D@\xb4$\xdb621\xfd\x0c\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00senssvc\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\rIY\xd3\x86\xc9\x83D@\xb4$\xdb621\xfd\x0c\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE7F700A20D38041EEBE253CC2C3D8\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\rIY\xd3\x86\xc9\x83D@\xb4$\xdb621\xfd\x0c\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xcc^\x00\x00\x00^\x00\x00\x00\x05\x00\x13\x00\r\xb0R\x8e7\xa9\xc0\xcf\x11\x82-\x00\xaa\x00Q\xe4\x0f\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0c\x00\\PIPE\\atsvc\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccH\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\r\xb0R\x8e7\xa9\xc0\xcf\x11\x82-\x00\xaa\x00Q\xe4\x0f\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00senssvc\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r\xb0R\x8e7\xa9\xc0\xcf\x11\x82-\x00\xaa\x00Q\xe4\x0f\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE7F700A20D38041EEBE253CC2C3D8\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\r\xb0R\x8e7\xa9\xc0\xcf\x11\x82-\x00\xaa\x00Q\xe4\x0f\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xcc^\x00\x00\x00^\x00\x00\x00\x05\x00\x13\x00\r\x82\x06\xf7\x1fQ\n\xe80\x07mt\x0b\xe8\xce\xe9\x8b\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0c\x00\\PIPE\\atsvc\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccH\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\r\x82\x06\xf7\x1fQ\n\xe80\x07mt\x0b\xe8\xce\xe9\x8b\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00senssvc\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r\x82\x06\xf7\x1fQ\n\xe80\x07mt\x0b\xe8\xce\xe9\x8b\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE7F700A20D38041EEBE253CC2C3D8\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\r\x82\x06\xf7\x1fQ\n\xe80\x07mt\x0b\xe8\xce\xe9\x8b\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xccH\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\r\x1c\xeft\n\xa4A\x06N\x83\xae\xdct\xfb\x1c\xddS\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00senssvc\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r\x1c\xeft\n\xa4A\x06N\x83\xae\xdct\xfb\x1c\xddS\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE7F700A20D38041EEBE253CC2C3D8\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\r\x1c\xeft\n\xa4A\x06N\x83\xae\xdct\xfb\x1c\xddS\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xccH\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\r\xb5m\xac\xc9\xb7\x82UN\xae\x8a\xe4d\xed{Bw\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00senssvc\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r\xb5m\xac\xc9\xb7\x82UN\xae\x8a\xe4d\xed{Bw\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE7F700A20D38041EEBE253CC2C3D8\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\r\xb5m\xac\xc9\xb7\x82UN\xae\x8a\xe4d\xed{Bw\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xcc`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r>\x8e\xb0.\x9fc\xbaO\x97\xb1\x14\xf8x\x96\x10v\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE7F700A20D38041EEBE253CC2C3D8\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\r>\x8e\xb0.\x9fc\xbaO\x97\xb1\x14\xf8x\x96\x10v\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xcc`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r\xb5m\xac\xc9\xb7\x82UN\xae\x8a\xe4d\xed{Bw\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE7F700A20D38041EEBE253CC2C3D8\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\r\xb5m\xac\xc9\xb7\x82UN\xae\x8a\xe4d\xed{Bw\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xccN\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\r\xb5m\xac\xc9\xb7\x82UN\xae\x8a\xe4d\xed{Bw\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00IUserProfile2\x00\xcc\xccd\x00\x00\x00d\x00\x00\x00\x05\x00\x13\x00\rM\xdds4\x88.\x06@\x9c\xba"W\t\t\xdd\x10\x05\x00\x02\x00\x01\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x12\x00\\PIPE\\W32TIME_ALT\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00L\x00\x00\x00L\x00\x00\x00\x04\x00\x13\x00\rM\xdds4\x88.\x06@\x9c\xba"W\t\t\xdd\x10\x05\x00\x02\x00\x01\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0c\x00W32TIME_ALT\x00X\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\rM\xdds4\x88.\x06@\x9c\xba"W\t\t\xdd\x10\x05\x00\x02\x00\x01\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-e096b877a0c1c7e4dc\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\rM\xdds4\x88.\x06@\x9c\xba"W\t\t\xdd\x10\x05\x00\x02\x00\x01\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE59206968EBA94EAC82860D7A65BE\x00X\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r\xcf\x0b\xa7~\xafHjO\x89hjD\x07T\xd5\xfa\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-e096b877a0c1c7e4dc\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r\xcf\x0b\xa7~\xafHjO\x89hjD\x07T\xd5\xfa\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE59206968EBA94EAC82860D7A65BE\x00X\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r\x82&\xb9/\x99e\xdcB\xae\x13\xbd,\xa8\x9b\xd1\x1c\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-529ca01a24709db950\x00X\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r\xbf\x11\x9d\x7f\xb9\x7fkC\xa8\x12\xb2\xd5\x0c]L\x03\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-529ca01a24709db950\x00X\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r%\x04I\xdd%SeE\xb7t~\'\xd6\xc0\x9c$\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-529ca01a24709db950\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x06\x01\x00\t\x04\x00\x00\x00\x00\x00\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x1f\x02\x00\xc0\x05\x01\x00\t\x04\x00\x00\x00\x00\x00\xccI\x00\x00\x00I\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\t\x00NTDS_LPC\x00\xcc\xcc\xcc`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLEA42FB87E2EF04FE2895FA42C2387\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x03\x01\x00\t\x04\x00\x00\x00\x00\x00\xccJ\x00\x00\x00J\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\n\x00samss lpc\x00\xcc\xccG\x00\x00\x00G\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x07\x00dsrole\x00\xccj\x00\x00\x00j\x00\x00\x00\x05\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x18\x00\\PIPE\\protected_storage\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccR\x00\x00\x00R\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x12\x00protected_storage\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0b\x00lsasspirpc\x00\xccP\x00\x00\x00P\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x10\x00lsapolicylookup\x00P\x00\x00\x00P\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x10\x00LSARPC_ENDPOINT\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00securityevent\x00\xcc\xccF\x00\x00\x00F\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x06\x00audit\x00\xcc\xccX\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-75fac2f88290daf44c\x00^\x00\x00\x00^\x00\x00\x00\x05\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\xcf\xfb\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0c\x00\\pipe\\lsass\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x06\x01\x00\t\x04\x00\x00\x00\x00\x00\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x1f\x02\x00\xc0\x05\x01\x00\t\x04\x00\x00\x00\x00\x00\xccI\x00\x00\x00I\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\t\x00NTDS_LPC\x00\xcc\xcc\xcc`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLEA42FB87E2EF04FE2895FA42C2387\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x03\x01\x00\t\x04\x00\x00\x00\x00\x00\xccJ\x00\x00\x00J\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\n\x00samss lpc\x00\xcc\xccG\x00\x00\x00G\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x07\x00dsrole\x00\xccj\x00\x00\x00j\x00\x00\x00\x05\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x18\x00\\PIPE\\protected_storage\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccR\x00\x00\x00R\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x12\x00protected_storage\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0b\x00lsasspirpc\x00\xccP\x00\x00\x00P\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x10\x00lsapolicylookup\x00P\x00\x00\x00P\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x10\x00LSARPC_ENDPOINT\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00securityevent\x00\xcc\xccF\x00\x00\x00F\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x06\x00audit\x00\xcc\xccX\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-75fac2f88290daf44c\x00^\x00\x00\x00^\x00\x00\x00\x05\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0c\x00\\pipe\\lsass\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x1f\x02\x00\xc0\x05\x01\x00\t\x04\x00\x00\x00\x00\x00\xccI\x00\x00\x00I\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\t\x00NTDS_LPC\x00\xcc\xcc\xcc`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLEA42FB87E2EF04FE2895FA42C2387\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x03\x01\x00\t\x04\x00\x00\x00\x00\x00\xccJ\x00\x00\x00J\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\n\x00samss lpc\x00\xcc\xccG\x00\x00\x00G\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x07\x00dsrole\x00\xccj\x00\x00\x00j\x00\x00\x00\x05\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x18\x00\\PIPE\\protected_storage\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccR\x00\x00\x00R\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x12\x00protected_storage\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0b\x00lsasspirpc\x00\xccP\x00\x00\x00P\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x10\x00lsapolicylookup\x00P\x00\x00\x00P\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x10\x00LSARPC_ENDPOINT\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00securityevent\x00\xcc\xccF\x00\x00\x00F\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x06\x00audit\x00\xcc\xccX\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-75fac2f88290daf44c\x00^\x00\x00\x00^\x00\x00\x00\x05\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x00\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0c\x00\\pipe\\lsass\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x1f\x02\x00\xc0\x05\x01\x00\t\x04\x00\x00\x00\x00\x00\xccI\x00\x00\x00I\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\t\x00NTDS_LPC\x00\xcc\xcc\xcc`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLEA42FB87E2EF04FE2895FA42C2387\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x03\x01\x00\t\x04\x00\x00\x00\x00\x00\xccJ\x00\x00\x00J\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\n\x00samss lpc\x00\xcc\xccG\x00\x00\x00G\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x07\x00dsrole\x00\xccj\x00\x00\x00j\x00\x00\x00\x05\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x18\x00\\PIPE\\protected_storage\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccR\x00\x00\x00R\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x12\x00protected_storage\x00\xcc\xccK\x00\x00\x00K\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0b\x00lsasspirpc\x00\xccP\x00\x00\x00P\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x10\x00lsapolicylookup\x00P\x00\x00\x00P\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x10\x00LSARPC_ENDPOINT\x00N\x00\x00\x00N\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x0e\x00securityevent\x00\xcc\xccF\x00\x00\x00F\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x06\x00audit\x00\xcc\xccX\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-75fac2f88290daf44c\x00^\x00\x00\x00^\x00\x00\x00\x05\x00\x13\x00\r5BQ\xe3\x06K\xd1\x11\xab\x04\x00\xc0O\xc2\xdc\xd2\x04\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0c\x00\\pipe\\lsass\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xccH\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\ra&EJ\x90\x826K\x8f\xbe\x7f@\x93\xa9Ix\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00spoolss\x00H\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\r\x9b\x063\xae\xa8\xa2\xeeF\xa25\xdd\xfd3\x9b\xe2\x81\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00spoolss\x00H\x00\x00\x00H\x00\x00\x00\x04\x00\x13\x00\r\xfa\xdbn\x0b$J\xc6O\x8a#\x94+\x1e\xcae\xd1\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x08\x00spoolss\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r\xa4\xc2\xabPMW\xb3@\x9df\xeeO\xd5\xfb\xa0v\x05\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0\x15\x01\x00\t\x04\x00\x00\x00\x00\x00\xcca\x00\x00\x00a\x00\x00\x00\x05\x00\x13\x00\r\xbf\t\x11\x81\xe1\xa4\xd1\x11\xabT\x00\xa0\xc9\x1e\x9bE\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0f\x00\\pipe\\WinsPipe\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xcc\xccX\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r\xbf\t\x11\x81\xe1\xa4\xd1\x11\xabT\x00\xa0\xc9\x1e\x9bE\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-1d5ca5ac42312a0056\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r\xbf\t\x11\x81\xe1\xa4\xd1\x11\xabT\x00\xa0\xc9\x1e\x9bE\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0,\x01\x00\t\x04\x00\x00\x00\x00\x00\xcc`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r\xbf\t\x11\x81\xe1\xa4\xd1\x11\xabT\x00\xa0\xc9\x1e\x9bE\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE261F2C99BFF143DE95CFD25D6F1B\x00a\x00\x00\x00a\x00\x00\x00\x05\x00\x13\x00\r(,\xf5E\x9f\x7f\x1a\x10\xb5+\x08\x00+.\xfa\xbe\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x0f\x0f\x00\\pipe\\WinsPipe\x00\x01\x00\x11\r\x00\\\\FREEFLY-DC\x00\xcc\xcc\xccX\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r(,\xf5E\x9f\x7f\x1a\x10\xb5+\x08\x00+.\xfa\xbe\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-1d5ca5ac42312a0056\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r(,\xf5E\x9f\x7f\x1a\x10\xb5+\x08\x00+.\xfa\xbe\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0,\x01\x00\t\x04\x00\x00\x00\x00\x00\xcc`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r(,\xf5E\x9f\x7f\x1a\x10\xb5+\x08\x00+.\xfa\xbe\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE261F2C99BFF143DE95CFD25D6F1B\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r\x81\xbbz6D\x98\xf15\xad2\x98\xf08\x00\x10\x03\x02\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0K\x01\x00\t\x04\x00\x00\x00\x00\x00\xccX\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-47015c651701b6fefd\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rxV4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xab\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0L\x01\x00\t\x04\x00\x00\x00\x00\x00\xccK\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r\x1e\xdd[k\x8cR,B\xaf\x8c\xa4\x07\x9b\xe4\xfeH\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\xc0L\x01\x00\t\x04\x00\x00\x00\x00\x00\xccX\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r\xe0\x0ck\x90\x0b\xc7g\x10\xb3\x17\x00\xdd\x01\x06b\xda\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-9c0b57db25a3353f68\x00`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r\xe0\x0ck\x90\x0b\xc7g\x10\xb3\x17\x00\xdd\x01\x06b\xda\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLEC7D101604F874C58BA48EAD7B5A2\x00X\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r\xe0\x0ck\x90\x0b\xc7g\x10\xb3\x17\x00\xdd\x01\x06b\xda\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-6d64ace2cb67ac5179\x00X\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r\xe0\x0ck\x90\x0b\xc7g\x10\xb3\x17\x00\xdd\x01\x06b\xda\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-6d64ace2cb67ac5179\x00X\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r\xe0\x0ck\x90\x0b\xc7g\x10\xb3\x17\x00\xdd\x01\x06b\xda\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-6d64ace2cb67ac5179\x00X\x00\x00\x00X\x00\x00\x00\x04\x00\x13\x00\r\xe0\x0ck\x90\x0b\xc7g\x10\xb3\x17\x00\xdd\x01\x06b\xda\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10\x18\x00LRPC-6d64ace2cb67ac5179\x00K\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\r_.~\x89\xf3\x93vC\x9c\x9c\xfd"wI\\\'\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\x16Z\x01\x00\t\x04\x00\x00\x00\x00\x00\xcc`\x00\x00\x00`\x00\x00\x00\x04\x00\x13\x00\r_.~\x89\xf3\x93vC\x9c\x9c\xfd"wI\\\'\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0c\x02\x00\x00\x00\x01\x00\x10 \x00OLE4B669A0A60C84C56926EB66DC651\x00\x00\x00\x00\x00'
        hexdump(ept_lookup_resp)
        lookupResponse= ept_lookupResponse(isNDR64=False)
        lookupResponse.fromString(ept_lookup_resp)
        lookupResponse.dumpRaw()
        output = lookupResponse.getData()
        print("ORIG: %d, REPACKED: %d" % (len(ept_lookup_resp), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(ept_lookup_resp)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(len(ept_lookup_resp), len(output))

    def test_12(self):
        ept_mapReq = b'\x87d\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x001j\x00\x00\x00\x00\x00\x00K\x00\x00\x00\x00\x00\x00\x00K\x00\x00\x00\x05\x00\x13\x00\rxW4\x124\x12\xcd\xab\xef\x00\x01#Eg\x89\xac\x01\x00\x02\x00\x00\x00\x13\x00\r\x04]\x88\x8a\xeb\x1c\xc9\x11\x9f\xe8\x08\x00+\x10H`\x02\x00\x02\x00\x00\x00\x01\x00\x0b\x02\x00\x00\x00\x01\x00\x07\x02\x00\x00\x00\x01\x00\t\x04\x00\x00\x00\x00\x00\xaa\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x04\x00\x00\x00'
        hexdump(ept_mapReq)

        tower = epm.EPMTower()
        interface = epm.EPMRPCInterface()
        interface['InterfaceUUID'] = string_to_bin('12345778-1234-ABCD-EF00-0123456789AC')
        interface['MajorVersion'] = 1
        interface['MinorVersion'] = 0

        dataRep = epm.EPMRPCDataRepresentation()
        dataRep['DataRepUuid'] = string_to_bin('8a885d04-1ceb-11c9-9fe8-08002b104860')
        dataRep['MajorVersion'] = 2
        dataRep['MinorVersion'] = 0

        protId = epm.EPMProtocolIdentifier()
        protId['ProtIdentifier'] = 0xb

        pipeName = epm.EPMPipeName()
        pipeName['PipeName'] = '\x00'

        portAddr = epm.EPMPortAddr()
        portAddr['IpPort'] = 0

        hostAddr = epm.EPMHostAddr()
        import socket
        hostAddr['Ip4addr'] = socket.inet_aton('0.0.0.0')

        hostName = epm.EPMHostName()
        hostName['HostName'] = '\x00'

        tower['NumberOfFloors'] = 5
        tower['Floors'] = interface.getData() + dataRep.getData() + protId.getData() + portAddr.getData() + hostAddr.getData()
        request = epm.ept_map(isNDR64=True)
        request['max_towers'] = 4
        request['map_tower']['tower_length'] = len(tower)
        request['map_tower']['tower_octet_string'] = tower.getData()

        output = request.getData()
        print("ORIG: %d, REPACKED: %d" % (len(ept_mapReq), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(ept_mapReq)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(len(ept_mapReq), len(output))

    def test_13(self):
        baseRegGetKeySecurityResponse = b'\x00\x00\x02\x00\x00\x04\x00\x00$\x00\x00\x00\x00\x04\x00\x00\x00\x00\x00\x00$\x00\x00\x00\x01\x00\x00\x80\x14\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x01\x02\x00\x00\x00\x00\x00\x05 \x00\x00\x00 \x02\x00\x00\x00\x00\x00\x00'
        hexdump(baseRegGetKeySecurityResponse)
        baseRegGetKeySecurity= BaseRegGetKeySecurityResponse()
        baseRegGetKeySecurity.fromString(baseRegGetKeySecurityResponse)
        baseRegGetKeySecurity.dumpRaw()
        output = baseRegGetKeySecurity.getData()
        print("ORIG: %d, REPACKED: %d" % (len(baseRegGetKeySecurityResponse), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(baseRegGetKeySecurityResponse)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(baseRegGetKeySecurityResponse, output)

    def test_14(self):
        samrLookupIdsInDomain = b'\x00\x00\x00\x00Bz\x94j&\\:E\xacS\xae\xa9c\xa8\xc5\xfb\x02\x00\x00\x00\xe8\x03\x00\x00\x00\x00\x00\x00\x02\x00\x00\x00\xf4\x01\x00\x00\xf5\x01\x00\x00'
        hexdump(samrLookupIdsInDomain)

        request = SamrLookupIdsInDomain()
        request['DomainHandle'] = 'A'*20
        request['Count'] = 2
        entry = dtypes.ULONG()
        entry['Data'] = 500
        request['RelativeIds'].append(entry)
        entry = dtypes.ULONG()
        entry['Data'] = 501
        request['RelativeIds'].append(entry)
        request.fields['RelativeIds'].fields['MaximumCount'] = 1000
        #request.dumpRaw()

        output = request.getData()
        print("ORIG: %d, REPACKED: %d" % (len(samrLookupIdsInDomain), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(samrLookupIdsInDomain)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(len(samrLookupIdsInDomain), len(output))

    def test_15(self):
        baseRegQueryMultipleValues = b'\x00\x00\x00\x00Ah?\x10^>GG\xbco\xa1\xc4(\x86\xbcR\xbf\xbf\xbf\xbf\x03\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x03\x00\x00\x00\x00\x00\x00\x00\xfan\x00\x00\x00\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\xdd\xdd\xdd\xddk\x86\x00\x00\x00\x00\x00\x00\x0b\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\xdd\xdd\xdd\xdd\xe3i\x00\x00\x00\x00\x00\x00\n\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\xcc\xcc\xcc\xcc\x18\x00\x18\x00\xbc\xbc\xbc\xbc/:\x00\x00\x00\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x0c\x00\x00\x00\x00\x00\x00\x00P\x00r\x00o\x00d\x00u\x00c\x00t\x00N\x00a\x00m\x00e\x00\x00\x00\x16\x00\x16\x00\xbc\xbc\xbc\xbc\x0c-\x00\x00\x00\x00\x00\x00\x0b\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x0b\x00\x00\x00\x00\x00\x00\x00S\x00y\x00s\x00t\x00e\x00m\x00R\x00o\x00o\x00t\x00\x00\x00\xcc\xcc\x14\x00\x14\x00\xbc\xbc\xbc\xbci\xab\x00\x00\x00\x00\x00\x00\n\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\n\x00\x00\x00\x00\x00\x00\x00E\x00d\x00i\x00t\x00i\x00o\x00n\x00I\x00D\x00\x00\x00\x03\x00\x00\x00?\x8b\x00\x00\x00\x00\x00\x00\x80\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x80\x00\x00\x00\x00\x00\x00\x00                                                                                                                                \x80\x00\x00\x00'
        hexdump(baseRegQueryMultipleValues)

        request = BaseRegQueryMultipleValues()

        item1 = RVALENT()
        item1['ve_valuename'] = 'ProductName\x00'
        item1['ve_valuelen'] = len('ProductName\x00')
        item1['ve_valueptr'] = NULL
        item1['ve_type'] = REG_SZ

        item2 = RVALENT()
        item2['ve_valuename'] = 'SystemRoot\x00'
        item2['ve_valuelen'] = len('SystemRoot\x00')
        item1['ve_valueptr'] = NULL
        item2['ve_type'] = REG_SZ

        item3 = RVALENT()
        item3['ve_valuename'] = 'EditionID\x00'
        item3['ve_valuelen'] = len('EditionID\x00')
        item3['ve_valueptr'] = NULL
        item3['ve_type'] = REG_SZ

        #request['hKey'] = 'A*20'
        request['val_listIn'].append(item1)
        request['val_listIn'].append(item2)
        request['val_listIn'].append(item3)
        request['num_vals'] = len(request['val_listIn'])
        request['lpvalueBuf'] = list(' '*128)
        request['ldwTotsize'] = 128
        #request.dumpRaw()

        request.changeTransferSyntax(self.NDR64Syntax)
        request.fromString(baseRegQueryMultipleValues)
        output = request.getData()
        print("ORIG: %d, REPACKED: %d" % (len(baseRegQueryMultipleValues), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(baseRegQueryMultipleValues)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(len(baseRegQueryMultipleValues), len(output))

    def test_16(self):
        complexPing = b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x02\x00\x01\x00\xaa\xaa\x92\xeb\x00\x00\x02\x00\x00\x00\xce\xc9\x00\x89\xd1\xd2\xad\x0f\x0f\x9fW\xceN\xf5bN\xb0\x92\x00\x00\x01\x00\x00\x00\xce\xc9\x00\x89\xd1\xd2\xad\x0f'
        hexdump(complexPing)

        request = ComplexPing()
        request.fromString(complexPing)
        output = request.getData()
        print("ORIG: %d, REPACKED: %d" % (len(complexPing), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(complexPing)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(len(complexPing), len(output))

    def test_17(self):
        #<class 'impacket.dcerpc.v5.samr.SamrLookupNamesInDomainResponse'>
        baseRegQueryValueResponse = b'\x00\x00\x02\x00\x00\x00\x00\x00\x04\x00\x02\x00\x00\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\x00\x02\x00\x00\x02\x00\x00\x0c\x00\x02\x00\x00\x00\x00\x00\x02\x00\x00\x00'
        hexdump(baseRegQueryValueResponse)
        baseRegQueryValue= BaseRegQueryValueResponse()
        baseRegQueryValue.fromString(baseRegQueryValueResponse)
        baseRegQueryValue.dumpRaw()
        output = baseRegQueryValue.getData()
        print("ORIG: %d, REPACKED: %d" % (len(baseRegQueryValueResponse), len(output)))
        print("="*80)
        print("ORIG")
        hexdump(baseRegQueryValueResponse)
        print("="*80)
        print("REPACKED")
        hexdump(output)
        print("="*80)
        self.assertEqual(baseRegQueryValueResponse, output)


if __name__ == '__main__':
    unittest.main(verbosity=1)
