# content of: tox.ini, put in same dir as setup.py
[tox]
envlist = clean,py{36,37,38,39,310,311},report

[gh-actions]
python =
    3.6: py36
    3.7: py37
    3.8: py38
    3.9: py39
    3.10: py310
    3.11: py311

[testenv]
deps = -r requirements-test.txt
passenv = REMOTE_CONFIG
commands =
    {envpython} -m pip check
    pytest --cov --cov-append --cov-context=test --cov-config=tox.ini {posargs}
depends =
    py{36,37,38,39,310,311}: clean
    report: py{36,37,38,39,310,311}

[testenv:clean]
basepython = python3.8
deps = coverage
skip_install = true
commands =
    coverage erase

[testenv:report]
basepython = python3.8
deps = coverage
skip_install = true
commands =
    coverage report
    coverage html

[testenv:py311]
ignore_errors = true

[pytest]
markers =
    remote: marks tests as remote

[coverage:run]
branch = True
source = impacket
omit = *remcom*
       *.tox*

[coverage:report]
# Regexes for lines to exclude from consideration
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover

    # Don't complain about missing debug-only code:
    if self\.debug

    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError

    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:

ignore_errors = True

[coverage:html]
show_contexts = True
