#pragma once

//
// Common Header Includes
//
#include <ntstatus.h>
#include <windows.h>

//
// Internal "Beacon" API header
//
#include "beacon.h"

WINADVAPI LONG WINAPI ADVAPI32$RegOpenKeyExA (HKEY, LPCSTR, DWORD, REGSAM, PHKEY);
WIN<PERSON><PERSON><PERSON> LONG WINAPI ADVAPI32$RegCloseKey(HKEY);
WINADVAPI LONG WINAPI ADVAPI32$RegSaveKeyA (HKEY, LPCSTR, LPSECURITY_ATTRIBUTES);
WINBASEAPI BOOL WINAPI ADVAPI32$OpenProcessToken (HANDLE, DWORD, <PERSON><PERSON><PERSON>LE);
WINBASEAPI DWORD WINAPI KERNEL32$GetLastError (void);
WINBASEAPI BOOL WINAPI ADVAPI32$LookupPrivilegeValueA (LPCSTR, LPCSTR, PLUID);
WINBASEAPI BOOL WINAPI ADVAPI32$AdjustTokenPrivileges(<PERSON><PERSON><PERSON><PERSON>, BOOL, PTOKEN_PRIVILEGES, DWORD, PTOKEN_PRIVILEGES, PDWORD);
WINBASEAPI HANDLE WINAPI KERNEL32$GetCurrentProcess (void);
WINBASEAPI BOOL WINAPI KERNEL32$CloseHandle (HANDLE);
WINBASEAPI LPSTR WINAPI SHLWAPI$PathCombineA(LPSTR,LPCSTR,LPCSTR);
